/* Chinese (simplified) localization for jQuery UI */
(function(factory) {
    "use strict";
    
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["../widgets/datepicker"], factory);
    } else {
        // Browser globals
        factory(jQuery.datepicker);
    }
})(function(datepicker) {
    "use strict";
    
    datepicker.regional["zh-CN"] = {
        closeText: "关闭",
        prevText: "&#x3C;上月",
        nextText: "下月&#x3E;",
        currentText: "今天",
        monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
        monthNamesShort: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
        dayNames: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
        dayNamesShort: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
        dayNamesMin: ["日", "一", "二", "三", "四", "五", "六"],
        weekHeader: "周",
        dateFormat: "yy-mm-dd",
        firstDay: 1,
        isRTL: false,
        showMonthAfterYear: true,
        yearSuffix: "年"
    };
    datepicker.setDefaults(datepicker.regional["zh-CN"]);
    
    return datepicker.regional["zh-CN"];
});

/* Chinese (simplified) localization for jQuery UI Dialog, Autocomplete, etc. */
(function(factory) {
    "use strict";
    
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["jquery"], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
})(function($) {
    "use strict";
    
    $.ui = $.ui || {};
    
    $.extend($.ui, {
        locale: {
            "zh-CN": {
                /* Common */
                closeText: "关闭",
                
                /* Dialog */
                dialogTitle: "对话框",
                dialogCloseText: "关闭",
                dialogCancelText: "取消",
                dialogOkText: "确定",
                
                /* Autocomplete */
                autocompleteNoMatch: "没有匹配结果",
                autocompleteLoading: "正在加载...",
                
                /* Menu */
                menuCancelText: "取消",
                
                /* SelectMenu */
                selectMenuNoResultsFound: "没有找到结果",
                
                /* Datepicker */
                weekHeader: "周",
                
                /* Progressbar */
                progressbarText: "正在处理...",
                
                /* Tooltip */
                tooltipCloseText: "关闭",
                
                /* Slider */
                sliderMinText: "最小",
                sliderMaxText: "最大"
            }
        }
    });
    
    // Set default locale
    $.ui.locale.current = "zh-CN";
    
    // Helper function to get localized text
    $.ui.getLocaleText = function(key) {
        var locale = $.ui.locale.current;
        var texts = $.ui.locale[locale];
        return texts && texts[key] ? texts[key] : key;
    };
});
