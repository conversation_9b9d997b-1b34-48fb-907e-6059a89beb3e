{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'toast'\nconst VERSION            = '4.3.1'\nconst DATA_KEY           = 'bs.toast'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Event = {\n  CLICK_DISMISS : `click.dismiss${EVENT_KEY}`,\n  HIDE          : `hide${EVENT_KEY}`,\n  HIDDEN        : `hidden${EVENT_KEY}`,\n  SHOW          : `show${EVENT_KEY}`,\n  SHOWN         : `shown${EVENT_KEY}`\n}\n\nconst ClassName = {\n  FADE    : 'fade',\n  HIDE    : 'hide',\n  SHOW    : 'show',\n  SHOWING : 'showing'\n}\n\nconst DefaultType = {\n  animation : 'boolean',\n  autohide  : 'boolean',\n  delay     : 'number'\n}\n\nconst Default = {\n  animation : true,\n  autohide  : true,\n  delay     : 500\n}\n\nconst Selector = {\n  DATA_DISMISS : '[data-dismiss=\"toast\"]'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config  = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    $(this._element).trigger(Event.SHOW)\n\n    if (this._config.animation) {\n      this._element.classList.add(ClassName.FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(ClassName.SHOWING)\n      this._element.classList.add(ClassName.SHOW)\n\n      $(this._element).trigger(Event.SHOWN)\n\n      if (this._config.autohide) {\n        this.hide()\n      }\n    }\n\n    this._element.classList.remove(ClassName.HIDE)\n    this._element.classList.add(ClassName.SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide(withoutTimeout) {\n    if (!this._element.classList.contains(ClassName.SHOW)) {\n      return\n    }\n\n    $(this._element).trigger(Event.HIDE)\n\n    if (withoutTimeout) {\n      this._close()\n    } else {\n      this._timeout = setTimeout(() => {\n        this._close()\n      }, this._config.delay)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(ClassName.SHOW)) {\n      this._element.classList.remove(ClassName.SHOW)\n    }\n\n    $(this._element).off(Event.CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config  = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      () => this.hide(true)\n    )\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(ClassName.HIDE)\n      $(this._element).trigger(Event.HIDDEN)\n    }\n\n    this._element.classList.remove(ClassName.SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data       = $element.data(DATA_KEY)\n      const _config  = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME]             = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict  = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "Event", "CLICK_DISMISS", "HIDE", "HIDDEN", "SHOW", "SHOWN", "ClassName", "FADE", "SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Selector", "DATA_DISMISS", "Toast", "element", "config", "_element", "_config", "_getConfig", "_timeout", "_setListeners", "show", "trigger", "classList", "add", "complete", "remove", "hide", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "withoutTimeout", "contains", "_close", "setTimeout", "dispose", "clearTimeout", "off", "removeData", "data", "typeCheckConfig", "constructor", "on", "_jQueryInterface", "each", "$element", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,OAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,UAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B;EAEA,IAAMO,KAAK,GAAG;EACZC,EAAAA,aAAa,oBAAmBL,SADpB;EAEZM,EAAAA,IAAI,WAAmBN,SAFX;EAGZO,EAAAA,MAAM,aAAmBP,SAHb;EAIZQ,EAAAA,IAAI,WAAmBR,SAJX;EAKZS,EAAAA,KAAK,YAAmBT;EALZ,CAAd;EAQA,IAAMU,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAM,MADM;EAEhBL,EAAAA,IAAI,EAAM,MAFM;EAGhBE,EAAAA,IAAI,EAAM,MAHM;EAIhBI,EAAAA,OAAO,EAAG;EAJM,CAAlB;EAOA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAG,SADM;EAElBC,EAAAA,QAAQ,EAAI,SAFM;EAGlBC,EAAAA,KAAK,EAAO;EAHM,CAApB;EAMA,IAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAG,IADE;EAEdC,EAAAA,QAAQ,EAAI,IAFE;EAGdC,EAAAA,KAAK,EAAO;EAHE,CAAhB;EAMA,IAAME,QAAQ,GAAG;EACfC,EAAAA,YAAY,EAAG;EAGjB;;;;;;EAJiB,CAAjB;;MAUMC;;;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAgBF,OAAhB;EACA,SAAKG,OAAL,GAAgB,KAAKC,UAAL,CAAgBH,MAAhB,CAAhB;EACA,SAAKI,QAAL,GAAgB,IAAhB;;EACA,SAAKC,aAAL;EACD;;;;;EAgBD;WAEAC,OAAA,gBAAO;EAAA;;EACL1B,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBM,OAAjB,CAAyBzB,KAAK,CAACI,IAA/B;;EAEA,QAAI,KAAKgB,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,WAAKS,QAAL,CAAcO,SAAd,CAAwBC,GAAxB,CAA4BrB,SAAS,CAACC,IAAtC;EACD;;EAED,QAAMqB,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACT,QAAL,CAAcO,SAAd,CAAwBG,MAAxB,CAA+BvB,SAAS,CAACE,OAAzC;;EACA,MAAA,KAAI,CAACW,QAAL,CAAcO,SAAd,CAAwBC,GAAxB,CAA4BrB,SAAS,CAACF,IAAtC;;EAEAN,MAAAA,CAAC,CAAC,KAAI,CAACqB,QAAN,CAAD,CAAiBM,OAAjB,CAAyBzB,KAAK,CAACK,KAA/B;;EAEA,UAAI,KAAI,CAACe,OAAL,CAAaT,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACmB,IAAL;EACD;EACF,KATD;;EAWA,SAAKX,QAAL,CAAcO,SAAd,CAAwBG,MAAxB,CAA+BvB,SAAS,CAACJ,IAAzC;;EACA,SAAKiB,QAAL,CAAcO,SAAd,CAAwBC,GAAxB,CAA4BrB,SAAS,CAACE,OAAtC;;EACA,QAAI,KAAKY,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,UAAMqB,kBAAkB,GAAGC,IAAI,CAACC,gCAAL,CAAsC,KAAKd,QAA3C,CAA3B;EAEArB,MAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CACGe,GADH,CACOF,IAAI,CAACG,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACLH,MAAAA,QAAQ;EACT;EACF;;WAEDE,OAAA,cAAKO,cAAL,EAAqB;EAAA;;EACnB,QAAI,CAAC,KAAKlB,QAAL,CAAcO,SAAd,CAAwBY,QAAxB,CAAiChC,SAAS,CAACF,IAA3C,CAAL,EAAuD;EACrD;EACD;;EAEDN,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBM,OAAjB,CAAyBzB,KAAK,CAACE,IAA/B;;EAEA,QAAImC,cAAJ,EAAoB;EAClB,WAAKE,MAAL;EACD,KAFD,MAEO;EACL,WAAKjB,QAAL,GAAgBkB,UAAU,CAAC,YAAM;EAC/B,QAAA,MAAI,CAACD,MAAL;EACD,OAFyB,EAEvB,KAAKnB,OAAL,CAAaR,KAFU,CAA1B;EAGD;EACF;;WAED6B,UAAA,mBAAU;EACRC,IAAAA,YAAY,CAAC,KAAKpB,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;;EAEA,QAAI,KAAKH,QAAL,CAAcO,SAAd,CAAwBY,QAAxB,CAAiChC,SAAS,CAACF,IAA3C,CAAJ,EAAsD;EACpD,WAAKe,QAAL,CAAcO,SAAd,CAAwBG,MAAxB,CAA+BvB,SAAS,CAACF,IAAzC;EACD;;EAEDN,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBwB,GAAjB,CAAqB3C,KAAK,CAACC,aAA3B;EAEAH,IAAAA,CAAC,CAAC8C,UAAF,CAAa,KAAKzB,QAAlB,EAA4BxB,QAA5B;EACA,SAAKwB,QAAL,GAAgB,IAAhB;EACA,SAAKC,OAAL,GAAgB,IAAhB;EACD;;;WAIDC,aAAA,oBAAWH,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qBACDL,OADC,EAEDf,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiB0B,IAAjB,EAFC,EAGD,OAAO3B,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;EAMAc,IAAAA,IAAI,CAACc,eAAL,CACErD,IADF,EAEEyB,MAFF,EAGE,KAAK6B,WAAL,CAAiBtC,WAHnB;EAMA,WAAOS,MAAP;EACD;;WAEDK,gBAAA,yBAAgB;EAAA;;EACdzB,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiB6B,EAAjB,CACEhD,KAAK,CAACC,aADR,EAEEa,QAAQ,CAACC,YAFX,EAGE;EAAA,aAAM,MAAI,CAACe,IAAL,CAAU,IAAV,CAAN;EAAA,KAHF;EAKD;;WAEDS,SAAA,kBAAS;EAAA;;EACP,QAAMX,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACT,QAAL,CAAcO,SAAd,CAAwBC,GAAxB,CAA4BrB,SAAS,CAACJ,IAAtC;;EACAJ,MAAAA,CAAC,CAAC,MAAI,CAACqB,QAAN,CAAD,CAAiBM,OAAjB,CAAyBzB,KAAK,CAACG,MAA/B;EACD,KAHD;;EAKA,SAAKgB,QAAL,CAAcO,SAAd,CAAwBG,MAAxB,CAA+BvB,SAAS,CAACF,IAAzC;;EACA,QAAI,KAAKgB,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,UAAMqB,kBAAkB,GAAGC,IAAI,CAACC,gCAAL,CAAsC,KAAKd,QAA3C,CAA3B;EAEArB,MAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CACGe,GADH,CACOF,IAAI,CAACG,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACLH,MAAAA,QAAQ;EACT;EACF;;;UAIMqB,mBAAP,0BAAwB/B,MAAxB,EAAgC;EAC9B,WAAO,KAAKgC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAGrD,CAAC,CAAC,IAAD,CAAlB;EACA,UAAI+C,IAAI,GAASM,QAAQ,CAACN,IAAT,CAAclD,QAAd,CAAjB;;EACA,UAAMyB,OAAO,GAAI,OAAOF,MAAP,KAAkB,QAAlB,IAA8BA,MAA/C;;EAEA,UAAI,CAAC2B,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI7B,KAAJ,CAAU,IAAV,EAAgBI,OAAhB,CAAP;EACA+B,QAAAA,QAAQ,CAACN,IAAT,CAAclD,QAAd,EAAwBkD,IAAxB;EACD;;EAED,UAAI,OAAO3B,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2B,IAAI,CAAC3B,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIkC,SAAJ,wBAAkClC,MAAlC,QAAN;EACD;;EAED2B,QAAAA,IAAI,CAAC3B,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAjBM,CAAP;EAkBD;;;;0BA7IoB;EACnB,aAAOxB,OAAP;EACD;;;0BAEwB;EACvB,aAAOe,WAAP;EACD;;;0BAEoB;EACnB,aAAOI,OAAP;EACD;;;;;EAsIH;;;;;;;EAMAf,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAyBuB,KAAK,CAACiC,gBAA/B;EACAnD,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAW4D,WAAX,GAAyBrC,KAAzB;;EACAlB,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAW6D,UAAX,GAAyB,YAAM;EAC7BxD,EAAAA,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAOmB,KAAK,CAACiC,gBAAb;EACD,CAHD;;;;;;;;"}