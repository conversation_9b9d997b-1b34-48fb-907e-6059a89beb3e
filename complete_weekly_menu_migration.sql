-- 完整的周菜单系统迁移脚本
-- 删除旧表，创建新表，更新相关表结构
USE [StudentsCMSSP]
GO

PRINT '========================================='
PRINT '开始完整的周菜单系统迁移...'
PRINT '========================================='
PRINT ''

-- 第一步：检查当前状态
PRINT '第一步：检查当前数据库状态'
PRINT '-----------------------------------------'

-- 检查需要删除的旧表
DECLARE @tables_to_drop TABLE (table_name NVARCHAR(128))
INSERT INTO @tables_to_drop VALUES 
    ('menu_plans'),
    ('weekly_menu_templates'), 
    ('daily_menus'),
    ('weekly_menu_recipes_temp')

DECLARE @table_name NVARCHAR(128)
DECLARE table_cursor CURSOR FOR SELECT table_name FROM @tables_to_drop

OPEN table_cursor
FETCH NEXT FROM table_cursor INTO @table_name

WHILE @@FETCH_STATUS = 0
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = @table_name)
    BEGIN
        PRINT '⚠️ 发现旧表: ' + @table_name + ' (将被删除)'
    END
    ELSE
    BEGIN
        PRINT '✓ 旧表已清理: ' + @table_name
    END
    
    FETCH NEXT FROM table_cursor INTO @table_name
END

CLOSE table_cursor
DEALLOCATE table_cursor

-- 检查需要保留的表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
    PRINT '✓ weekly_menus 表存在'
ELSE
    PRINT '❌ weekly_menus 表不存在 (将被创建)'

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menu_recipes')
    PRINT '✓ weekly_menu_recipes 表存在'
ELSE
    PRINT '❌ weekly_menu_recipes 表不存在 (将被创建)'

PRINT ''

-- 第二步：删除旧表和相关约束
PRINT '第二步：清理旧表结构'
PRINT '-----------------------------------------'

-- 删除可能存在的外键约束
PRINT '删除相关外键约束...'

-- 删除 consumption_plans 中可能的 menu_plan_id 外键
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE parent_object_id = OBJECT_ID('consumption_plans') AND name LIKE '%menu_plan%')
BEGIN
    DECLARE @fk_name NVARCHAR(128)
    SELECT @fk_name = name FROM sys.foreign_keys 
    WHERE parent_object_id = OBJECT_ID('consumption_plans') AND name LIKE '%menu_plan%'
    
    EXEC('ALTER TABLE consumption_plans DROP CONSTRAINT ' + @fk_name)
    PRINT '✓ 删除 consumption_plans 的 menu_plan 外键约束'
END

-- 删除旧表
DECLARE drop_cursor CURSOR FOR SELECT table_name FROM @tables_to_drop
OPEN drop_cursor
FETCH NEXT FROM drop_cursor INTO @table_name

WHILE @@FETCH_STATUS = 0
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = @table_name)
    BEGIN
        EXEC('DROP TABLE ' + @table_name)
        PRINT '✓ 删除旧表: ' + @table_name
    END
    
    FETCH NEXT FROM drop_cursor INTO @table_name
END

CLOSE drop_cursor
DEALLOCATE drop_cursor

PRINT ''

-- 第三步：创建新的周菜单表结构
PRINT '第三步：创建新的周菜单表结构'
PRINT '-----------------------------------------'

-- 创建 weekly_menus 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
BEGIN
    CREATE TABLE [dbo].[weekly_menus](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [week_start] [date] NOT NULL,
        [week_end] [date] NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '计划中',
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menus] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menus_area_id] FOREIGN KEY([area_id]) 
            REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_weekly_menus_created_by] FOREIGN KEY([created_by]) 
            REFERENCES [dbo].[users] ([id])
    )
    PRINT '✓ 创建 weekly_menus 表'
END
ELSE
BEGIN
    PRINT '✓ weekly_menus 表已存在'
END

-- 创建 weekly_menu_recipes 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menu_recipes')
BEGIN
    CREATE TABLE [dbo].[weekly_menu_recipes](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [weekly_menu_id] [int] NOT NULL,
        [day_of_week] [int] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [recipe_id] [int] NULL,
        [recipe_name] [nvarchar](100) NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menu_recipes] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menu_recipes_weekly_menu_id] FOREIGN KEY([weekly_menu_id]) 
            REFERENCES [dbo].[weekly_menus] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_weekly_menu_recipes_recipe_id] FOREIGN KEY([recipe_id]) 
            REFERENCES [dbo].[recipes] ([id])
    )
    PRINT '✓ 创建 weekly_menu_recipes 表'
END
ELSE
BEGIN
    PRINT '✓ weekly_menu_recipes 表已存在'
END

PRINT ''

-- 第四步：更新 consumption_plans 表
PRINT '第四步：更新 consumption_plans 表结构'
PRINT '-----------------------------------------'

-- 删除 menu_plan_id 字段（如果存在）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
BEGIN
    ALTER TABLE consumption_plans DROP COLUMN menu_plan_id
    PRINT '✓ 删除 consumption_plans.menu_plan_id 字段'
END

-- 确保 area_id 字段存在
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
BEGIN
    ALTER TABLE consumption_plans ADD area_id INT NOT NULL DEFAULT 1
    ALTER TABLE consumption_plans ADD CONSTRAINT FK_consumption_plans_area_id 
    FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
    PRINT '✓ 添加 consumption_plans.area_id 字段'
END
ELSE
BEGIN
    PRINT '✓ consumption_plans.area_id 字段已存在'
END

PRINT ''

-- 第五步：创建优化索引
PRINT '第五步：创建性能优化索引'
PRINT '-----------------------------------------'

-- weekly_menus 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_optimized')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_area_week_optimized]
    ON [dbo].[weekly_menus] ([area_id], [week_start])
    INCLUDE ([id], [week_end], [status], [created_by], [created_at], [updated_at])
    PRINT '✓ 创建 weekly_menus 复合优化索引'
END

-- weekly_menu_recipes 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_menu_day_meal')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_menu_day_meal]
    ON [dbo].[weekly_menu_recipes] ([weekly_menu_id], [day_of_week], [meal_type])
    INCLUDE ([recipe_id], [recipe_name])
    PRINT '✓ 创建 weekly_menu_recipes 复合优化索引'
END

-- consumption_plans 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'IX_consumption_plans_area_date_meal')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_consumption_plans_area_date_meal]
    ON [dbo].[consumption_plans] ([area_id], [consumption_date], [meal_type])
    INCLUDE ([status], [diners_count])
    PRINT '✓ 创建 consumption_plans 复合优化索引'
END

PRINT ''

-- 第六步：更新统计信息
PRINT '第六步：更新统计信息'
PRINT '-----------------------------------------'
UPDATE STATISTICS weekly_menus
UPDATE STATISTICS weekly_menu_recipes  
UPDATE STATISTICS consumption_plans
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '========================================='
PRINT '🎉 周菜单系统迁移完成！'
PRINT '========================================='
PRINT ''
PRINT '迁移总结：'
PRINT '• 删除了所有旧的菜单相关表'
PRINT '• 创建了新的 weekly_menus 和 weekly_menu_recipes 表'
PRINT '• 更新了 consumption_plans 表结构'
PRINT '• 创建了性能优化索引'
PRINT '• 更新了统计信息'
PRINT ''
PRINT '现在可以正常使用新的周菜单系统了！'
PRINT '访问: /weekly-menu-v2/ 开始使用'
PRINT '========================================='
