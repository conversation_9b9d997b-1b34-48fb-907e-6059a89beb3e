{% macro render(item, depth=0) %}
  {% set type = type_name(item) %}

  {% if type == 'tuple' or type == 'list' %}
    {% if not item %}
      Empty {{ type }}.
    {% else %}
      {% for n in item %}
        {{ loop.index }}) {{ render(n, depth + 1) }}<br/>
      {% endfor %}
    {% endif %}
  {% elif type == 'bool' %}
    {% if depth == 0 and item %}
    OK
    {% else %}
    <span class="type-bool">{{ item }}</span>
    {% endif %}
  {% elif type == 'str' or type == 'unicode' %}
    "{{ item }}"
  {% elif type == 'bytes' %}
    "{{ item.decode('utf-8') }}"
  {% elif type == 'TextWrapper' %}
    <pre>{{ item }}</pre>
  {% elif type == 'dict' %}
    {% for k, v in item.items() %}
      {{ loop.index }}) {{ k }} - {{ render(v, depth + 1) }}<br/>
    {% endfor %}
  {% else %}
    {{ item }}
  {% endif %}
{% endmacro %}
{{ render(result) }}