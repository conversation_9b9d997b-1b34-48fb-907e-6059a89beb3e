#!/usr/bin/env python3
"""
清理 MenuPlan 相关的模板文件
执行前请确保已备份重要文件
"""

import os
import shutil
from pathlib import Path

def cleanup_menu_plan_templates():
    """清理 MenuPlan 相关的模板文件"""
    
    # 要删除的目录和文件
    cleanup_targets = [
        'app/templates/menu_plan',  # 整个 menu_plan 模板目录
    ]
    
    # 可能需要检查的其他文件（包含 menu_plan 引用的模板）
    check_files = [
        'app/templates/main/index.html',
        'app/templates/main/dashboard.html',
        'app/templates/consumption_plan/create_from_menu.html',
        'app/templates/traceability/menu_trace.html',
    ]
    
    print("🧹 开始清理 MenuPlan 相关模板文件...")
    
    # 删除目录和文件
    for target in cleanup_targets:
        target_path = Path(target)
        if target_path.exists():
            if target_path.is_dir():
                print(f"📁 删除目录: {target}")
                shutil.rmtree(target_path)
            else:
                print(f"📄 删除文件: {target}")
                target_path.unlink()
        else:
            print(f"⚠️  目标不存在: {target}")
    
    print("\n🔍 检查可能包含 MenuPlan 引用的模板文件...")
    
    # 检查其他文件中的引用
    for file_path in check_files:
        path = Path(file_path)
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含 menu_plan 相关的引用
                menu_plan_refs = []
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if any(keyword in line.lower() for keyword in ['menu_plan', 'menu-plan', 'menuplan']):
                        menu_plan_refs.append(f"  第{i}行: {line.strip()}")
                
                if menu_plan_refs:
                    print(f"\n📄 {file_path} 中发现可能的 MenuPlan 引用:")
                    for ref in menu_plan_refs[:5]:  # 只显示前5个
                        print(ref)
                    if len(menu_plan_refs) > 5:
                        print(f"  ... 还有 {len(menu_plan_refs) - 5} 个引用")
                else:
                    print(f"✅ {file_path} - 未发现 MenuPlan 引用")
                    
            except Exception as e:
                print(f"❌ 检查 {file_path} 时出错: {e}")
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print("\n✅ 模板文件清理完成！")
    print("\n📋 建议后续操作:")
    print("1. 检查上述文件中的 MenuPlan 引用，手动修改或删除")
    print("2. 测试相关页面确保没有模板引用错误")
    print("3. 检查导航菜单中是否有指向已删除页面的链接")

if __name__ == '__main__':
    cleanup_menu_plan_templates()
