-- 财务系统模块可见性初始化SQL脚本
-- 为所有角色设置财务系统模块的默认可见性

PRINT '开始初始化财务系统模块可见性设置...';

-- 定义财务系统模块列表
DECLARE @financial_modules TABLE (
    module_id NVARCHAR(50)
);

INSERT INTO @financial_modules (module_id) VALUES
('financial'),              -- 财务管理主模块
('financial_overview'),     -- 财务概览
('accounting_subjects'),    -- 会计科目
('financial_vouchers'),     -- 财务凭证
('account_payables'),       -- 应付账款
('payment_records'),        -- 付款记录
('pending_stock_ins'),      -- 待处理入库
('balance_sheet'),          -- 资产负债表
('cost_analysis'),          -- 成本分析表
('payables_aging');         -- 账龄分析

-- 获取所有角色
DECLARE @role_id INT;
DECLARE @role_name NVARCHAR(100);
DECLARE @module_id NVARCHAR(50);
DECLARE @default_visibility BIT;
DECLARE @existing_count INT;
DECLARE @created_count INT = 0;
DECLARE @updated_count INT = 0;

-- 角色游标
DECLARE role_cursor CURSOR FOR 
    SELECT id, name FROM roles ORDER BY name;

OPEN role_cursor;
FETCH NEXT FROM role_cursor INTO @role_id, @role_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '处理角色: ' + @role_name + ' (ID: ' + CAST(@role_id AS NVARCHAR(10)) + ')';
    
    -- 模块游标
    DECLARE module_cursor CURSOR FOR 
        SELECT module_id FROM @financial_modules;
    
    OPEN module_cursor;
    FETCH NEXT FROM module_cursor INTO @module_id;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- 检查是否已存在设置
        SELECT @existing_count = COUNT(*) 
        FROM module_visibility 
        WHERE module_id = @module_id AND role_id = @role_id;
        
        IF @existing_count = 0
        BEGIN
            -- 根据角色名称确定默认可见性
            SET @default_visibility = 0; -- 默认不可见
            
            -- 管理员角色默认可见所有模块
            IF @role_name LIKE '%管理员%' OR @role_name LIKE '%admin%'
                SET @default_visibility = 1;
            
            -- 财务相关角色默认可见所有财务模块
            ELSE IF @role_name LIKE '%财务%' OR @role_name LIKE '%会计%' OR @role_name LIKE '%出纳%'
                SET @default_visibility = 1;
            
            -- 校长、副校长等高级管理角色可见报表模块
            ELSE IF (@role_name LIKE '%校长%' OR @role_name LIKE '%副校长%' OR @role_name LIKE '%主任%')
                AND @module_id IN ('financial', 'financial_overview', 'balance_sheet', 'income_statement', 'payables_aging')
                SET @default_visibility = 1;
            
            -- 采购相关角色可见应付账款相关模块
            ELSE IF (@role_name LIKE '%采购%' OR @role_name LIKE '%库管%')
                AND @module_id IN ('financial', 'account_payables', 'payment_records', 'pending_stock_ins')
                SET @default_visibility = 1;
            
            -- 插入新的可见性设置
            INSERT INTO module_visibility (module_id, role_id, is_visible, created_at, updated_at)
            VALUES (@module_id, @role_id, @default_visibility, GETDATE(), GETDATE());
            
            SET @created_count = @created_count + 1;
            
            PRINT '  ➕ ' + @module_id + ': 创建新设置 (可见性: ' + 
                  CASE WHEN @default_visibility = 1 THEN '是' ELSE '否' END + ')';
        END
        ELSE
        BEGIN
            SET @updated_count = @updated_count + 1;
            PRINT '  ✓ ' + @module_id + ': 已存在设置';
        END
        
        FETCH NEXT FROM module_cursor INTO @module_id;
    END
    
    CLOSE module_cursor;
    DEALLOCATE module_cursor;
    
    FETCH NEXT FROM role_cursor INTO @role_id, @role_name;
END

CLOSE role_cursor;
DEALLOCATE role_cursor;

PRINT '';
PRINT '✅ 财务模块可见性设置完成！';
PRINT '📊 统计信息:';
PRINT '   ➕ 新创建: ' + CAST(@created_count AS NVARCHAR(10)) + ' 个设置';
PRINT '   🔄 已存在: ' + CAST(@updated_count AS NVARCHAR(10)) + ' 个设置';

-- 显示各角色的财务模块可见性统计
PRINT '';
PRINT '📈 各角色财务模块可见性统计:';

SELECT 
    r.name AS '角色名称',
    COUNT(CASE WHEN mv.is_visible = 1 THEN 1 END) AS '可见模块数',
    COUNT(mv.id) AS '总模块数',
    CAST(ROUND(
        CAST(COUNT(CASE WHEN mv.is_visible = 1 THEN 1 END) AS FLOAT) / 
        CAST(COUNT(mv.id) AS FLOAT) * 100, 1
    ) AS NVARCHAR(10)) + '%' AS '可见比例'
FROM roles r
LEFT JOIN module_visibility mv ON r.id = mv.role_id 
    AND mv.module_id IN (
        'financial', 'financial_overview', 'accounting_subjects', 'financial_vouchers',
        'account_payables', 'payment_records', 'pending_stock_ins',
        'balance_sheet', 'cost_analysis', 'payables_aging'
    )
GROUP BY r.id, r.name
ORDER BY r.name;

-- 显示需要管理员手动调整的建议
PRINT '';
PRINT '💡 建议:';
PRINT '1. 访问后台管理 -> 系统 -> 模块可见性管理，根据实际需要调整各角色的财务模块可见性';
PRINT '2. 访问后台管理 -> 系统 -> 角色权限管理，为角色分配相应的财务权限';
PRINT '3. 财务管理首页地址: /financial/reports';

PRINT '';
PRINT '🎉 财务系统模块可见性初始化完成！';
