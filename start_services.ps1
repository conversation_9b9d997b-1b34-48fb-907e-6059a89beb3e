# PowerShell脚本：启动所有必要的服务
# 可以以普通用户身份运行

Write-Host "正在启动tdtech.xin相关服务..." -ForegroundColor Green

$currentDir = Get-Location

# 检查并启动IIS服务
Write-Host "`n检查IIS服务状态..." -ForegroundColor Yellow
$iisService = Get-Service W3SVC -ErrorAction SilentlyContinue
if ($iisService) {
    if ($iisService.Status -ne "Running") {
        Write-Host "启动IIS服务..." -ForegroundColor Yellow
        try {
            Start-Service W3SVC
            Write-Host "✓ IIS服务已启动" -ForegroundColor Green
        } catch {
            Write-Host "✗ 启动IIS服务失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✓ IIS服务已在运行" -ForegroundColor Green
    }
} else {
    Write-Host "✗ 未找到IIS服务" -ForegroundColor Red
}

# 检查Flask应用进程
Write-Host "`n检查Flask应用状态..." -ForegroundColor Yellow
$flaskProcess = Get-Process python -ErrorAction SilentlyContinue | Where-Object { 
    $_.ProcessName -eq "python" -and (Get-NetTCPConnection -OwningProcess $_.Id -ErrorAction SilentlyContinue | Where-Object LocalPort -eq 8080)
}

if ($flaskProcess) {
    Write-Host "✓ Flask应用已在运行 (PID: $($flaskProcess.Id))" -ForegroundColor Green
} else {
    Write-Host "启动Flask应用..." -ForegroundColor Yellow
    
    # 检查虚拟环境
    $venvPath = Join-Path $currentDir "venv\Scripts\Activate.ps1"
    if (Test-Path $venvPath) {
        Write-Host "激活虚拟环境..." -ForegroundColor Gray
        & $venvPath
    }
    
    # 启动Flask应用
    try {
        $flaskJob = Start-Job -ScriptBlock {
            param($dir)
            Set-Location $dir
            python run.py
        } -ArgumentList $currentDir
        
        Start-Sleep 3  # 等待应用启动
        
        # 再次检查
        $newFlaskProcess = Get-Process python -ErrorAction SilentlyContinue | Where-Object { 
            $_.ProcessName -eq "python" -and (Get-NetTCPConnection -OwningProcess $_.Id -ErrorAction SilentlyContinue | Where-Object LocalPort -eq 8080)
        }
        
        if ($newFlaskProcess) {
            Write-Host "✓ Flask应用启动成功 (PID: $($newFlaskProcess.Id))" -ForegroundColor Green
        } else {
            Write-Host "✗ Flask应用启动失败" -ForegroundColor Red
            Write-Host "请手动运行: python run.py" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "✗ 启动Flask应用时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 检查端口状态
Write-Host "`n检查端口状态..." -ForegroundColor Yellow

$ports = @(
    @{Port=80; Name="IIS (HTTP)"},
    @{Port=8080; Name="Flask应用"}
)

foreach ($portInfo in $ports) {
    $port = $portInfo.Port
    $name = $portInfo.Name
    
    $listening = netstat -ano | Select-String ":$port.*LISTENING"
    if ($listening) {
        Write-Host "✓ 端口 $port ($name): 正在监听" -ForegroundColor Green
    } else {
        Write-Host "✗ 端口 $port ($name): 未监听" -ForegroundColor Red
    }
}

# 测试本地连接
Write-Host "`n测试本地连接..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ Flask应用响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Flask应用连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ IIS代理响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ IIS代理连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "服务状态检查完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n如果所有服务都正常运行，您现在可以通过以下方式访问:" -ForegroundColor Yellow
Write-Host "- http://tdtech.xin" -ForegroundColor White
Write-Host "- http://www.tdtech.xin" -ForegroundColor White
Write-Host "- http://localhost (本地测试)" -ForegroundColor White
