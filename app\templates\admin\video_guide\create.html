{% extends "base.html" %}

{% block title %}创建视频{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-video mr-2"></i>创建引导视频
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('guide_management.video_management') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.step_name.label(class="form-label") }}
                                    {{ form.step_name(class="form-control") }}
                                    {% if form.step_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.step_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3") }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.video_file.label(class="form-label") }}
                                    {{ form.video_file(class="form-control-file") }}
                                    <small class="form-text text-muted">
                                        支持格式：MP4、AVI、MOV、WMV、FLV、WebM，文件大小限制50MB
                                    </small>
                                    {% if form.video_file.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.video_file.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.thumbnail_file.label(class="form-label") }}
                                    {{ form.thumbnail_file(class="form-control-file") }}
                                    <small class="form-text text-muted">
                                        可选，支持JPG、PNG、GIF格式
                                    </small>
                                    {% if form.thumbnail_file.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.thumbnail_file.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save mr-1"></i>创建视频
                            </button>
                            <a href="{{ url_for('guide_management.video_management') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times mr-1"></i>取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 文件大小检查
    $('#video_file').on('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                alert('视频文件大小不能超过50MB');
                $(this).val('');
            }
        }
    });
});
</script>
{% endblock %}
