"""
增强版自动修复模块

此模块提供全面的系统修复功能，包括：
1. 检测和修复缺失的表
2. 检测和修复表中缺失的列
3. 修复SQL语法兼容性问题
4. 处理数据类型不匹配问题
5. 提供详细的操作日志
"""

import os
import re
import sys
import traceback
import importlib
import inspect
from datetime import datetime
from sqlalchemy import text, inspect as sa_inspect
from flask import current_app

from app import create_app, db
from app.models import *

# 日志记录
def log_enhanced_fix(operation, status, details=None):
    """记录增强修复操作日志"""
    log_dir = os.path.join(current_app.root_path, 'logs')
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f'enhanced_fix_{datetime.now().strftime("%Y%m%d")}.log')

    with open(log_file, 'a', encoding='utf-8') as f:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {operation} - {status}"
        if details:
            log_entry += f" - {details}"
        f.write(log_entry + "\n")

# 数据库表定义 - 包含所有必要的表
TABLE_DEFINITIONS = {
    'users': """
        CREATE TABLE users (
            id INT IDENTITY(1,1) PRIMARY KEY,
            username NVARCHAR(80) NOT NULL,
            password_hash NVARCHAR(128) NOT NULL,
            email NVARCHAR(100) NULL,
            real_name NVARCHAR(50) NULL,
            phone NVARCHAR(20) NULL,
            avatar NVARCHAR(200) NULL,
            last_login DATETIME2 NULL,
            status INT NOT NULL DEFAULT 1,
            area_id INT NULL,
            area_level INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE()
        )
    """,
    'recipes': """
        CREATE TABLE recipes (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            category NVARCHAR(50) NULL,
            category_id INT NULL,
            meal_type NVARCHAR(20) NULL,
            main_image NVARCHAR(200) NULL,
            description NVARCHAR(MAX) NULL,
            calories INT NULL,
            nutrition_info NVARCHAR(MAX) NULL,
            cooking_method NVARCHAR(100) NULL,
            cooking_steps NVARCHAR(MAX) NULL,
            cooking_time INT NULL,
            serving_size INT NULL,
            status INT NOT NULL DEFAULT 1,
            created_by INT NOT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL,
            parent_id INT NULL,
            is_template BIT DEFAULT 0,
            template_type NVARCHAR(50) NULL,
            variation_reason NVARCHAR(200) NULL,
            version INT DEFAULT 1
        )
    """,
    'food_samples': """
        CREATE TABLE food_samples (
            id INT IDENTITY(1,1) PRIMARY KEY,
            sample_number NVARCHAR(50) NULL,
            recipe_id INT NOT NULL,
            area_id INT NULL,
            menu_plan_id INT NULL,
            meal_date DATE NULL,
            meal_type NVARCHAR(20) NULL,
            sample_image NVARCHAR(200) NOT NULL,
            sample_quantity FLOAT NULL,
            sample_unit NVARCHAR(20) NULL,
            storage_location NVARCHAR(100) NOT NULL,
            storage_temperature NVARCHAR(50) NULL,
            start_time DATETIME2 NOT NULL,
            end_time DATETIME2 NOT NULL,
            status NVARCHAR(20) DEFAULT N'保存中' NOT NULL,
            created_by INT NOT NULL,
            notes NVARCHAR(MAX) NULL,
            created_at DATETIME2 DEFAULT GETDATE() NOT NULL,
            updated_at DATETIME2 NULL,
            operator_id INT NULL,
            destruction_time DATETIME2 NULL,
            destruction_operator_id INT NULL
        )
    """,
    'purchase_orders': """
        CREATE TABLE purchase_orders (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_number NVARCHAR(50) NOT NULL,
            supplier_id INT NOT NULL,
            requisition_id INT NULL,
            area_id INT NOT NULL,
            total_amount DECIMAL(12, 2) NOT NULL,
            order_date DATETIME2 NOT NULL,
            expected_delivery_date DATE NULL,
            payment_terms NVARCHAR(200) NULL,
            delivery_terms NVARCHAR(200) NULL,
            status NVARCHAR(20) NOT NULL,
            delivery_date DATETIME2 NULL,
            created_by INT NOT NULL,
            approved_by INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
    """
}

# 列定义 - 包含所有表中可能缺失的列
COLUMN_DEFINITIONS = {
    'recipes': {
        'category': 'NVARCHAR(50)',
        'meal_type': 'NVARCHAR(20)',
        'main_image': 'NVARCHAR(200)',
        'calories': 'INT',
        'cooking_steps': 'NVARCHAR(MAX)',
        'parent_id': 'INT',
        'is_template': 'BIT DEFAULT 0',
        'template_type': 'NVARCHAR(50)',
        'variation_reason': 'NVARCHAR(200)',
        'version': 'INT DEFAULT 1'
    },
    'food_samples': {
        'operator_id': 'INT',
        'destruction_time': 'DATETIME2',
        'destruction_operator_id': 'INT'
    },
    'users': {
        'area_level': 'INT',
        'status': 'INT DEFAULT 1'
    }
}

def get_all_models():
    """获取所有模型类"""
    models = []
    for name, obj in inspect.getmembers(sys.modules['app.models']):
        if inspect.isclass(obj) and issubclass(obj, db.Model) and obj != db.Model:
            models.append(obj)
    return models

def check_and_fix_missing_tables():
    """检查并修复缺失的表"""
    app = create_app()
    fixed_tables = []

    with app.app_context():
        try:
            # 获取所有现有表
            result = db.session.execute(text("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"))
            existing_tables = [row[0].lower() for row in result.fetchall()]

            # 检查并创建缺失的表
            for table_name, create_sql in TABLE_DEFINITIONS.items():
                if table_name.lower() not in existing_tables:
                    try:
                        db.session.execute(text(create_sql))
                        fixed_tables.append(table_name)
                        log_enhanced_fix("check_and_fix_missing_tables", "成功", f"创建了表: {table_name}")
                    except Exception as e:
                        log_enhanced_fix("check_and_fix_missing_tables", "失败", f"创建表 {table_name} 时出错: {str(e)}")
                        print(f"创建表 {table_name} 时出错: {str(e)}")

            # 提交事务
            if fixed_tables:
                db.session.commit()
                print(f"已修复缺失的表: {', '.join(fixed_tables)}")
            else:
                print("所有必要的表都已存在")

            return fixed_tables

        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            log_enhanced_fix("check_and_fix_missing_tables", "失败", error_details)
            print(f"检查并修复缺失的表时出错: {str(e)}")
            return []

def check_and_fix_missing_columns():
    """检查并修复缺失的列"""
    app = create_app()
    fixed_columns = {}

    with app.app_context():
        try:
            # 检查并添加缺失的列
            for table_name, columns in COLUMN_DEFINITIONS.items():
                # 检查表是否存在
                result = db.session.execute(text(f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'"))
                if result.scalar() == 0:
                    print(f"表 {table_name} 不存在，无法添加列")
                    continue

                # 获取现有列
                result = db.session.execute(text(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}'"))
                existing_columns = [row[0].lower() for row in result.fetchall()]

                # 添加缺失的列
                table_fixed_columns = []
                for column_name, data_type in columns.items():
                    if column_name.lower() not in existing_columns:
                        try:
                            sql = f"ALTER TABLE {table_name} ADD {column_name} {data_type}"
                            db.session.execute(text(sql))
                            table_fixed_columns.append(f"{column_name} ({data_type})")
                            log_enhanced_fix("check_and_fix_missing_columns", "成功", f"在表 {table_name} 中添加了列: {column_name}")
                        except Exception as e:
                            log_enhanced_fix("check_and_fix_missing_columns", "失败", f"在表 {table_name} 中添加列 {column_name} 时出错: {str(e)}")
                            print(f"在表 {table_name} 中添加列 {column_name} 时出错: {str(e)}")

                if table_fixed_columns:
                    fixed_columns[table_name] = table_fixed_columns

            # 提交事务
            if fixed_columns:
                db.session.commit()
                for table_name, columns in fixed_columns.items():
                    print(f"已修复表 {table_name} 中缺失的列: {', '.join(columns)}")
            else:
                print("所有必要的列都已存在")

            return fixed_columns

        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            log_enhanced_fix("check_and_fix_missing_columns", "失败", error_details)
            print(f"检查并修复缺失的列时出错: {str(e)}")
            return {}

def check_and_fix_data_types():
    """检查并修复数据类型不匹配问题"""
    app = create_app()
    fixed_columns = {}

    with app.app_context():
        try:
            # 获取所有模型
            models = get_all_models()

            # 检查每个模型的列类型
            for model in models:
                table_name = model.__tablename__

                # 检查表是否存在
                result = db.session.execute(text(f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'"))
                if result.scalar() == 0:
                    print(f"表 {table_name} 不存在，无法检查数据类型")
                    continue

                # 获取表中的列信息
                result = db.session.execute(text(f"""
                    SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = '{table_name}'
                """))
                db_columns = {row[0].lower(): {'type': row[1], 'length': row[2]} for row in result.fetchall()}

                # 检查模型中定义的列类型
                table_fixed_columns = []
                for attr_name, column in inspect.getmembers(model):
                    if isinstance(column, db.Column) and not attr_name.startswith('_'):
                        if attr_name.lower() in db_columns:
                            # 这里可以添加类型检查和修复逻辑
                            # 由于类型修复比较复杂，这里只记录不匹配的情况
                            expected_type = str(column.type)
                            actual_type = db_columns[attr_name.lower()]['type']

                            # 简单的类型检查，可以根据需要扩展
                            if 'VARCHAR' in expected_type.upper() and 'NVARCHAR' not in actual_type.upper():
                                log_enhanced_fix("check_and_fix_data_types", "警告",
                                                f"表 {table_name} 中列 {attr_name} 的类型不匹配: 期望 {expected_type}, 实际 {actual_type}")

                if table_fixed_columns:
                    fixed_columns[table_name] = table_fixed_columns

            return fixed_columns

        except Exception as e:
            error_details = traceback.format_exc()
            log_enhanced_fix("check_and_fix_data_types", "失败", error_details)
            print(f"检查并修复数据类型时出错: {str(e)}")
            return {}

def fix_sql_syntax_compatibility():
    """修复SQL语法兼容性问题"""
    app = create_app()
    fixed_files = []

    # SQL语法替换规则
    syntax_replacements = [
        # 字符串连接
        (r"(\w+)\s*\|\|\s*(\w+)", r"CONCAT(\1, \2)"),
        (r"(\w+)\s*\|\|\s*(['\"].*?['\"])", r"CONCAT(\1, \2)"),
        (r"(['\"].*?['\"])\s*\|\|\s*(\w+)", r"CONCAT(\1, \2)"),
        (r"(['\"].*?['\"])\s*\|\|\s*(['\"].*?['\"])", r"CONCAT(\1, \2)"),

        # 日期函数
        (r"strftime\(['\"](.*?)['\"],\s*(.+?)\)", r"FORMAT(\2, '\1')"),
        (r"date\((.+?)\)", r"CAST(\1 AS DATE)"),
        (r"datetime\((.+?)\)", r"CAST(\1 AS DATETIME2)"),

        # LIMIT和OFFSET
        (r"LIMIT\s+(\d+)\s+OFFSET\s+(\d+)", r"OFFSET \2 ROWS FETCH NEXT \1 ROWS ONLY"),
        (r"LIMIT\s+(\d+)", r"OFFSET 0 ROWS FETCH NEXT \1 ROWS ONLY"),

        # 大小写不敏感的LIKE
        (r"LIKE\s+(['\"]\S+['\"])\s+COLLATE\s+NOCASE", r"LIKE \1"),

        # NULL值处理
        (r"IFNULL\((.+?),\s*(.+?)\)", r"ISNULL(\1, \2)"),
    ]

    try:
        # 获取应用根目录
        app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 扫描Python文件
        for root, dirs, files in os.walk(app_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)

                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 应用语法替换
                    modified = False
                    for pattern, replacement in syntax_replacements:
                        if re.search(pattern, content):
                            content = re.sub(pattern, replacement, content)
                            modified = True

                    # 如果有修改，保存文件
                    if modified:
                        # 创建备份
                        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
                        with open(backup_path, 'w', encoding='utf-8') as f:
                            f.write(content)

                        # 保存修改后的文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)

                        fixed_files.append(file_path)
                        log_enhanced_fix("fix_sql_syntax_compatibility", "成功", f"修复了文件: {file_path}")

        return fixed_files

    except Exception as e:
        error_details = traceback.format_exc()
        log_enhanced_fix("fix_sql_syntax_compatibility", "失败", error_details)
        print(f"修复SQL语法兼容性时出错: {str(e)}")
        return []

def enhanced_auto_fix_all():
    """增强版自动检测和修复所有问题"""
    print("开始增强版自动修复...")

    # 1. 修复缺失的表
    fixed_tables = check_and_fix_missing_tables()

    # 2. 修复缺失的列
    fixed_columns = check_and_fix_missing_columns()

    # 3. 检查数据类型不匹配
    type_issues = check_and_fix_data_types()

    # 4. 修复SQL语法兼容性问题
    fixed_files = fix_sql_syntax_compatibility()

    # 返回修复结果
    return {
        'fixed_tables': fixed_tables,
        'fixed_columns': fixed_columns,
        'type_issues': type_issues,
        'fixed_files': fixed_files
    }
