"""
权限迁移工具

用于自动迁移角色权限，处理模块变化和合并。
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Role
from app.utils.decorators import admin_required
from app.utils.permissions import parse_permissions_json, format_permissions_json
import json

permission_migration_bp = Blueprint('permission_migration', __name__)

@permission_migration_bp.route('/')
@login_required
@admin_required
def index():
    """权限迁移工具首页"""
    # 获取所有角色
    roles = Role.query.all()
    
    # 统计需要迁移的角色数量
    menu_plan_roles = []
    sample_roles = []
    
    for role in roles:
        permissions = parse_permissions_json(role.permissions)
        
        # 检查是否有菜单计划权限但没有周菜单权限
        if 'menu_plan' in permissions and 'weekly_menu' not in permissions:
            menu_plan_roles.append(role)
        
        # 检查是否有留样权限但没有溯源权限
        if 'sample' in permissions and 'traceability' not in permissions:
            sample_roles.append(role)
    
    return render_template(
        'system_fix/permission_migration.html',
        title='权限迁移工具',
        roles=roles,
        menu_plan_roles=menu_plan_roles,
        sample_roles=sample_roles
    )

@permission_migration_bp.route('/migrate-menu-plan')
@login_required
@admin_required
def migrate_menu_plan():
    """迁移菜单计划权限到周菜单权限"""
    try:
        # 获取所有角色
        roles = Role.query.all()
        
        migrated_count = 0
        
        for role in roles:
            permissions = parse_permissions_json(role.permissions)
            
            # 检查是否有菜单计划权限但没有周菜单权限
            if 'menu_plan' in permissions and 'weekly_menu' not in permissions:
                # 添加周菜单权限
                permissions['weekly_menu'] = permissions['menu_plan'].copy()
                
                # 如果有execute权限，替换为publish权限
                if 'execute' in permissions['weekly_menu']:
                    permissions['weekly_menu'].remove('execute')
                    if 'publish' not in permissions['weekly_menu']:
                        permissions['weekly_menu'].append('publish')
                
                # 更新角色权限
                permissions_json = format_permissions_json(permissions)
                
                # 使用原始SQL更新角色权限，避免精度问题
                from sqlalchemy import text
                sql = text("""
                UPDATE roles
                SET permissions = :permissions
                WHERE id = :id
                """)
                
                db.session.execute(
                    sql,
                    {
                        'permissions': permissions_json,
                        'id': role.id
                    }
                )
                
                migrated_count += 1
        
        db.session.commit()
        
        flash(f'成功迁移 {migrated_count} 个角色的菜单计划权限到周菜单权限', 'success')
        return redirect(url_for('permission_migration.index'))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"迁移菜单计划权限时出错: {str(e)}")
        flash(f'迁移菜单计划权限时出错: {str(e)}', 'danger')
        return redirect(url_for('permission_migration.index'))

@permission_migration_bp.route('/migrate-sample')
@login_required
@admin_required
def migrate_sample():
    """迁移留样权限到食材溯源与留样权限"""
    try:
        # 获取所有角色
        roles = Role.query.all()
        
        migrated_count = 0
        
        for role in roles:
            permissions = parse_permissions_json(role.permissions)
            
            # 检查是否有留样权限但没有溯源权限
            if 'sample' in permissions and 'traceability' not in permissions:
                # 添加溯源权限
                permissions['traceability'] = permissions['sample'].copy()
                
                # 添加manage_sample权限
                if 'manage_sample' not in permissions['traceability']:
                    permissions['traceability'].append('manage_sample')
                
                # 更新角色权限
                permissions_json = format_permissions_json(permissions)
                
                # 使用原始SQL更新角色权限，避免精度问题
                from sqlalchemy import text
                sql = text("""
                UPDATE roles
                SET permissions = :permissions
                WHERE id = :id
                """)
                
                db.session.execute(
                    sql,
                    {
                        'permissions': permissions_json,
                        'id': role.id
                    }
                )
                
                migrated_count += 1
        
        db.session.commit()
        
        flash(f'成功迁移 {migrated_count} 个角色的留样权限到食材溯源与留样权限', 'success')
        return redirect(url_for('permission_migration.index'))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"迁移留样权限时出错: {str(e)}")
        flash(f'迁移留样权限时出错: {str(e)}', 'danger')
        return redirect(url_for('permission_migration.index'))

@permission_migration_bp.route('/add-daily-management')
@login_required
@admin_required
def add_daily_management():
    """为食堂管理员角色添加食堂日常管理权限"""
    try:
        # 获取所有食堂管理员角色
        cafeteria_admin_roles = Role.query.filter(Role.name.like('%食堂%')).all()
        
        added_count = 0
        
        for role in cafeteria_admin_roles:
            permissions = parse_permissions_json(role.permissions)
            
            # 检查是否没有食堂日常管理权限
            if 'daily_management' not in permissions:
                # 添加食堂日常管理权限
                permissions['daily_management'] = ['*']
                
                # 更新角色权限
                permissions_json = format_permissions_json(permissions)
                
                # 使用原始SQL更新角色权限，避免精度问题
                from sqlalchemy import text
                sql = text("""
                UPDATE roles
                SET permissions = :permissions
                WHERE id = :id
                """)
                
                db.session.execute(
                    sql,
                    {
                        'permissions': permissions_json,
                        'id': role.id
                    }
                )
                
                added_count += 1
        
        db.session.commit()
        
        flash(f'成功为 {added_count} 个食堂管理员角色添加食堂日常管理权限', 'success')
        return redirect(url_for('permission_migration.index'))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加食堂日常管理权限时出错: {str(e)}")
        flash(f'添加食堂日常管理权限时出错: {str(e)}', 'danger')
        return redirect(url_for('permission_migration.index'))
