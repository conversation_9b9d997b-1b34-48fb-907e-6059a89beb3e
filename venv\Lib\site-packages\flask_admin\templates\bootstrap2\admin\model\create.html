{% extends 'admin/master.html' %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
{% endblock %}

{% block body %}
  {% block navlinks %}
  <ul class="nav nav-tabs">
    <li>
        <a href="{{ return_url }}">{{ _gettext('List') }}</a>
    </li>
    <li class="active">
        <a href="javascript:void(0)">{{ _gettext('Create') }}</a>
    </li>
  </ul>
  {% endblock %}

  {% block create_form %}
    {{ lib.render_form(form, return_url, extra(), form_opts) }}
  {% endblock %}
{% endblock %}

{% block tail %}
  {{ super() }}
  {{ lib.form_js() }}
{% endblock %}
