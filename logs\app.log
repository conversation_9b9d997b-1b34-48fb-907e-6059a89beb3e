2025-06-06 22:44:27,378 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 22:44:27,382 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 22:44:27,414 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:51:34,625 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-06 22:51:56,260 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-06 22:52:18,257 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:52:18,269 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 22:52:18,271 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-06 22:52:18,272 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 22:52:18,300 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:53:07,833 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:57:09,376 INFO: 新的在线咨询提交: ID=1, 姓名=食堂仓库, 联系方式=微信:18373062333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\consultation.py:78]
2025-06-06 23:10:14,124 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 23:15:56,644 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 23:18:24,999 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 23:20:10,167 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 23:25:32,289 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 23:34:04,939 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-07 12:32:13,169 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-07 18:24:51,336 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:788]
2025-06-07 18:25:11,631 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-07 18:25:11,660 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-07 18:26:03,446 INFO: 获取副表数据用于补全主表: weekly_menu_id=38 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 缤纷吐司蒸, ID=153 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 米饭, ID=154 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 黑木耳炒山药, ID=367 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,457 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 鲜笋烧仔排, ID=370 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=早餐, 菜品=🏫 韭菜炒藕丝, ID=375 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 黄米南瓜盅, ID=368 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜蚕豆烧大雁, ID=369 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜椒青瓜干, ID=371 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,458 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲍汁茶树菇, ID=372 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,459 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 鲜桃仁木耳炒山药, ID=373 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,459 INFO: 副表数据: 日期=1, 餐次=午餐, 菜品=🏫 韭菜炒软饼元, ID=374 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
2025-06-07 18:26:03,459 INFO: 副表数据: 日期=1, 餐次=晚餐, 菜品=🏫 西瓜桃子面, ID=152 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:368]
