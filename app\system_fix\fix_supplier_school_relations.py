"""
修复supplier_school_relations表结构
"""

from app import create_app, db
from sqlalchemy import text

def fix_supplier_school_relations_table():
    """修复supplier_school_relations表的datetime精度问题"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否存在
            result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'supplier_school_relations'"))
            if result.scalar() == 0:
                print("supplier_school_relations表不存在，无需修复")
                return
            
            # 检查created_at和updated_at列的精度
            result = db.session.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, DATETIME_PRECISION 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'supplier_school_relations' 
                AND COLUMN_NAME IN ('created_at', 'updated_at')
            """))
            
            columns_to_fix = []
            for row in result:
                column_name, data_type, precision = row
                if data_type == 'datetime2' and (precision is None or precision != 1):
                    columns_to_fix.append(column_name)
            
            if not columns_to_fix:
                print("supplier_school_relations表的datetime列精度已正确设置，无需修复")
                return
            
            # 修复列精度
            for column in columns_to_fix:
                print(f"修复supplier_school_relations表的{column}列精度...")
                db.session.execute(text(f"""
                    ALTER TABLE supplier_school_relations
                    ALTER COLUMN {column} datetime2(1) NOT NULL
                """))
            
            db.session.commit()
            print("supplier_school_relations表结构修复完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"修复supplier_school_relations表结构时出错: {str(e)}")
            raise

if __name__ == "__main__":
    fix_supplier_school_relations_table()
