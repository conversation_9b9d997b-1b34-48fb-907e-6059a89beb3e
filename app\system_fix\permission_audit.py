"""
权限审计工具

用于检查角色权限是否与实际模块一致，识别可能存在问题的权限设置。
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Role
from app.utils.decorators import admin_required
from app.utils.permissions import PERMISSIONS, parse_permissions_json, format_permissions_json
import json

permission_audit_bp = Blueprint('permission_audit', __name__)

@permission_audit_bp.route('/')
@login_required
@admin_required
def index():
    """权限审计工具首页"""
    # 获取所有角色
    roles = Role.query.all()
    
    # 审计结果
    audit_results = []
    
    # 定义已弃用和已合并的模块
    deprecated_modules = ['menu_plan']
    merged_modules = ['sample']
    new_modules = ['weekly_menu', 'traceability', 'daily_management']
    
    # 定义模块映射关系
    module_mapping = {
        'menu_plan': 'weekly_menu',
        'sample': 'traceability'
    }
    
    for role in roles:
        permissions = parse_permissions_json(role.permissions)
        
        role_issues = []
        
        # 检查是否使用了已弃用的模块但没有使用替代模块
        for deprecated_module in deprecated_modules:
            if deprecated_module in permissions:
                replacement_module = module_mapping.get(deprecated_module)
                if replacement_module and replacement_module not in permissions:
                    role_issues.append({
                        'type': 'deprecated',
                        'module': deprecated_module,
                        'replacement': replacement_module,
                        'message': f'使用了已弃用的模块 {deprecated_module}，但没有使用替代模块 {replacement_module}'
                    })
        
        # 检查是否使用了已合并的模块但没有使用合并后的模块
        for merged_module in merged_modules:
            if merged_module in permissions:
                merged_into = module_mapping.get(merged_module)
                if merged_into and merged_into not in permissions:
                    role_issues.append({
                        'type': 'merged',
                        'module': merged_module,
                        'merged_into': merged_into,
                        'message': f'使用了已合并的模块 {merged_module}，但没有使用合并后的模块 {merged_into}'
                    })
        
        # 检查是否缺少新模块的权限
        for new_module in new_modules:
            if new_module not in permissions:
                role_issues.append({
                    'type': 'missing',
                    'module': new_module,
                    'message': f'缺少新模块 {new_module} 的权限'
                })
        
        # 检查是否使用了不存在的模块
        for module in permissions:
            if module not in PERMISSIONS and module != '*':
                role_issues.append({
                    'type': 'unknown',
                    'module': module,
                    'message': f'使用了不存在的模块 {module}'
                })
        
        # 检查是否使用了不存在的操作
        for module, actions in permissions.items():
            if module in PERMISSIONS:
                for action in actions:
                    if action != '*' and action not in PERMISSIONS[module]['actions']:
                        role_issues.append({
                            'type': 'unknown_action',
                            'module': module,
                            'action': action,
                            'message': f'在模块 {module} 中使用了不存在的操作 {action}'
                        })
        
        if role_issues:
            audit_results.append({
                'role': role,
                'issues': role_issues
            })
    
    return render_template(
        'system_fix/permission_audit.html',
        title='权限审计工具',
        roles=roles,
        audit_results=audit_results
    )

@permission_audit_bp.route('/fix-all-issues')
@login_required
@admin_required
def fix_all_issues():
    """修复所有权限问题"""
    try:
        # 获取所有角色
        roles = Role.query.all()
        
        # 定义模块映射关系
        module_mapping = {
            'menu_plan': 'weekly_menu',
            'sample': 'traceability'
        }
        
        fixed_count = 0
        
        for role in roles:
            permissions = parse_permissions_json(role.permissions)
            
            # 修复已弃用和已合并的模块
            for old_module, new_module in module_mapping.items():
                if old_module in permissions and new_module not in permissions:
                    # 复制权限
                    permissions[new_module] = permissions[old_module].copy()
                    
                    # 特殊处理
                    if old_module == 'menu_plan' and 'execute' in permissions[new_module]:
                        permissions[new_module].remove('execute')
                        if 'publish' not in permissions[new_module]:
                            permissions[new_module].append('publish')
                    
                    if old_module == 'sample' and new_module == 'traceability':
                        if 'manage_sample' not in permissions[new_module]:
                            permissions[new_module].append('manage_sample')
            
            # 为食堂管理员角色添加食堂日常管理权限
            if '食堂' in role.name and 'daily_management' not in permissions:
                permissions['daily_management'] = ['*']
            
            # 更新角色权限
            permissions_json = format_permissions_json(permissions)
            
            # 使用原始SQL更新角色权限，避免精度问题
            from sqlalchemy import text
            sql = text("""
            UPDATE roles
            SET permissions = :permissions
            WHERE id = :id
            """)
            
            db.session.execute(
                sql,
                {
                    'permissions': permissions_json,
                    'id': role.id
                }
            )
            
            fixed_count += 1
        
        db.session.commit()
        
        flash(f'成功修复 {fixed_count} 个角色的权限问题', 'success')
        return redirect(url_for('permission_audit.index'))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修复权限问题时出错: {str(e)}")
        flash(f'修复权限问题时出错: {str(e)}', 'danger')
        return redirect(url_for('permission_audit.index'))
