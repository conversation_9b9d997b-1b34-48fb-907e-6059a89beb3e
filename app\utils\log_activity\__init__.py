"""
用户活动日志记录工具

提供统一的用户活动日志记录功能，确保所有操作都被正确记录。
"""

from flask import request, current_app
from flask_login import current_user
from app import db
from sqlalchemy import text
import json
from datetime import datetime

def log_activity(action, description=None, resource_type=None, resource_id=None, area_id=None, details=None):
    """
    记录用户活动

    使用原始SQL插入审计日志，避免SQLAlchemy的日期时间处理问题

    Args:
        action: 操作类型，如'创建'、'更新'、'删除'等
        description: 操作描述
        resource_type: 资源类型，如'daily_log'、'inspection'等
        resource_id: 资源ID
        area_id: 区域ID
        details: 详细信息，可以是字典或其他复杂对象

    Returns:
        int: 新创建的日志ID，如果创建失败则返回None
    """
    if not current_user.is_authenticated:
        return None

    try:
        # 处理详情字段
        details_value = json.dumps(details) if details else description
        ip_address = request.remote_addr if request else None
        user_agent = request.user_agent.string if request and request.user_agent else None

        # 使用SQL Server兼容的日期时间格式和OUTPUT子句获取插入的ID
        # 注意：不包含 created_at 字段，让数据库使用默认值
        sql = text("""
        INSERT INTO audit_logs
        (user_id, action, resource_type, resource_id, area_id, details, ip_address, user_agent)
        OUTPUT inserted.id
        VALUES
        (:user_id, :action, :resource_type, :resource_id, :area_id, :details, :ip_address, :user_agent)
        """)

        result = db.session.execute(
            sql,
            {
                'user_id': current_user.id,
                'action': action,
                'resource_type': resource_type,
                'resource_id': resource_id,
                'area_id': area_id,
                'details': details_value,
                'ip_address': ip_address,
                'user_agent': user_agent
            }
        )

        # 获取插入的ID
        inserted_id = result.scalar()
        db.session.commit()

        return inserted_id

    except Exception as e:
        current_app.logger.error(f"记录活动失败: {str(e)}")
        db.session.rollback()
        return None
