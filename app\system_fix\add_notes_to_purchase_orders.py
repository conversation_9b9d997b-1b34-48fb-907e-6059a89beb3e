"""
添加notes字段到purchase_orders表
"""

import pyodbc
import os
from dotenv import load_dotenv
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

def add_notes_column():
    """向purchase_orders表添加notes列"""
    try:
        # 获取数据库连接信息
        server = os.getenv('DB_SERVER')
        database = os.getenv('DB_NAME')
        username = os.getenv('DB_USERNAME')
        password = os.getenv('DB_PASSWORD')
        
        # 创建连接字符串
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
        
        # 连接数据库
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # 检查列是否已存在
        cursor.execute("""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'purchase_orders'
        AND COLUMN_NAME = 'notes'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if not column_exists:
            # 添加notes列
            cursor.execute("""
            ALTER TABLE purchase_orders
            ADD notes NVARCHAR(MAX) NULL
            """)
            conn.commit()
            logger.info("成功添加notes列到purchase_orders表")
        else:
            logger.info("notes列已存在于purchase_orders表中")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"添加notes列时出错: {str(e)}")
        return False

if __name__ == "__main__":
    add_notes_column()
