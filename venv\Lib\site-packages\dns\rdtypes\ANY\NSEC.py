# Copyright (C) Dnspython Contributors, see LICENSE for text of ISC license

# Copyright (C) 2004-2007, 2009-2011 Nominum, Inc.
#
# Permission to use, copy, modify, and distribute this software and its
# documentation for any purpose with or without fee is hereby granted,
# provided that the above copyright notice and this permission notice
# appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND NOMINUM DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL NOMINUM BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
# OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

import dns.exception
import dns.immutable
import dns.name
import dns.rdata
import dns.rdatatype
import dns.rdtypes.util


@dns.immutable.immutable
class Bitmap(dns.rdtypes.util.Bitmap):
    type_name = "NSEC"


@dns.immutable.immutable
class NSEC(dns.rdata.Rdata):
    """NSEC record"""

    __slots__ = ["next", "windows"]

    def __init__(self, rdclass, rdtype, next, windows):
        super().__init__(rdclass, rdtype)
        self.next = self._as_name(next)
        if not isinstance(windows, Bitmap):
            windows = Bitmap(windows)
        self.windows = tuple(windows.windows)

    def to_text(self, origin=None, relativize=True, **kw):
        next = self.next.choose_relativity(origin, relativize)
        text = Bitmap(self.windows).to_text()
        return "{}{}".format(next, text)

    @classmethod
    def from_text(
        cls, rdclass, rdtype, tok, origin=None, relativize=True, relativize_to=None
    ):
        next = tok.get_name(origin, relativize, relativize_to)
        windows = Bitmap.from_text(tok)
        return cls(rdclass, rdtype, next, windows)

    def _to_wire(self, file, compress=None, origin=None, canonicalize=False):
        # Note that NSEC downcasing, originally mandated by RFC 4034
        # section 6.2 was removed by RFC 6840 section 5.1.
        self.next.to_wire(file, None, origin, False)
        Bitmap(self.windows).to_wire(file)

    @classmethod
    def from_wire_parser(cls, rdclass, rdtype, parser, origin=None):
        next = parser.get_name(origin)
        bitmap = Bitmap.from_wire_parser(parser)
        return cls(rdclass, rdtype, next, bitmap)
