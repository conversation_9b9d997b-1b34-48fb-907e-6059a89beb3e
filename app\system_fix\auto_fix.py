"""
自动修复工具 - 自动检测和修复系统中的各种问题
"""

from app import create_app, db
from sqlalchemy import text, inspect
import traceback
import os
import json
from datetime import datetime
import re

# 数据库表定义
TABLE_DEFINITIONS = {
    'users': """
        CREATE TABLE users (
            id INT IDENTITY(1,1) PRIMARY KEY,
            username NVARCHAR(80) NOT NULL,
            password_hash NVARCHAR(128) NOT NULL,
            email NVARCHAR(100) NULL,
            real_name NVARCHAR(50) NULL,
            phone NVARCHAR(20) NULL,
            avatar NVARCHAR(200) NULL,
            last_login DATETIME2 NULL,
            status INT NOT NULL DEFAULT 1,
            area_id INT NULL,
            area_level INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE()
        )
    """,
    'roles': """
        CREATE TABLE roles (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name <PERSON>VA<PERSON>HA<PERSON>(50) NOT NULL,
            description NVARCHAR(200) NULL,
            permissions NVARCHAR(MAX) NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE()
        )
    """,
    'user_roles': """
        CREATE TABLE user_roles (
            user_id INT NOT NULL,
            role_id INT NOT NULL,
            PRIMARY KEY (user_id, role_id)
        )
    """,
    'administrative_areas': """
        CREATE TABLE administrative_areas (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            code NVARCHAR(50) NULL,
            level INT NOT NULL,
            parent_id INT NULL,
            status INT NOT NULL DEFAULT 1,
            is_township_school BIT NOT NULL DEFAULT 0,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
    """,
    'food_samples': """
        CREATE TABLE food_samples (
            id INT IDENTITY(1,1) PRIMARY KEY,
            sample_number NVARCHAR(50) NULL,
            recipe_id INT NOT NULL,
            area_id INT NULL,
            menu_plan_id INT NULL,
            meal_date DATE NULL,
            meal_type NVARCHAR(20) NULL,
            sample_image NVARCHAR(200) NOT NULL,
            sample_quantity FLOAT NULL,
            sample_unit NVARCHAR(20) NULL,
            storage_location NVARCHAR(100) NOT NULL,
            storage_temperature NVARCHAR(50) NULL,
            start_time DATETIME2 NOT NULL,
            end_time DATETIME2 NOT NULL,
            operator_id INT NULL,
            status NVARCHAR(20) NOT NULL DEFAULT '已留样',
            destruction_time DATETIME2 NULL,
            destruction_operator_id INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
    """,
    'purchase_orders': """
        CREATE TABLE purchase_orders (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_number NVARCHAR(50) NOT NULL,
            supplier_id INT NOT NULL,
            requisition_id INT NULL,
            area_id INT NOT NULL,
            total_amount DECIMAL(12, 2) NOT NULL,
            order_date DATETIME2 NOT NULL,
            expected_delivery_date DATE NULL,
            payment_terms NVARCHAR(200) NULL,
            delivery_terms NVARCHAR(200) NULL,
            status NVARCHAR(20) NOT NULL,
            delivery_date DATETIME2 NULL,
            created_by INT NOT NULL,
            approved_by INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
    """,
    'purchase_order_items': """
        CREATE TABLE purchase_order_items (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            ingredient_id INT NOT NULL,
            quantity FLOAT NOT NULL,
            unit NVARCHAR(20) NOT NULL,
            unit_price DECIMAL(10, 2) NOT NULL,
            total_price DECIMAL(10, 2) NOT NULL,
            received_quantity FLOAT NULL DEFAULT 0,
            notes NVARCHAR(MAX) NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
    """,
    'recipes': """
        CREATE TABLE recipes (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            category NVARCHAR(50) NULL,
            category_id INT NULL,
            meal_type NVARCHAR(20) NULL,
            main_image NVARCHAR(200) NULL,
            description NVARCHAR(MAX) NULL,
            calories INT NULL,
            nutrition_info NVARCHAR(MAX) NULL,
            cooking_method NVARCHAR(100) NULL,
            cooking_steps NVARCHAR(MAX) NULL,
            cooking_time INT NULL,
            serving_size INT NULL,
            status INT NOT NULL DEFAULT 1,
            created_by INT NOT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL,
            parent_id INT NULL,
            is_template BIT DEFAULT 0,
            template_type NVARCHAR(20) NULL,
            variation_reason NVARCHAR(200) NULL,
            version INT DEFAULT 1
        )
    """
}

# 列定义
COLUMN_DEFINITIONS = {
    'food_samples': {
        'operator_id': 'INT',
        'destruction_time': 'DATETIME2',
        'destruction_operator_id': 'INT'
    },
    'recipes': {
        'category': 'NVARCHAR(50)',
        'meal_type': 'NVARCHAR(20)',
        'main_image': 'NVARCHAR(200)',
        'calories': 'INT',
        'cooking_steps': 'NVARCHAR(MAX)',
        'parent_id': 'INT',
        'is_template': 'BIT DEFAULT 0',
        'template_type': 'NVARCHAR(20)',
        'variation_reason': 'NVARCHAR(200)',
        'version': 'INT DEFAULT 1'
    }
}

def log_auto_fix(operation, status, details=None):
    """记录自动修复操作日志"""
    app = create_app()
    log_dir = os.path.join(app.root_path, 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'auto_fix_{datetime.now().strftime("%Y%m%d")}.log')
    
    with open(log_file, 'a', encoding='utf-8') as f:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {operation} - {status}"
        if details:
            log_entry += f" - {details}"
        f.write(log_entry + "\n")

def check_and_fix_missing_tables():
    """检查并修复缺失的表"""
    app = create_app()
    fixed_tables = []
    
    with app.app_context():
        try:
            # 获取所有现有表
            result = db.session.execute(text("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"))
            existing_tables = [row[0].lower() for row in result.fetchall()]
            
            # 检查并创建缺失的表
            for table_name, create_sql in TABLE_DEFINITIONS.items():
                if table_name.lower() not in existing_tables:
                    try:
                        db.session.execute(text(create_sql))
                        fixed_tables.append(table_name)
                        log_auto_fix("check_and_fix_missing_tables", "成功", f"创建了表: {table_name}")
                    except Exception as e:
                        log_auto_fix("check_and_fix_missing_tables", "失败", f"创建表 {table_name} 时出错: {str(e)}")
                        print(f"创建表 {table_name} 时出错: {str(e)}")
            
            # 提交事务
            if fixed_tables:
                db.session.commit()
                print(f"已修复缺失的表: {', '.join(fixed_tables)}")
            else:
                print("所有必要的表都已存在")
            
            return fixed_tables
            
        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            log_auto_fix("check_and_fix_missing_tables", "失败", error_details)
            print(f"检查并修复缺失的表时出错: {str(e)}")
            return []

def check_and_fix_missing_columns():
    """检查并修复缺失的列"""
    app = create_app()
    fixed_columns = {}
    
    with app.app_context():
        try:
            # 检查并添加缺失的列
            for table_name, columns in COLUMN_DEFINITIONS.items():
                # 检查表是否存在
                result = db.session.execute(text(f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'"))
                if result.scalar() == 0:
                    print(f"表 {table_name} 不存在，无法添加列")
                    continue
                
                # 获取现有列
                result = db.session.execute(text(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}'"))
                existing_columns = [row[0].lower() for row in result.fetchall()]
                
                # 添加缺失的列
                table_fixed_columns = []
                for column_name, data_type in columns.items():
                    if column_name.lower() not in existing_columns:
                        try:
                            sql = f"ALTER TABLE {table_name} ADD {column_name} {data_type}"
                            db.session.execute(text(sql))
                            table_fixed_columns.append(f"{column_name} ({data_type})")
                            log_auto_fix("check_and_fix_missing_columns", "成功", f"在表 {table_name} 中添加了列: {column_name}")
                        except Exception as e:
                            log_auto_fix("check_and_fix_missing_columns", "失败", f"在表 {table_name} 中添加列 {column_name} 时出错: {str(e)}")
                            print(f"在表 {table_name} 中添加列 {column_name} 时出错: {str(e)}")
                
                if table_fixed_columns:
                    fixed_columns[table_name] = table_fixed_columns
            
            # 提交事务
            if fixed_columns:
                db.session.commit()
                for table_name, columns in fixed_columns.items():
                    print(f"已修复表 {table_name} 中缺失的列: {', '.join(columns)}")
            else:
                print("所有必要的列都已存在")
            
            return fixed_columns
            
        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            log_auto_fix("check_and_fix_missing_columns", "失败", error_details)
            print(f"检查并修复缺失的列时出错: {str(e)}")
            return {}

def auto_fix_all():
    """自动检测和修复所有问题"""
    print("开始自动修复...")
    
    # 1. 修复缺失的表
    fixed_tables = check_and_fix_missing_tables()
    
    # 2. 修复缺失的列
    fixed_columns = check_and_fix_missing_columns()
    
    # 返回修复结果
    return {
        'fixed_tables': fixed_tables,
        'fixed_columns': fixed_columns
    }

if __name__ == "__main__":
    auto_fix_all()
