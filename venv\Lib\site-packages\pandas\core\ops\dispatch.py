"""
Functions for defining unary operations.
"""
from __future__ import annotations

from typing import Any

from pandas._typing import <PERSON><PERSON>y<PERSON>ike

from pandas.core.dtypes.generic import ABCExtensionArray


def should_extension_dispatch(left: ArrayLike, right: Any) -> bool:
    """
    Identify cases where Series operation should dispatch to ExtensionArray method.

    Parameters
    ----------
    left : np.ndarray or ExtensionArray
    right : object

    Returns
    -------
    bool
    """
    return isinstance(left, ABCExtensionArray) or isinstance(right, ABCExtensionArray)
