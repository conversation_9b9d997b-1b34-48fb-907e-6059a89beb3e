{"version": 3, "file": "collapse.js", "sources": ["../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'collapse'\nconst VERSION             = '4.3.1'\nconst DATA_KEY            = 'bs.collapse'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst Default = {\n  toggle : true,\n  parent : ''\n}\n\nconst DefaultType = {\n  toggle : 'boolean',\n  parent : '(string|element)'\n}\n\nconst Event = {\n  SHOW           : `show${EVENT_KEY}`,\n  SHOWN          : `shown${EVENT_KEY}`,\n  HIDE           : `hide${EVENT_KEY}`,\n  HIDDEN         : `hidden${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  SHOW       : 'show',\n  COLLAPSE   : 'collapse',\n  COLLAPSING : 'collapsing',\n  COLLAPSED  : 'collapsed'\n}\n\nconst Dimension = {\n  WIDTH  : 'width',\n  HEIGHT : 'height'\n}\n\nconst Selector = {\n  ACTIVES     : '.show, .collapsing',\n  DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element         = element\n    this._config          = this._getConfig(config)\n    this._triggerArray    = [].slice.call(document.querySelectorAll(\n      `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n      `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n    ))\n\n    const toggleList = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = Util.getSelectorFromElement(elem)\n      const filterElement = [].slice.call(document.querySelectorAll(selector))\n        .filter((foundElem) => foundElem === element)\n\n      if (selector !== null && filterElement.length > 0) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if ($(this._element).hasClass(ClassName.SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      $(this._element).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = [].slice.call(this._parent.querySelectorAll(Selector.ACTIVES))\n        .filter((elem) => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(ClassName.COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    if (actives) {\n      activesData = $(actives).not(this._selector).data(DATA_KEY)\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = $.Event(Event.SHOW)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (actives) {\n      Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n      if (!activesData) {\n        $(actives).data(DATA_KEY, null)\n      }\n    }\n\n    const dimension = this._getDimension()\n\n    $(this._element)\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      $(this._triggerArray)\n        .removeClass(ClassName.COLLAPSED)\n        .attr('aria-expanded', true)\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      $(this._element)\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE)\n        .addClass(ClassName.SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      $(this._element).trigger(Event.SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !$(this._element).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const startEvent = $.Event(Event.HIDE)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    Util.reflow(this._element)\n\n    $(this._element)\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const selector = Util.getSelectorFromElement(trigger)\n\n        if (selector !== null) {\n          const $elem = $([].slice.call(document.querySelectorAll(selector)))\n          if (!$elem.hasClass(ClassName.SHOW)) {\n            $(trigger).addClass(ClassName.COLLAPSED)\n              .attr('aria-expanded', false)\n          }\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      $(this._element)\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE)\n        .trigger(Event.HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n\n    this._config          = null\n    this._parent          = null\n    this._element         = null\n    this._triggerArray    = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n    return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n  }\n\n  _getParent() {\n    let parent\n\n    if (Util.isElement(this._config.parent)) {\n      parent = this._config.parent\n\n      // It's a jQuery object\n      if (typeof this._config.parent.jquery !== 'undefined') {\n        parent = this._config.parent[0]\n      }\n    } else {\n      parent = document.querySelector(this._config.parent)\n    }\n\n    const selector =\n      `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n    const children = [].slice.call(parent.querySelectorAll(selector))\n    $(children).each((i, element) => {\n      this._addAriaAndCollapsedClass(\n        Collapse._getTargetFromElement(element),\n        [element]\n      )\n    })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    const isOpen = $(element).hasClass(ClassName.SHOW)\n\n    if (triggerArray.length) {\n      $(triggerArray)\n        .toggleClass(ClassName.COLLAPSED, !isOpen)\n        .attr('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n\n  static _getTargetFromElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    return selector ? document.querySelector(selector) : null\n  }\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this   = $(this)\n      let data      = $this.data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$this.data(),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data && _config.toggle && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      if (!data) {\n        data = new Collapse(this, _config)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.currentTarget.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const $trigger = $(this)\n  const selector = Util.getSelectorFromElement(this)\n  const selectors = [].slice.call(document.querySelectorAll(selector))\n\n  $(selectors).each(function () {\n    const $target = $(this)\n    const data    = $target.data(DATA_KEY)\n    const config  = data ? 'toggle' : $trigger.data()\n    Collapse._jQueryInterface.call($target, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Collapse._jQueryInterface\n$.fn[NAME].Constructor = Collapse\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Collapse._jQueryInterface\n}\n\nexport default Collapse\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "COLLAPSE", "COLLAPSING", "COLLAPSED", "Dimension", "WIDTH", "HEIGHT", "Selector", "ACTIVES", "DATA_TOGGLE", "Collapse", "element", "config", "_isTransitioning", "_element", "_config", "_getConfig", "_triggerArray", "slice", "call", "document", "querySelectorAll", "id", "toggleList", "i", "len", "length", "elem", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "filterElement", "filter", "foundElem", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hasClass", "hide", "show", "actives", "activesData", "getAttribute", "classList", "contains", "not", "data", "startEvent", "trigger", "isDefaultPrevented", "_jQueryInterface", "dimension", "_getDimension", "removeClass", "addClass", "style", "attr", "setTransitioning", "complete", "capitalizedDimension", "toUpperCase", "scrollSize", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "getBoundingClientRect", "reflow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$elem", "isTransitioning", "dispose", "removeData", "Boolean", "typeCheckConfig", "<PERSON><PERSON><PERSON><PERSON>", "isElement", "j<PERSON>y", "querySelector", "children", "each", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "toggleClass", "$this", "test", "TypeError", "on", "event", "currentTarget", "tagName", "preventDefault", "$trigger", "selectors", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAkB,UAA5B;EACA,IAAMC,OAAO,GAAe,OAA5B;EACA,IAAMC,QAAQ,GAAc,aAA5B;EACA,IAAMC,SAAS,SAAiBD,QAAhC;EACA,IAAME,YAAY,GAAU,WAA5B;EACA,IAAMC,kBAAkB,GAAIC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA5B;EAEA,IAAMQ,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAG,IADK;EAEdC,EAAAA,MAAM,EAAG;EAFK,CAAhB;EAKA,IAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAG,SADS;EAElBC,EAAAA,MAAM,EAAG;EAFS,CAApB;EAKA,IAAME,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAoBV,SADZ;EAEZW,EAAAA,KAAK,YAAoBX,SAFb;EAGZY,EAAAA,IAAI,WAAoBZ,SAHZ;EAIZa,EAAAA,MAAM,aAAoBb,SAJd;EAKZc,EAAAA,cAAc,YAAWd,SAAX,GAAuBC;EALzB,CAAd;EAQA,IAAMc,SAAS,GAAG;EAChBL,EAAAA,IAAI,EAAS,MADG;EAEhBM,EAAAA,QAAQ,EAAK,UAFG;EAGhBC,EAAAA,UAAU,EAAG,YAHG;EAIhBC,EAAAA,SAAS,EAAI;EAJG,CAAlB;EAOA,IAAMC,SAAS,GAAG;EAChBC,EAAAA,KAAK,EAAI,OADO;EAEhBC,EAAAA,MAAM,EAAG;EAFO,CAAlB;EAKA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,OAAO,EAAO,oBADC;EAEfC,EAAAA,WAAW,EAAG;EAGhB;;;;;;EALiB,CAAjB;;MAWMC;;;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,gBAAL,GAAwB,KAAxB;EACA,SAAKC,QAAL,GAAwBH,OAAxB;EACA,SAAKI,OAAL,GAAwB,KAAKC,UAAL,CAAgBJ,MAAhB,CAAxB;EACA,SAAKK,aAAL,GAAwB,GAAGC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CACpC,wCAAmCV,OAAO,CAACW,EAA3C,4DAC0CX,OAAO,CAACW,EADlD,SADoC,CAAd,CAAxB;EAKA,QAAMC,UAAU,GAAG,GAAGL,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0Bd,QAAQ,CAACE,WAAnC,CAAd,CAAnB;;EACA,SAAK,IAAIe,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,UAAU,CAACG,MAAjC,EAAyCF,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;EACrD,UAAMG,IAAI,GAAGJ,UAAU,CAACC,CAAD,CAAvB;EACA,UAAMI,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4BH,IAA5B,CAAjB;EACA,UAAMI,aAAa,GAAG,GAAGb,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,EACnBI,MADmB,CACZ,UAACC,SAAD;EAAA,eAAeA,SAAS,KAAKtB,OAA7B;EAAA,OADY,CAAtB;;EAGA,UAAIiB,QAAQ,KAAK,IAAb,IAAqBG,aAAa,CAACL,MAAd,GAAuB,CAAhD,EAAmD;EACjD,aAAKQ,SAAL,GAAiBN,QAAjB;;EACA,aAAKX,aAAL,CAAmBkB,IAAnB,CAAwBR,IAAxB;EACD;EACF;;EAED,SAAKS,OAAL,GAAe,KAAKrB,OAAL,CAAavB,MAAb,GAAsB,KAAK6C,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAKtB,OAAL,CAAavB,MAAlB,EAA0B;EACxB,WAAK8C,yBAAL,CAA+B,KAAKxB,QAApC,EAA8C,KAAKG,aAAnD;EACD;;EAED,QAAI,KAAKF,OAAL,CAAaxB,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF;;;;;EAYD;WAEAA,SAAA,kBAAS;EACP,QAAIH,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiByB,QAAjB,CAA0BvC,SAAS,CAACL,IAApC,CAAJ,EAA+C;EAC7C,WAAK6C,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;WAEDA,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK5B,gBAAL,IACFzB,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiByB,QAAjB,CAA0BvC,SAAS,CAACL,IAApC,CADF,EAC6C;EAC3C;EACD;;EAED,QAAI+C,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKP,OAAT,EAAkB;EAChBM,MAAAA,OAAO,GAAG,GAAGxB,KAAH,CAASC,IAAT,CAAc,KAAKiB,OAAL,CAAaf,gBAAb,CAA8Bd,QAAQ,CAACC,OAAvC,CAAd,EACPwB,MADO,CACA,UAACL,IAAD,EAAU;EAChB,YAAI,OAAO,KAAI,CAACZ,OAAL,CAAavB,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOmC,IAAI,CAACiB,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC7B,OAAL,CAAavB,MAAzD;EACD;;EAED,eAAOmC,IAAI,CAACkB,SAAL,CAAeC,QAAf,CAAwB9C,SAAS,CAACC,QAAlC,CAAP;EACD,OAPO,CAAV;;EASA,UAAIyC,OAAO,CAAChB,MAAR,KAAmB,CAAvB,EAA0B;EACxBgB,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,QAAIA,OAAJ,EAAa;EACXC,MAAAA,WAAW,GAAGvD,CAAC,CAACsD,OAAD,CAAD,CAAWK,GAAX,CAAe,KAAKb,SAApB,EAA+Bc,IAA/B,CAAoChE,QAApC,CAAd;;EACA,UAAI2D,WAAW,IAAIA,WAAW,CAAC9B,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,QAAMoC,UAAU,GAAG7D,CAAC,CAACM,KAAF,CAAQA,KAAK,CAACC,IAAd,CAAnB;EACAP,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiBoC,OAAjB,CAAyBD,UAAzB;;EACA,QAAIA,UAAU,CAACE,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAIT,OAAJ,EAAa;EACXhC,MAAAA,QAAQ,CAAC0C,gBAAT,CAA0BjC,IAA1B,CAA+B/B,CAAC,CAACsD,OAAD,CAAD,CAAWK,GAAX,CAAe,KAAKb,SAApB,CAA/B,EAA+D,MAA/D;;EACA,UAAI,CAACS,WAAL,EAAkB;EAChBvD,QAAAA,CAAC,CAACsD,OAAD,CAAD,CAAWM,IAAX,CAAgBhE,QAAhB,EAA0B,IAA1B;EACD;EACF;;EAED,QAAMqE,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEAlE,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CACGyC,WADH,CACevD,SAAS,CAACC,QADzB,EAEGuD,QAFH,CAEYxD,SAAS,CAACE,UAFtB;EAIA,SAAKY,QAAL,CAAc2C,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKpC,aAAL,CAAmBS,MAAvB,EAA+B;EAC7BtC,MAAAA,CAAC,CAAC,KAAK6B,aAAN,CAAD,CACGsC,WADH,CACevD,SAAS,CAACG,SADzB,EAEGuD,IAFH,CAEQ,eAFR,EAEyB,IAFzB;EAGD;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBxE,MAAAA,CAAC,CAAC,KAAI,CAAC0B,QAAN,CAAD,CACGyC,WADH,CACevD,SAAS,CAACE,UADzB,EAEGsD,QAFH,CAEYxD,SAAS,CAACC,QAFtB,EAGGuD,QAHH,CAGYxD,SAAS,CAACL,IAHtB;EAKA,MAAA,KAAI,CAACmB,QAAL,CAAc2C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;EAEA,MAAA,KAAI,CAACM,gBAAL,CAAsB,KAAtB;;EAEAvE,MAAAA,CAAC,CAAC,KAAI,CAAC0B,QAAN,CAAD,CAAiBoC,OAAjB,CAAyBxD,KAAK,CAACE,KAA/B;EACD,KAXD;;EAaA,QAAMiE,oBAAoB,GAAGR,SAAS,CAAC,CAAD,CAAT,CAAaS,WAAb,KAA6BT,SAAS,CAACnC,KAAV,CAAgB,CAAhB,CAA1D;EACA,QAAM6C,UAAU,cAAYF,oBAA5B;EACA,QAAMG,kBAAkB,GAAGnC,IAAI,CAACoC,gCAAL,CAAsC,KAAKnD,QAA3C,CAA3B;EAEA1B,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CACGoD,GADH,CACOrC,IAAI,CAACsC,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBJ,kBAFxB;EAIA,SAAKlD,QAAL,CAAc2C,KAAd,CAAoBJ,SAApB,IAAoC,KAAKvC,QAAL,CAAciD,UAAd,CAApC;EACD;;WAEDvB,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK3B,gBAAL,IACF,CAACzB,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiByB,QAAjB,CAA0BvC,SAAS,CAACL,IAApC,CADH,EAC8C;EAC5C;EACD;;EAED,QAAMsD,UAAU,GAAG7D,CAAC,CAACM,KAAF,CAAQA,KAAK,CAACG,IAAd,CAAnB;EACAT,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiBoC,OAAjB,CAAyBD,UAAzB;;EACA,QAAIA,UAAU,CAACE,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAME,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKxC,QAAL,CAAc2C,KAAd,CAAoBJ,SAApB,IAAoC,KAAKvC,QAAL,CAAcuD,qBAAd,GAAsChB,SAAtC,CAApC;EAEAxB,IAAAA,IAAI,CAACyC,MAAL,CAAY,KAAKxD,QAAjB;EAEA1B,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CACG0C,QADH,CACYxD,SAAS,CAACE,UADtB,EAEGqD,WAFH,CAEevD,SAAS,CAACC,QAFzB,EAGGsD,WAHH,CAGevD,SAAS,CAACL,IAHzB;EAKA,QAAM4E,kBAAkB,GAAG,KAAKtD,aAAL,CAAmBS,MAA9C;;EACA,QAAI6C,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAI/C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+C,kBAApB,EAAwC/C,CAAC,EAAzC,EAA6C;EAC3C,YAAM0B,OAAO,GAAG,KAAKjC,aAAL,CAAmBO,CAAnB,CAAhB;EACA,YAAMI,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4BoB,OAA5B,CAAjB;;EAEA,YAAItB,QAAQ,KAAK,IAAjB,EAAuB;EACrB,cAAM4C,KAAK,GAAGpF,CAAC,CAAC,GAAG8B,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAD,CAAf;;EACA,cAAI,CAAC4C,KAAK,CAACjC,QAAN,CAAevC,SAAS,CAACL,IAAzB,CAAL,EAAqC;EACnCP,YAAAA,CAAC,CAAC8D,OAAD,CAAD,CAAWM,QAAX,CAAoBxD,SAAS,CAACG,SAA9B,EACGuD,IADH,CACQ,eADR,EACyB,KADzB;EAED;EACF;EACF;EACF;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;EACAvE,MAAAA,CAAC,CAAC,MAAI,CAAC0B,QAAN,CAAD,CACGyC,WADH,CACevD,SAAS,CAACE,UADzB,EAEGsD,QAFH,CAEYxD,SAAS,CAACC,QAFtB,EAGGiD,OAHH,CAGWxD,KAAK,CAACI,MAHjB;EAID,KAND;;EAQA,SAAKgB,QAAL,CAAc2C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;EACA,QAAMW,kBAAkB,GAAGnC,IAAI,CAACoC,gCAAL,CAAsC,KAAKnD,QAA3C,CAA3B;EAEA1B,IAAAA,CAAC,CAAC,KAAK0B,QAAN,CAAD,CACGoD,GADH,CACOrC,IAAI,CAACsC,cADZ,EAC4BP,QAD5B,EAEGQ,oBAFH,CAEwBJ,kBAFxB;EAGD;;WAEDL,mBAAA,0BAAiBc,eAAjB,EAAkC;EAChC,SAAK5D,gBAAL,GAAwB4D,eAAxB;EACD;;WAEDC,UAAA,mBAAU;EACRtF,IAAAA,CAAC,CAACuF,UAAF,CAAa,KAAK7D,QAAlB,EAA4B9B,QAA5B;EAEA,SAAK+B,OAAL,GAAwB,IAAxB;EACA,SAAKqB,OAAL,GAAwB,IAAxB;EACA,SAAKtB,QAAL,GAAwB,IAAxB;EACA,SAAKG,aAAL,GAAwB,IAAxB;EACA,SAAKJ,gBAAL,GAAwB,IAAxB;EACD;;;WAIDG,aAAA,oBAAWJ,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qBACDtB,OADC,EAEDsB,MAFC,CAAN;EAIAA,IAAAA,MAAM,CAACrB,MAAP,GAAgBqF,OAAO,CAAChE,MAAM,CAACrB,MAAR,CAAvB,CALiB;;EAMjBsC,IAAAA,IAAI,CAACgD,eAAL,CAAqB/F,IAArB,EAA2B8B,MAA3B,EAAmCnB,WAAnC;EACA,WAAOmB,MAAP;EACD;;WAED0C,gBAAA,yBAAgB;EACd,QAAMwB,QAAQ,GAAG1F,CAAC,CAAC,KAAK0B,QAAN,CAAD,CAAiByB,QAAjB,CAA0BnC,SAAS,CAACC,KAApC,CAAjB;EACA,WAAOyE,QAAQ,GAAG1E,SAAS,CAACC,KAAb,GAAqBD,SAAS,CAACE,MAA9C;EACD;;WAED+B,aAAA,sBAAa;EAAA;;EACX,QAAI7C,MAAJ;;EAEA,QAAIqC,IAAI,CAACkD,SAAL,CAAe,KAAKhE,OAAL,CAAavB,MAA5B,CAAJ,EAAyC;EACvCA,MAAAA,MAAM,GAAG,KAAKuB,OAAL,CAAavB,MAAtB,CADuC;;EAIvC,UAAI,OAAO,KAAKuB,OAAL,CAAavB,MAAb,CAAoBwF,MAA3B,KAAsC,WAA1C,EAAuD;EACrDxF,QAAAA,MAAM,GAAG,KAAKuB,OAAL,CAAavB,MAAb,CAAoB,CAApB,CAAT;EACD;EACF,KAPD,MAOO;EACLA,MAAAA,MAAM,GAAG4B,QAAQ,CAAC6D,aAAT,CAAuB,KAAKlE,OAAL,CAAavB,MAApC,CAAT;EACD;;EAED,QAAMoC,QAAQ,iDAC6B,KAAKb,OAAL,CAAavB,MAD1C,QAAd;EAGA,QAAM0F,QAAQ,GAAG,GAAGhE,KAAH,CAASC,IAAT,CAAc3B,MAAM,CAAC6B,gBAAP,CAAwBO,QAAxB,CAAd,CAAjB;EACAxC,IAAAA,CAAC,CAAC8F,QAAD,CAAD,CAAYC,IAAZ,CAAiB,UAAC3D,CAAD,EAAIb,OAAJ,EAAgB;EAC/B,MAAA,MAAI,CAAC2B,yBAAL,CACE5B,QAAQ,CAAC0E,qBAAT,CAA+BzE,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF;EAID,KALD;EAOA,WAAOnB,MAAP;EACD;;WAED8C,4BAAA,mCAA0B3B,OAA1B,EAAmC0E,YAAnC,EAAiD;EAC/C,QAAMC,MAAM,GAAGlG,CAAC,CAACuB,OAAD,CAAD,CAAW4B,QAAX,CAAoBvC,SAAS,CAACL,IAA9B,CAAf;;EAEA,QAAI0F,YAAY,CAAC3D,MAAjB,EAAyB;EACvBtC,MAAAA,CAAC,CAACiG,YAAD,CAAD,CACGE,WADH,CACevF,SAAS,CAACG,SADzB,EACoC,CAACmF,MADrC,EAEG5B,IAFH,CAEQ,eAFR,EAEyB4B,MAFzB;EAGD;EACF;;;aAIMF,wBAAP,+BAA6BzE,OAA7B,EAAsC;EACpC,QAAMiB,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4BnB,OAA5B,CAAjB;EACA,WAAOiB,QAAQ,GAAGR,QAAQ,CAAC6D,aAAT,CAAuBrD,QAAvB,CAAH,GAAsC,IAArD;EACD;;aAEMwB,mBAAP,0BAAwBxC,MAAxB,EAAgC;EAC9B,WAAO,KAAKuE,IAAL,CAAU,YAAY;EAC3B,UAAMK,KAAK,GAAKpG,CAAC,CAAC,IAAD,CAAjB;EACA,UAAI4D,IAAI,GAAQwC,KAAK,CAACxC,IAAN,CAAWhE,QAAX,CAAhB;;EACA,UAAM+B,OAAO,qBACRzB,OADQ,EAERkG,KAAK,CAACxC,IAAN,EAFQ,EAGR,OAAOpC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;EAMA,UAAI,CAACoC,IAAD,IAASjC,OAAO,CAACxB,MAAjB,IAA2B,YAAYkG,IAAZ,CAAiB7E,MAAjB,CAA/B,EAAyD;EACvDG,QAAAA,OAAO,CAACxB,MAAR,GAAiB,KAAjB;EACD;;EAED,UAAI,CAACyD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItC,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP;EACAyE,QAAAA,KAAK,CAACxC,IAAN,CAAWhE,QAAX,EAAqBgE,IAArB;EACD;;EAED,UAAI,OAAOpC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOoC,IAAI,CAACpC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAI8E,SAAJ,wBAAkC9E,MAAlC,QAAN;EACD;;EACDoC,QAAAA,IAAI,CAACpC,MAAD,CAAJ;EACD;EACF,KAxBM,CAAP;EAyBD;;;;0BArQoB;EACnB,aAAO7B,OAAP;EACD;;;0BAEoB;EACnB,aAAOO,OAAP;EACD;;;;;EAkQH;;;;;;;EAMAF,CAAC,CAACgC,QAAD,CAAD,CAAYuE,EAAZ,CAAejG,KAAK,CAACK,cAArB,EAAqCQ,QAAQ,CAACE,WAA9C,EAA2D,UAAUmF,KAAV,EAAiB;EAC1E;EACA,MAAIA,KAAK,CAACC,aAAN,CAAoBC,OAApB,KAAgC,GAApC,EAAyC;EACvCF,IAAAA,KAAK,CAACG,cAAN;EACD;;EAED,MAAMC,QAAQ,GAAG5G,CAAC,CAAC,IAAD,CAAlB;EACA,MAAMwC,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4B,IAA5B,CAAjB;EACA,MAAMmE,SAAS,GAAG,GAAG/E,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAlB;EAEAxC,EAAAA,CAAC,CAAC6G,SAAD,CAAD,CAAad,IAAb,CAAkB,YAAY;EAC5B,QAAMe,OAAO,GAAG9G,CAAC,CAAC,IAAD,CAAjB;EACA,QAAM4D,IAAI,GAAMkD,OAAO,CAAClD,IAAR,CAAahE,QAAb,CAAhB;EACA,QAAM4B,MAAM,GAAIoC,IAAI,GAAG,QAAH,GAAcgD,QAAQ,CAAChD,IAAT,EAAlC;;EACAtC,IAAAA,QAAQ,CAAC0C,gBAAT,CAA0BjC,IAA1B,CAA+B+E,OAA/B,EAAwCtF,MAAxC;EACD,GALD;EAMD,CAhBD;EAkBA;;;;;;EAMAxB,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAa4B,QAAQ,CAAC0C,gBAAtB;EACAhE,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWqH,WAAX,GAAyBzF,QAAzB;;EACAtB,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWsH,UAAX,GAAwB,YAAM;EAC5BhH,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOuB,QAAQ,CAAC0C,gBAAhB;EACD,CAHD;;;;;;;;"}