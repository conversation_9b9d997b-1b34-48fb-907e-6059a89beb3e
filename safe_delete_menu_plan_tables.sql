-- 安全删除 MenuPlan 和 MenuRecipe 表的 SQL 脚本
-- 基于检查结果，menu_plans 表为空，可以安全删除

USE [StudentsCMSSP]
GO

PRINT '🚀 开始安全删除 MenuPlan 和 MenuRecipe 相关表...'
PRINT '================================================'
PRINT ''

-- 第一步：检查表的当前状态
PRINT '第一步：检查表的当前状态'
PRINT '------------------------'

-- 检查 menu_plans 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    DECLARE @menu_plans_count INT
    SELECT @menu_plans_count = COUNT(*) FROM menu_plans
    PRINT '✓ menu_plans 表存在，记录数：' + CAST(@menu_plans_count AS VARCHAR(10))
    
    IF @menu_plans_count > 0
    BEGIN
        PRINT '⚠️  警告：menu_plans 表中有数据，建议先备份！'
        PRINT '如果确定要删除，请手动清空表后重新运行此脚本'
        RETURN
    END
    ELSE
    BEGIN
        PRINT '✓ menu_plans 表为空，可以安全删除'
    END
END
ELSE
BEGIN
    PRINT '- menu_plans 表不存在'
END

-- 检查 menu_recipes 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
BEGIN
    DECLARE @menu_recipes_count INT
    SELECT @menu_recipes_count = COUNT(*) FROM menu_recipes
    PRINT '✓ menu_recipes 表存在，记录数：' + CAST(@menu_recipes_count AS VARCHAR(10))
    
    IF @menu_recipes_count > 0
    BEGIN
        PRINT '⚠️  警告：menu_recipes 表中有数据，建议先备份！'
        PRINT '如果确定要删除，请手动清空表后重新运行此脚本'
        RETURN
    END
    ELSE
    BEGIN
        PRINT '✓ menu_recipes 表为空，可以安全删除'
    END
END
ELSE
BEGIN
    PRINT '- menu_recipes 表不存在'
END

PRINT ''

-- 第二步：移除外键约束
PRINT '第二步：移除外键约束'
PRINT '--------------------'

-- 移除 consumption_plans 表中的 menu_plan_id 外键约束
DECLARE @fk_name NVARCHAR(128)

-- 查找 consumption_plans.menu_plan_id 的外键约束名
SELECT @fk_name = fk.name
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
WHERE fkc.parent_object_id = OBJECT_ID('consumption_plans')
AND c.name = 'menu_plan_id'

IF @fk_name IS NOT NULL
BEGIN
    EXEC('ALTER TABLE consumption_plans DROP CONSTRAINT ' + @fk_name)
    PRINT '✓ 已移除 consumption_plans.menu_plan_id 外键约束：' + @fk_name
END
ELSE
BEGIN
    PRINT '- consumption_plans.menu_plan_id 外键约束不存在'
END

-- 移除 food_samples 表中的 menu_plan_id 外键约束
SET @fk_name = NULL
SELECT @fk_name = fk.name
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns c ON fkc.parent_object_id = c.object_id AND fkc.parent_column_id = c.column_id
WHERE fkc.parent_object_id = OBJECT_ID('food_samples')
AND c.name = 'menu_plan_id'

IF @fk_name IS NOT NULL
BEGIN
    EXEC('ALTER TABLE food_samples DROP CONSTRAINT ' + @fk_name)
    PRINT '✓ 已移除 food_samples.menu_plan_id 外键约束：' + @fk_name
END
ELSE
BEGIN
    PRINT '- food_samples.menu_plan_id 外键约束不存在'
END

-- 移除 menu_recipes 表的外键约束
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
BEGIN
    -- 移除所有 menu_recipes 表的外键约束
    DECLARE fk_cursor CURSOR FOR
    SELECT name FROM sys.foreign_keys WHERE parent_object_id = OBJECT_ID('menu_recipes')
    
    OPEN fk_cursor
    FETCH NEXT FROM fk_cursor INTO @fk_name
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC('ALTER TABLE menu_recipes DROP CONSTRAINT ' + @fk_name)
        PRINT '✓ 已移除 menu_recipes 外键约束：' + @fk_name
        FETCH NEXT FROM fk_cursor INTO @fk_name
    END
    
    CLOSE fk_cursor
    DEALLOCATE fk_cursor
END

-- 移除 menu_plans 表的外键约束
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    -- 移除所有 menu_plans 表的外键约束
    DECLARE fk_cursor2 CURSOR FOR
    SELECT name FROM sys.foreign_keys WHERE parent_object_id = OBJECT_ID('menu_plans')
    
    OPEN fk_cursor2
    FETCH NEXT FROM fk_cursor2 INTO @fk_name
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC('ALTER TABLE menu_plans DROP CONSTRAINT ' + @fk_name)
        PRINT '✓ 已移除 menu_plans 外键约束：' + @fk_name
        FETCH NEXT FROM fk_cursor2 INTO @fk_name
    END
    
    CLOSE fk_cursor2
    DEALLOCATE fk_cursor2
END

PRINT ''

-- 第三步：删除索引
PRINT '第三步：删除相关索引'
PRINT '------------------'

-- 删除 menu_plans 相关索引
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    DECLARE idx_cursor CURSOR FOR
    SELECT name FROM sys.indexes 
    WHERE object_id = OBJECT_ID('menu_plans') 
    AND name IS NOT NULL 
    AND is_primary_key = 0 
    AND is_unique_constraint = 0
    
    DECLARE @idx_name NVARCHAR(128)
    OPEN idx_cursor
    FETCH NEXT FROM idx_cursor INTO @idx_name
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC('DROP INDEX ' + @idx_name + ' ON menu_plans')
        PRINT '✓ 已删除 menu_plans 索引：' + @idx_name
        FETCH NEXT FROM idx_cursor INTO @idx_name
    END
    
    CLOSE idx_cursor
    DEALLOCATE idx_cursor
END

-- 删除 menu_recipes 相关索引
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
BEGIN
    DECLARE idx_cursor2 CURSOR FOR
    SELECT name FROM sys.indexes 
    WHERE object_id = OBJECT_ID('menu_recipes') 
    AND name IS NOT NULL 
    AND is_primary_key = 0 
    AND is_unique_constraint = 0
    
    OPEN idx_cursor2
    FETCH NEXT FROM idx_cursor2 INTO @idx_name
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC('DROP INDEX ' + @idx_name + ' ON menu_recipes')
        PRINT '✓ 已删除 menu_recipes 索引：' + @idx_name
        FETCH NEXT FROM idx_cursor2 INTO @idx_name
    END
    
    CLOSE idx_cursor2
    DEALLOCATE idx_cursor2
END

PRINT ''

-- 第四步：删除表
PRINT '第四步：删除表'
PRINT '------------'

-- 删除 menu_recipes 表（子表先删除）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
BEGIN
    DROP TABLE menu_recipes
    PRINT '✓ 已删除 menu_recipes 表'
END
ELSE
BEGIN
    PRINT '- menu_recipes 表不存在'
END

-- 删除 menu_plans 表（主表后删除）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    DROP TABLE menu_plans
    PRINT '✓ 已删除 menu_plans 表'
END
ELSE
BEGIN
    PRINT '- menu_plans 表不存在'
END

PRINT ''

-- 第五步：验证删除结果
PRINT '第五步：验证删除结果'
PRINT '------------------'

-- 检查表是否已删除
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
    PRINT '✓ menu_plans 表已成功删除'
ELSE
    PRINT '❌ menu_plans 表删除失败'

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_recipes')
    PRINT '✓ menu_recipes 表已成功删除'
ELSE
    PRINT '❌ menu_recipes 表删除失败'

-- 检查 consumption_plans 和 food_samples 表中的 menu_plan_id 字段状态
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
    PRINT '- consumption_plans.menu_plan_id 字段仍然存在（用于历史数据兼容）'
ELSE
    PRINT '- consumption_plans.menu_plan_id 字段不存在'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('food_samples') AND name = 'menu_plan_id')
    PRINT '- food_samples.menu_plan_id 字段仍然存在（用于历史数据兼容）'
ELSE
    PRINT '- food_samples.menu_plan_id 字段不存在'

PRINT ''
PRINT '🎉 MenuPlan 和 MenuRecipe 表删除完成！'
PRINT ''
PRINT '📋 删除总结：'
PRINT '• 已删除 menu_plans 表'
PRINT '• 已删除 menu_recipes 表'
PRINT '• 已移除相关外键约束'
PRINT '• 已删除相关索引'
PRINT '• 保留了其他表中的 menu_plan_id 字段用于历史数据兼容'
PRINT ''
PRINT '✅ 系统现在完全依赖 WeeklyMenu + WeeklyMenuRecipe 进行菜单管理'
PRINT '✅ 消耗计划创建现在基于周菜单数据'
PRINT '✅ 所有功能应该正常工作'

GO
