{% import 'admin/static.html' as admin_static with context%}
{% import 'admin/lib.html' as lib with context %}

{# store the jinja2 context for form_rules rendering logic #}
{% set render_ctx = h.resolve_ctx() %}

{% block body %}
  {# "save and add" button is removed from modal (it won't function properly) #}
  {% block create_form %}
    {{ lib.render_form(form, return_url, extra=None, form_opts=form_opts,
                       action=url_for('.create_view', url=return_url),
                       is_modal=True) }}
  {% endblock %}
{% endblock %}

{% block tail %}
  <script src="{{ admin_static.url(filename='admin/js/bs2_modal.js', v='1.0.0') }}"></script>

  <script>
  // fill the header of modal dynamically
  $('.modal-header h3').html('{% block header_text -%}
    <h3>{{ _gettext('Create New Record') }}</h3>
  {%- endblock %}');
  </script>
{% endblock %}
