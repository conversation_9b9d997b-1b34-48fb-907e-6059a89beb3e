# 财务系统部署指南

## 🚀 快速部署

### 方法一：一键部署（推荐）

```bash
# 运行一键部署脚本
python deploy_financial_system.py
```

### 方法二：分步部署

#### 步骤1：创建数据库表结构
```sql
-- 在SQL Server中执行
sqlcmd -S [服务器名] -d [数据库名] -i migrations/financial_system_init.sql
```

#### 步骤2：初始化基础会计科目
```sql
-- 为所有学校创建标准会计科目
sqlcmd -S [服务器名] -d [数据库名] -i migrations/financial_basic_subjects.sql
```

#### 步骤3：设置模块可见性
```sql
-- 方式A：使用SQL脚本（推荐）
sqlcmd -S [服务器名] -d [数据库名] -i migrations/financial_module_visibility_init.sql

-- 方式B：使用Python脚本
python migrations/financial_module_visibility_init.py
```

#### 步骤4：初始化财务权限
```bash
# 为现有角色添加财务权限
python migrations/financial_permissions_init.py
```

#### 步骤5：验证部署
```bash
# 运行系统验证
python test_financial_system.py
```

## 📋 部署后配置

### 1. 模块可见性管理

访问：**后台管理 → 系统 → 模块可见性管理**

财务系统包含以下模块：
- `financial` - 财务管理主模块
- `financial_overview` - 财务概览
- `accounting_subjects` - 会计科目
- `financial_vouchers` - 财务凭证
- `account_payables` - 应付账款
- `payment_records` - 付款记录
- `pending_stock_ins` - 待处理入库
- `balance_sheet` - 资产负债表
- `cost_analysis` - 成本分析表
- `payables_aging` - 账龄分析

**建议设置：**
- **管理员角色**：所有财务模块可见
- **财务角色**：所有财务模块可见
- **会计角色**：除管理功能外的财务模块可见
- **出纳角色**：付款相关模块可见
- **采购角色**：应付账款相关模块可见
- **管理层角色**：财务报表模块可见

### 2. 角色权限管理

访问：**后台管理 → 系统 → 角色权限管理**

财务系统权限模块：
- **财务管理** - 基础财务权限
- **会计科目管理** - 科目设置权限
- **财务凭证管理** - 凭证操作权限
- **应付账款管理** - 账款管理权限
- **收入管理** - 收入管理权限
- **成本核算** - 成本核算权限
- **财务报表** - 报表查看权限

**权限操作说明：**
- `view` - 查看权限
- `create` - 创建权限
- `edit` - 编辑权限
- `delete` - 删除权限
- `approve` - 审批权限
- `review` - 审核权限
- `payment` - 付款权限
- `export` - 导出权限

### 3. 用户角色分配

为用户分配合适的角色：
- **财务主管** → 财务管理员角色
- **会计人员** → 会计角色
- **出纳人员** → 出纳角色
- **采购人员** → 采购角色
- **校长/主任** → 管理层角色

## 🔧 系统配置

### 1. 会计科目设置

访问：**财务管理 → 会计科目**

系统已预设标准会计科目体系：
- **资产类 (1xxx)** - 现金、银行存款、原材料等
- **负债类 (2xxx)** - 应付账款、应付职工薪酬等
- **所有者权益类 (3xxx)** - 实收资本、本年利润等
- **收入类 (6xxx)** - 主营业务收入等
- **费用类 (6xxx)** - 主营业务成本、管理费用等

可根据实际需要添加自定义科目。

### 2. 财务流程设置

#### 入库到付款流程：
1. **采购订单** → 创建采购需求
2. **入库确认** → 确认货物入库
3. **财务确认** → 财务人员确认入库单
4. **生成应付账款** → 系统自动生成应付账款
5. **付款记录** → 记录实际付款
6. **更新账款状态** → 自动更新应付账款余额

#### 凭证处理流程：
1. **创建凭证** → 录入凭证基本信息
2. **录入明细** → 添加借贷明细
3. **借贷平衡检查** → 系统自动验证
4. **提交审核** → 提交给审核人员
5. **审核通过** → 审核人员确认
6. **记账** → 正式记账

## 📊 功能使用

### 1. 财务概览
- 访问：`/financial/reports`
- 功能：查看财务概况、快速操作

### 2. 会计科目管理
- 访问：`/financial/accounting-subjects`
- 功能：管理会计科目体系

### 3. 财务凭证管理
- 访问：`/financial/vouchers`
- 功能：创建、审核、管理财务凭证

### 4. 应付账款管理
- 访问：`/financial/payables`
- 功能：管理供应商应付账款

### 5. 付款记录管理
- 访问：`/financial/payments`
- 功能：记录和跟踪付款

### 6. 财务报表
- 资产负债表：`/financial/balance-sheet`
- 利润表：`/financial/income-statement`
- 账龄分析：`/financial/payables-aging`

## 🔍 故障排除

### 常见问题

1. **财务菜单不显示**
   - 检查用户角色是否有财务模块可见性
   - 确认用户是否有财务相关权限

2. **无法创建财务记录**
   - 检查用户是否有相应的创建权限
   - 确认会计科目是否已正确设置

3. **应付账款无法生成**
   - 确认入库单是否已财务确认
   - 检查供应商信息是否完整

4. **凭证无法审核**
   - 检查借贷是否平衡
   - 确认用户是否有审核权限

### 数据检查

```sql
-- 检查财务表是否存在
SELECT name FROM sys.tables WHERE name LIKE '%financial%' OR name LIKE '%accounting%';

-- 检查会计科目数量
SELECT area_id, COUNT(*) as subject_count 
FROM accounting_subjects 
GROUP BY area_id;

-- 检查模块可见性设置
SELECT r.name, mv.module_id, mv.is_visible 
FROM roles r 
LEFT JOIN module_visibility mv ON r.id = mv.role_id 
WHERE mv.module_id LIKE '%financial%';
```

## 📞 技术支持

如遇问题，请提供：
1. 错误信息截图
2. 用户角色和权限信息
3. 操作步骤描述
4. 系统日志相关内容

---

**部署完成后，建议先在测试环境验证所有功能正常，再在生产环境使用。**
