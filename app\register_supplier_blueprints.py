"""
注册供应商管理模块的蓝图
"""

def register_supplier_blueprints(app):
    """注册供应商管理模块的蓝图"""

    # 供应商基本信息管理
    from app.routes.supplier import supplier_bp
    app.register_blueprint(supplier_bp, url_prefix='/supplier')

    # 供应商分类管理
    from app.routes.supplier_category import supplier_category_bp
    app.register_blueprint(supplier_category_bp, url_prefix='/supplier-category')

    # 供应商产品管理
    from app.routes.supplier_product import supplier_product_bp
    app.register_blueprint(supplier_product_bp, url_prefix='/supplier-product')

    # 供应商证书管理
    from app.routes.supplier_certificate import supplier_certificate_bp
    app.register_blueprint(supplier_certificate_bp, url_prefix='/supplier-certificate')

    # 供应商-学校关系管理
    from app.routes.supplier_school import supplier_school_bp
    app.register_blueprint(supplier_school_bp, url_prefix='/supplier-school')

    # 产品批量上架管理
    from app.routes.product_batch import product_batch_bp
    app.register_blueprint(product_batch_bp, url_prefix='/product-batch')
