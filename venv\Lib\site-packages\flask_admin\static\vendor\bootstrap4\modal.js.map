{"version": 3, "file": "modal.js", "sources": ["../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'modal'\nconst VERSION            = '4.3.1'\nconst DATA_KEY           = 'bs.modal'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE     = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst Default = {\n  backdrop : true,\n  keyboard : true,\n  focus    : true,\n  show     : true\n}\n\nconst DefaultType = {\n  backdrop : '(boolean|string)',\n  keyboard : 'boolean',\n  focus    : 'boolean',\n  show     : 'boolean'\n}\n\nconst Event = {\n  HIDE              : `hide${EVENT_KEY}`,\n  HIDDEN            : `hidden${EVENT_KEY}`,\n  SHOW              : `show${EVENT_KEY}`,\n  SHOWN             : `shown${EVENT_KEY}`,\n  FOCUSIN           : `focusin${EVENT_KEY}`,\n  RESIZE            : `resize${EVENT_KEY}`,\n  CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n  KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n  MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n  MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n  CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  SCROLLABLE         : 'modal-dialog-scrollable',\n  SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n  BACKDROP           : 'modal-backdrop',\n  OPEN               : 'modal-open',\n  FADE               : 'fade',\n  SHOW               : 'show'\n}\n\nconst Selector = {\n  DIALOG         : '.modal-dialog',\n  MODAL_BODY     : '.modal-body',\n  DATA_TOGGLE    : '[data-toggle=\"modal\"]',\n  DATA_DISMISS   : '[data-dismiss=\"modal\"]',\n  FIXED_CONTENT  : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n  STICKY_CONTENT : '.sticky-top'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config              = this._getConfig(config)\n    this._element             = element\n    this._dialog              = element.querySelector(Selector.DIALOG)\n    this._backdrop            = null\n    this._isShown             = false\n    this._isBodyOverflowing   = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning     = false\n    this._scrollbarWidth      = 0\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if ($(this._element).hasClass(ClassName.FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = $.Event(Event.SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (this._isShown || showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      (event) => this.hide(event)\n    )\n\n    $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(Event.HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(ClassName.FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(Event.FOCUSIN)\n\n    $(this._element).removeClass(ClassName.SHOW)\n\n    $(this._element).off(Event.CLICK_DISMISS)\n    $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach((htmlElement) => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `Event.FOCUSIN` and `Event.CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `Event.CLICK_DATA_API` event that should remain\n     */\n    $(document).off(Event.FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config              = null\n    this._element             = null\n    this._dialog              = null\n    this._backdrop            = null\n    this._isShown             = null\n    this._isBodyOverflowing   = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning     = null\n    this._scrollbarWidth      = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(ClassName.FADE)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n\n    if ($(this._dialog).hasClass(ClassName.SCROLLABLE)) {\n      this._dialog.querySelector(Selector.MODAL_BODY).scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(ClassName.SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(Event.SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(Event.FOCUSIN) // Guard against infinite focus loop\n      .on(Event.FOCUSIN, (event) => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown && this._config.keyboard) {\n      $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n        if (event.which === ESCAPE_KEYCODE) {\n          event.preventDefault()\n          this.hide()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(Event.KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n    } else {\n      $(window).off(Event.RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(ClassName.OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(Event.HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(ClassName.FADE)\n      ? ClassName.FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = ClassName.BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(Event.CLICK_DISMISS, (event) => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n        if (event.target !== event.currentTarget) {\n          return\n        }\n        if (this._config.backdrop === 'static') {\n          this._element.focus()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(ClassName.SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(ClassName.SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(ClassName.FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(Selector.STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(ClassName.OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${Selector.STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY)\n    ? 'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(Event.SHOW, (showEvent) => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(Event.HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "FOCUSIN", "RESIZE", "CLICK_DISMISS", "KEYDOWN_DISMISS", "MOUSEUP_DISMISS", "MOUSEDOWN_DISMISS", "CLICK_DATA_API", "ClassName", "SCROLLABLE", "SCROLLBAR_MEASURER", "BACKDROP", "OPEN", "FADE", "Selector", "DIALOG", "MODAL_BODY", "DATA_TOGGLE", "DATA_DISMISS", "FIXED_CONTENT", "STICKY_CONTENT", "Modal", "element", "config", "_config", "_getConfig", "_element", "_dialog", "querySelector", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_scrollbarWidth", "toggle", "relatedTarget", "hide", "hasClass", "showEvent", "trigger", "isDefaultPrevented", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "hideEvent", "transition", "document", "off", "removeClass", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "window", "for<PERSON>ach", "htmlElement", "removeData", "handleUpdate", "typeCheckConfig", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "body", "append<PERSON><PERSON><PERSON>", "style", "display", "removeAttribute", "setAttribute", "scrollTop", "reflow", "addClass", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "remove", "callback", "animate", "createElement", "className", "classList", "add", "appendTo", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "paddingLeft", "paddingRight", "rect", "getBoundingClientRect", "left", "right", "innerWidth", "_getScrollbarWidth", "fixedContent", "slice", "call", "querySelectorAll", "sticky<PERSON>ontent", "each", "index", "actualPadding", "calculatedPadding", "css", "data", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "elements", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "_jQueryInterface", "TypeError", "selector", "getSelectorFromElement", "tagName", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,OAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,UAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,YAAY,GAAS,WAA3B;EACA,IAAMC,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EACA,IAAMQ,cAAc,GAAO,EAA3B;;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAG,IADG;EAEdC,EAAAA,QAAQ,EAAG,IAFG;EAGdC,EAAAA,KAAK,EAAM,IAHG;EAIdC,EAAAA,IAAI,EAAO;EAJG,CAAhB;EAOA,IAAMC,WAAW,GAAG;EAClBJ,EAAAA,QAAQ,EAAG,kBADO;EAElBC,EAAAA,QAAQ,EAAG,SAFO;EAGlBC,EAAAA,KAAK,EAAM,SAHO;EAIlBC,EAAAA,IAAI,EAAO;EAJO,CAApB;EAOA,IAAME,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAuBb,SADf;EAEZc,EAAAA,MAAM,aAAuBd,SAFjB;EAGZe,EAAAA,IAAI,WAAuBf,SAHf;EAIZgB,EAAAA,KAAK,YAAuBhB,SAJhB;EAKZiB,EAAAA,OAAO,cAAuBjB,SALlB;EAMZkB,EAAAA,MAAM,aAAuBlB,SANjB;EAOZmB,EAAAA,aAAa,oBAAuBnB,SAPxB;EAQZoB,EAAAA,eAAe,sBAAuBpB,SAR1B;EASZqB,EAAAA,eAAe,sBAAuBrB,SAT1B;EAUZsB,EAAAA,iBAAiB,wBAAuBtB,SAV5B;EAWZuB,EAAAA,cAAc,YAAcvB,SAAd,GAA0BC;EAX5B,CAAd;EAcA,IAAMuB,SAAS,GAAG;EAChBC,EAAAA,UAAU,EAAW,yBADL;EAEhBC,EAAAA,kBAAkB,EAAG,yBAFL;EAGhBC,EAAAA,QAAQ,EAAa,gBAHL;EAIhBC,EAAAA,IAAI,EAAiB,YAJL;EAKhBC,EAAAA,IAAI,EAAiB,MALL;EAMhBd,EAAAA,IAAI,EAAiB;EANL,CAAlB;EASA,IAAMe,QAAQ,GAAG;EACfC,EAAAA,MAAM,EAAW,eADF;EAEfC,EAAAA,UAAU,EAAO,aAFF;EAGfC,EAAAA,WAAW,EAAM,uBAHF;EAIfC,EAAAA,YAAY,EAAK,wBAJF;EAKfC,EAAAA,aAAa,EAAI,mDALF;EAMfC,EAAAA,cAAc,EAAG;EAGnB;;;;;;EATiB,CAAjB;;MAeMC;;;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,OAAL,GAA4B,KAAKC,UAAL,CAAgBF,MAAhB,CAA5B;EACA,SAAKG,QAAL,GAA4BJ,OAA5B;EACA,SAAKK,OAAL,GAA4BL,OAAO,CAACM,aAAR,CAAsBd,QAAQ,CAACC,MAA/B,CAA5B;EACA,SAAKc,SAAL,GAA4B,IAA5B;EACA,SAAKC,QAAL,GAA4B,KAA5B;EACA,SAAKC,kBAAL,GAA4B,KAA5B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,gBAAL,GAA4B,KAA5B;EACA,SAAKC,eAAL,GAA4B,CAA5B;EACD;;;;;EAYD;WAEAC,SAAA,gBAAOC,aAAP,EAAsB;EACpB,WAAO,KAAKN,QAAL,GAAgB,KAAKO,IAAL,EAAhB,GAA8B,KAAK3C,IAAL,CAAU0C,aAAV,CAArC;EACD;;WAED1C,OAAA,cAAK0C,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKN,QAAL,IAAiB,KAAKG,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI9C,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B9B,SAAS,CAACK,IAApC,CAAJ,EAA+C;EAC7C,WAAKoB,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAMM,SAAS,GAAGpD,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACG,IAAd,EAAoB;EACpCqC,MAAAA,aAAa,EAAbA;EADoC,KAApB,CAAlB;EAIAjD,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBD,SAAzB;;EAEA,QAAI,KAAKT,QAAL,IAAiBS,SAAS,CAACE,kBAAV,EAArB,EAAqD;EACnD;EACD;;EAED,SAAKX,QAAL,GAAgB,IAAhB;;EAEA,SAAKY,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA3D,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBqB,EAAjB,CACEnD,KAAK,CAACO,aADR,EAEEW,QAAQ,CAACI,YAFX,EAGE,UAAC8B,KAAD;EAAA,aAAW,KAAI,CAACX,IAAL,CAAUW,KAAV,CAAX;EAAA,KAHF;EAMA7D,IAAAA,CAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBoB,EAAhB,CAAmBnD,KAAK,CAACU,iBAAzB,EAA4C,YAAM;EAChDnB,MAAAA,CAAC,CAAC,KAAI,CAACuC,QAAN,CAAD,CAAiBuB,GAAjB,CAAqBrD,KAAK,CAACS,eAA3B,EAA4C,UAAC2C,KAAD,EAAW;EACrD,YAAI7D,CAAC,CAAC6D,KAAK,CAACE,MAAP,CAAD,CAAgBC,EAAhB,CAAmB,KAAI,CAACzB,QAAxB,CAAJ,EAAuC;EACrC,UAAA,KAAI,CAACM,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKoB,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkBjB,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDC,OAAA,cAAKW,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAACM,cAAN;EACD;;EAED,QAAI,CAAC,KAAKxB,QAAN,IAAkB,KAAKG,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAMsB,SAAS,GAAGpE,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACC,IAAd,CAAlB;EAEAV,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBe,SAAzB;;EAEA,QAAI,CAAC,KAAKzB,QAAN,IAAkByB,SAAS,CAACd,kBAAV,EAAtB,EAAsD;EACpD;EACD;;EAED,SAAKX,QAAL,GAAgB,KAAhB;EACA,QAAM0B,UAAU,GAAGrE,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B9B,SAAS,CAACK,IAApC,CAAnB;;EAEA,QAAI2C,UAAJ,EAAgB;EACd,WAAKvB,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKY,eAAL;;EACA,SAAKC,eAAL;;EAEA3D,IAAAA,CAAC,CAACsE,QAAD,CAAD,CAAYC,GAAZ,CAAgB9D,KAAK,CAACK,OAAtB;EAEAd,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBiC,WAAjB,CAA6BnD,SAAS,CAACT,IAAvC;EAEAZ,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBgC,GAAjB,CAAqB9D,KAAK,CAACO,aAA3B;EACAhB,IAAAA,CAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgB+B,GAAhB,CAAoB9D,KAAK,CAACU,iBAA1B;;EAGA,QAAIkD,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAIC,IAAI,CAACC,gCAAL,CAAsC,KAAKpC,QAA3C,CAA5B;EAEAvC,MAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CACGuB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4B,UAACf,KAAD;EAAA,eAAW,MAAI,CAACgB,UAAL,CAAgBhB,KAAhB,CAAX;EAAA,OAD5B,EAEGiB,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACL,WAAKI,UAAL;EACD;EACF;;WAEDE,UAAA,mBAAU;EACR,KAACC,MAAD,EAAS,KAAKzC,QAAd,EAAwB,KAAKC,OAA7B,EACGyC,OADH,CACW,UAACC,WAAD;EAAA,aAAiBlF,CAAC,CAACkF,WAAD,CAAD,CAAeX,GAAf,CAAmB1E,SAAnB,CAAjB;EAAA,KADX;EAGA;;;;;;EAKAG,IAAAA,CAAC,CAACsE,QAAD,CAAD,CAAYC,GAAZ,CAAgB9D,KAAK,CAACK,OAAtB;EAEAd,IAAAA,CAAC,CAACmF,UAAF,CAAa,KAAK5C,QAAlB,EAA4B3C,QAA5B;EAEA,SAAKyC,OAAL,GAA4B,IAA5B;EACA,SAAKE,QAAL,GAA4B,IAA5B;EACA,SAAKC,OAAL,GAA4B,IAA5B;EACA,SAAKE,SAAL,GAA4B,IAA5B;EACA,SAAKC,QAAL,GAA4B,IAA5B;EACA,SAAKC,kBAAL,GAA4B,IAA5B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAKC,gBAAL,GAA4B,IAA5B;EACA,SAAKC,eAAL,GAA4B,IAA5B;EACD;;WAEDqC,eAAA,wBAAe;EACb,SAAK3B,aAAL;EACD;;;WAIDnB,aAAA,oBAAWF,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qBACDjC,OADC,EAEDiC,MAFC,CAAN;EAIAsC,IAAAA,IAAI,CAACW,eAAL,CAAqB3F,IAArB,EAA2B0C,MAA3B,EAAmC5B,WAAnC;EACA,WAAO4B,MAAP;EACD;;WAED8B,eAAA,sBAAajB,aAAb,EAA4B;EAAA;;EAC1B,QAAMoB,UAAU,GAAGrE,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B9B,SAAS,CAACK,IAApC,CAAnB;;EAEA,QAAI,CAAC,KAAKa,QAAL,CAAc+C,UAAf,IACA,KAAK/C,QAAL,CAAc+C,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAnB,MAAAA,QAAQ,CAACoB,IAAT,CAAcC,WAAd,CAA0B,KAAKpD,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAcqD,KAAd,CAAoBC,OAApB,GAA8B,OAA9B;;EACA,SAAKtD,QAAL,CAAcuD,eAAd,CAA8B,aAA9B;;EACA,SAAKvD,QAAL,CAAcwD,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EAEA,QAAI/F,CAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBW,QAAhB,CAAyB9B,SAAS,CAACC,UAAnC,CAAJ,EAAoD;EAClD,WAAKkB,OAAL,CAAaC,aAAb,CAA2Bd,QAAQ,CAACE,UAApC,EAAgDmE,SAAhD,GAA4D,CAA5D;EACD,KAFD,MAEO;EACL,WAAKzD,QAAL,CAAcyD,SAAd,GAA0B,CAA1B;EACD;;EAED,QAAI3B,UAAJ,EAAgB;EACdK,MAAAA,IAAI,CAACuB,MAAL,CAAY,KAAK1D,QAAjB;EACD;;EAEDvC,IAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiB2D,QAAjB,CAA0B7E,SAAS,CAACT,IAApC;;EAEA,QAAI,KAAKyB,OAAL,CAAa/B,KAAjB,EAAwB;EACtB,WAAK6F,aAAL;EACD;;EAED,QAAMC,UAAU,GAAGpG,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACI,KAAd,EAAqB;EACtCoC,MAAAA,aAAa,EAAbA;EADsC,KAArB,CAAnB;;EAIA,QAAMoD,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAAChE,OAAL,CAAa/B,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAACiC,QAAL,CAAcjC,KAAd;EACD;;EACD,MAAA,MAAI,CAACwC,gBAAL,GAAwB,KAAxB;EACA9C,MAAAA,CAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyB+C,UAAzB;EACD,KAND;;EAQA,QAAI/B,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAIC,IAAI,CAACC,gCAAL,CAAsC,KAAKnC,OAA3C,CAA5B;EAEAxC,MAAAA,CAAC,CAAC,KAAKwC,OAAN,CAAD,CACGsB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4ByB,kBAD5B,EAEGvB,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACL4B,MAAAA,kBAAkB;EACnB;EACF;;WAEDF,gBAAA,yBAAgB;EAAA;;EACdnG,IAAAA,CAAC,CAACsE,QAAD,CAAD,CACGC,GADH,CACO9D,KAAK,CAACK,OADb;EAAA,KAEG8C,EAFH,CAEMnD,KAAK,CAACK,OAFZ,EAEqB,UAAC+C,KAAD,EAAW;EAC5B,UAAIS,QAAQ,KAAKT,KAAK,CAACE,MAAnB,IACA,MAAI,CAACxB,QAAL,KAAkBsB,KAAK,CAACE,MADxB,IAEA/D,CAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiB+D,GAAjB,CAAqBzC,KAAK,CAACE,MAA3B,EAAmCwC,MAAnC,KAA8C,CAFlD,EAEqD;EACnD,QAAA,MAAI,CAAChE,QAAL,CAAcjC,KAAd;EACD;EACF,KARH;EASD;;WAEDoD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKf,QAAL,IAAiB,KAAKN,OAAL,CAAahC,QAAlC,EAA4C;EAC1CL,MAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBqB,EAAjB,CAAoBnD,KAAK,CAACQ,eAA1B,EAA2C,UAAC4C,KAAD,EAAW;EACpD,YAAIA,KAAK,CAAC2C,KAAN,KAAgBtG,cAApB,EAAoC;EAClC2D,UAAAA,KAAK,CAACM,cAAN;;EACA,UAAA,MAAI,CAACjB,IAAL;EACD;EACF,OALD;EAMD,KAPD,MAOO,IAAI,CAAC,KAAKP,QAAV,EAAoB;EACzB3C,MAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBgC,GAAjB,CAAqB9D,KAAK,CAACQ,eAA3B;EACD;EACF;;WAED0C,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKhB,QAAT,EAAmB;EACjB3C,MAAAA,CAAC,CAACgF,MAAD,CAAD,CAAUpB,EAAV,CAAanD,KAAK,CAACM,MAAnB,EAA2B,UAAC8C,KAAD;EAAA,eAAW,MAAI,CAACuB,YAAL,CAAkBvB,KAAlB,CAAX;EAAA,OAA3B;EACD,KAFD,MAEO;EACL7D,MAAAA,CAAC,CAACgF,MAAD,CAAD,CAAUT,GAAV,CAAc9D,KAAK,CAACM,MAApB;EACD;EACF;;WAED8D,aAAA,sBAAa;EAAA;;EACX,SAAKtC,QAAL,CAAcqD,KAAd,CAAoBC,OAApB,GAA8B,MAA9B;;EACA,SAAKtD,QAAL,CAAcwD,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKxD,QAAL,CAAcuD,eAAd,CAA8B,YAA9B;;EACA,SAAKhD,gBAAL,GAAwB,KAAxB;;EACA,SAAKmB,aAAL,CAAmB,YAAM;EACvBjE,MAAAA,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CAAiBlB,WAAjB,CAA6BnD,SAAS,CAACI,IAAvC;;EACA,MAAA,MAAI,CAACgF,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACA1G,MAAAA,CAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyB5C,KAAK,CAACE,MAA/B;EACD,KALD;EAMD;;WAEDgG,kBAAA,2BAAkB;EAChB,QAAI,KAAKjE,SAAT,EAAoB;EAClB1C,MAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBkE,MAAlB;EACA,WAAKlE,SAAL,GAAiB,IAAjB;EACD;EACF;;WAEDuB,gBAAA,uBAAc4C,QAAd,EAAwB;EAAA;;EACtB,QAAMC,OAAO,GAAG9G,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B9B,SAAS,CAACK,IAApC,IACZL,SAAS,CAACK,IADE,GACK,EADrB;;EAGA,QAAI,KAAKiB,QAAL,IAAiB,KAAKN,OAAL,CAAajC,QAAlC,EAA4C;EAC1C,WAAKsC,SAAL,GAAiB4B,QAAQ,CAACyC,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKrE,SAAL,CAAesE,SAAf,GAA2B3F,SAAS,CAACG,QAArC;;EAEA,UAAIsF,OAAJ,EAAa;EACX,aAAKpE,SAAL,CAAeuE,SAAf,CAAyBC,GAAzB,CAA6BJ,OAA7B;EACD;;EAED9G,MAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkByE,QAAlB,CAA2B7C,QAAQ,CAACoB,IAApC;EAEA1F,MAAAA,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBqB,EAAjB,CAAoBnD,KAAK,CAACO,aAA1B,EAAyC,UAAC6C,KAAD,EAAW;EAClD,YAAI,MAAI,CAAChB,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EACD,YAAIgB,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAACuD,aAA3B,EAA0C;EACxC;EACD;;EACD,YAAI,MAAI,CAAC/E,OAAL,CAAajC,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAA,MAAI,CAACmC,QAAL,CAAcjC,KAAd;EACD,SAFD,MAEO;EACL,UAAA,MAAI,CAAC4C,IAAL;EACD;EACF,OAbD;;EAeA,UAAI4D,OAAJ,EAAa;EACXpC,QAAAA,IAAI,CAACuB,MAAL,CAAY,KAAKvD,SAAjB;EACD;;EAED1C,MAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBwD,QAAlB,CAA2B7E,SAAS,CAACT,IAArC;;EAEA,UAAI,CAACiG,QAAL,EAAe;EACb;EACD;;EAED,UAAI,CAACC,OAAL,EAAc;EACZD,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMQ,0BAA0B,GAAG3C,IAAI,CAACC,gCAAL,CAAsC,KAAKjC,SAA3C,CAAnC;EAEA1C,MAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CACGoB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4BiC,QAD5B,EAEG/B,oBAFH,CAEwBuC,0BAFxB;EAGD,KA7CD,MA6CO,IAAI,CAAC,KAAK1E,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C1C,MAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkB8B,WAAlB,CAA8BnD,SAAS,CAACT,IAAxC;;EAEA,UAAM0G,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACX,eAAL;;EACA,YAAIE,QAAJ,EAAc;EACZA,UAAAA,QAAQ;EACT;EACF,OALD;;EAOA,UAAI7G,CAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B9B,SAAS,CAACK,IAApC,CAAJ,EAA+C;EAC7C,YAAM2F,2BAA0B,GAAG3C,IAAI,CAACC,gCAAL,CAAsC,KAAKjC,SAA3C,CAAnC;;EAEA1C,QAAAA,CAAC,CAAC,KAAK0C,SAAN,CAAD,CACGoB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4B0C,cAD5B,EAEGxC,oBAFH,CAEwBuC,2BAFxB;EAGD,OAND,MAMO;EACLC,QAAAA,cAAc;EACf;EACF,KAnBM,MAmBA,IAAIT,QAAJ,EAAc;EACnBA,MAAAA,QAAQ;EACT;EACF;EAGD;EACA;EACA;;;WAEApD,gBAAA,yBAAgB;EACd,QAAM8D,kBAAkB,GACtB,KAAKhF,QAAL,CAAciF,YAAd,GAA6BlD,QAAQ,CAACmD,eAAT,CAAyBC,YADxD;;EAGA,QAAI,CAAC,KAAK9E,kBAAN,IAA4B2E,kBAAhC,EAAoD;EAClD,WAAKhF,QAAL,CAAcqD,KAAd,CAAoB+B,WAApB,GAAqC,KAAK5E,eAA1C;EACD;;EAED,QAAI,KAAKH,kBAAL,IAA2B,CAAC2E,kBAAhC,EAAoD;EAClD,WAAKhF,QAAL,CAAcqD,KAAd,CAAoBgC,YAApB,GAAsC,KAAK7E,eAA3C;EACD;EACF;;WAED0D,oBAAA,6BAAoB;EAClB,SAAKlE,QAAL,CAAcqD,KAAd,CAAoB+B,WAApB,GAAkC,EAAlC;EACA,SAAKpF,QAAL,CAAcqD,KAAd,CAAoBgC,YAApB,GAAmC,EAAnC;EACD;;WAEDrE,kBAAA,2BAAkB;EAChB,QAAMsE,IAAI,GAAGvD,QAAQ,CAACoB,IAAT,CAAcoC,qBAAd,EAAb;EACA,SAAKlF,kBAAL,GAA0BiF,IAAI,CAACE,IAAL,GAAYF,IAAI,CAACG,KAAjB,GAAyBhD,MAAM,CAACiD,UAA1D;EACA,SAAKlF,eAAL,GAAuB,KAAKmF,kBAAL,EAAvB;EACD;;WAED1E,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKZ,kBAAT,EAA6B;EAC3B;EACA;EACA,UAAMuF,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAc/D,QAAQ,CAACgE,gBAAT,CAA0B3G,QAAQ,CAACK,aAAnC,CAAd,CAArB;EACA,UAAMuG,aAAa,GAAG,GAAGH,KAAH,CAASC,IAAT,CAAc/D,QAAQ,CAACgE,gBAAT,CAA0B3G,QAAQ,CAACM,cAAnC,CAAd,CAAtB,CAJ2B;;EAO3BjC,MAAAA,CAAC,CAACmI,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQtG,OAAR,EAAoB;EACvC,YAAMuG,aAAa,GAAGvG,OAAO,CAACyD,KAAR,CAAcgC,YAApC;EACA,YAAMe,iBAAiB,GAAG3I,CAAC,CAACmC,OAAD,CAAD,CAAWyG,GAAX,CAAe,eAAf,CAA1B;EACA5I,QAAAA,CAAC,CAACmC,OAAD,CAAD,CACG0G,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,MAAI,CAAC5F,eAFhE;EAGD,OAND,EAP2B;;EAgB3B/C,MAAAA,CAAC,CAACuI,aAAD,CAAD,CAAiBC,IAAjB,CAAsB,UAACC,KAAD,EAAQtG,OAAR,EAAoB;EACxC,YAAM4G,YAAY,GAAG5G,OAAO,CAACyD,KAAR,CAAcoD,WAAnC;EACA,YAAMC,gBAAgB,GAAGjJ,CAAC,CAACmC,OAAD,CAAD,CAAWyG,GAAX,CAAe,cAAf,CAAzB;EACA5I,QAAAA,CAAC,CAACmC,OAAD,CAAD,CACG0G,IADH,CACQ,cADR,EACwBE,YADxB,EAEGH,GAFH,CAEO,cAFP,EAE0BE,UAAU,CAACG,gBAAD,CAAV,GAA+B,MAAI,CAAClG,eAF9D;EAGD,OAND,EAhB2B;;EAyB3B,UAAM2F,aAAa,GAAGpE,QAAQ,CAACoB,IAAT,CAAcE,KAAd,CAAoBgC,YAA1C;EACA,UAAMe,iBAAiB,GAAG3I,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CAAiBkD,GAAjB,CAAqB,eAArB,CAA1B;EACA5I,MAAAA,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CACGmD,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,KAAK5F,eAFhE;EAGD;;EAED/C,IAAAA,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CAAiBQ,QAAjB,CAA0B7E,SAAS,CAACI,IAApC;EACD;;WAEDiF,kBAAA,2BAAkB;EAChB;EACA,QAAMyB,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAc/D,QAAQ,CAACgE,gBAAT,CAA0B3G,QAAQ,CAACK,aAAnC,CAAd,CAArB;EACAhC,IAAAA,CAAC,CAACmI,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQtG,OAAR,EAAoB;EACvC,UAAM+G,OAAO,GAAGlJ,CAAC,CAACmC,OAAD,CAAD,CAAW0G,IAAX,CAAgB,eAAhB,CAAhB;EACA7I,MAAAA,CAAC,CAACmC,OAAD,CAAD,CAAWgD,UAAX,CAAsB,eAAtB;EACAhD,MAAAA,OAAO,CAACyD,KAAR,CAAcgC,YAAd,GAA6BsB,OAAO,GAAGA,OAAH,GAAa,EAAjD;EACD,KAJD,EAHgB;;EAUhB,QAAMC,QAAQ,GAAG,GAAGf,KAAH,CAASC,IAAT,CAAc/D,QAAQ,CAACgE,gBAAT,MAA6B3G,QAAQ,CAACM,cAAtC,CAAd,CAAjB;EACAjC,IAAAA,CAAC,CAACmJ,QAAD,CAAD,CAAYX,IAAZ,CAAiB,UAACC,KAAD,EAAQtG,OAAR,EAAoB;EACnC,UAAMiH,MAAM,GAAGpJ,CAAC,CAACmC,OAAD,CAAD,CAAW0G,IAAX,CAAgB,cAAhB,CAAf;;EACA,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjCpJ,QAAAA,CAAC,CAACmC,OAAD,CAAD,CAAWyG,GAAX,CAAe,cAAf,EAA+BQ,MAA/B,EAAuCjE,UAAvC,CAAkD,cAAlD;EACD;EACF,KALD,EAXgB;;EAmBhB,QAAM+D,OAAO,GAAGlJ,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CAAiBmD,IAAjB,CAAsB,eAAtB,CAAhB;EACA7I,IAAAA,CAAC,CAACsE,QAAQ,CAACoB,IAAV,CAAD,CAAiBP,UAAjB,CAA4B,eAA5B;EACAb,IAAAA,QAAQ,CAACoB,IAAT,CAAcE,KAAd,CAAoBgC,YAApB,GAAmCsB,OAAO,GAAGA,OAAH,GAAa,EAAvD;EACD;;WAEDhB,qBAAA,8BAAqB;EAAE;EACrB,QAAMmB,SAAS,GAAG/E,QAAQ,CAACyC,aAAT,CAAuB,KAAvB,CAAlB;EACAsC,IAAAA,SAAS,CAACrC,SAAV,GAAsB3F,SAAS,CAACE,kBAAhC;EACA+C,IAAAA,QAAQ,CAACoB,IAAT,CAAcC,WAAd,CAA0B0D,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAACvB,qBAAV,GAAkCyB,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACAlF,IAAAA,QAAQ,CAACoB,IAAT,CAAc+D,WAAd,CAA0BJ,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIMI,mBAAP,0BAAwBtH,MAAxB,EAAgCa,aAAhC,EAA+C;EAC7C,WAAO,KAAKuF,IAAL,CAAU,YAAY;EAC3B,UAAIK,IAAI,GAAG7I,CAAC,CAAC,IAAD,CAAD,CAAQ6I,IAAR,CAAajJ,QAAb,CAAX;;EACA,UAAMyC,OAAO,qBACRlC,OADQ,EAERH,CAAC,CAAC,IAAD,CAAD,CAAQ6I,IAAR,EAFQ,EAGR,OAAOzG,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;EAMA,UAAI,CAACyG,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI3G,KAAJ,CAAU,IAAV,EAAgBG,OAAhB,CAAP;EACArC,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQ6I,IAAR,CAAajJ,QAAb,EAAuBiJ,IAAvB;EACD;;EAED,UAAI,OAAOzG,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyG,IAAI,CAACzG,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIuH,SAAJ,wBAAkCvH,MAAlC,QAAN;EACD;;EACDyG,QAAAA,IAAI,CAACzG,MAAD,CAAJ,CAAaa,aAAb;EACD,OALD,MAKO,IAAIZ,OAAO,CAAC9B,IAAZ,EAAkB;EACvBsI,QAAAA,IAAI,CAACtI,IAAL,CAAU0C,aAAV;EACD;EACF,KArBM,CAAP;EAsBD;;;;0BA9boB;EACnB,aAAOtD,OAAP;EACD;;;0BAEoB;EACnB,aAAOQ,OAAP;EACD;;;;;EA2bH;;;;;;;EAMAH,CAAC,CAACsE,QAAD,CAAD,CAAYV,EAAZ,CAAenD,KAAK,CAACW,cAArB,EAAqCO,QAAQ,CAACG,WAA9C,EAA2D,UAAU+B,KAAV,EAAiB;EAAA;;EAC1E,MAAIE,MAAJ;EACA,MAAM6F,QAAQ,GAAGlF,IAAI,CAACmF,sBAAL,CAA4B,IAA5B,CAAjB;;EAEA,MAAID,QAAJ,EAAc;EACZ7F,IAAAA,MAAM,GAAGO,QAAQ,CAAC7B,aAAT,CAAuBmH,QAAvB,CAAT;EACD;;EAED,MAAMxH,MAAM,GAAGpC,CAAC,CAAC+D,MAAD,CAAD,CAAU8E,IAAV,CAAejJ,QAAf,IACX,QADW,qBAERI,CAAC,CAAC+D,MAAD,CAAD,CAAU8E,IAAV,EAFQ,EAGR7I,CAAC,CAAC,IAAD,CAAD,CAAQ6I,IAAR,EAHQ,CAAf;;EAMA,MAAI,KAAKiB,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDjG,IAAAA,KAAK,CAACM,cAAN;EACD;;EAED,MAAM4F,OAAO,GAAG/J,CAAC,CAAC+D,MAAD,CAAD,CAAUD,GAAV,CAAcrD,KAAK,CAACG,IAApB,EAA0B,UAACwC,SAAD,EAAe;EACvD,QAAIA,SAAS,CAACE,kBAAV,EAAJ,EAAoC;EAClC;EACA;EACD;;EAEDyG,IAAAA,OAAO,CAACjG,GAAR,CAAYrD,KAAK,CAACE,MAAlB,EAA0B,YAAM;EAC9B,UAAIX,CAAC,CAAC,OAAD,CAAD,CAAQgE,EAAR,CAAW,UAAX,CAAJ,EAA4B;EAC1B,QAAA,OAAI,CAAC1D,KAAL;EACD;EACF,KAJD;EAKD,GAXe,CAAhB;;EAaA4B,EAAAA,KAAK,CAACwH,gBAAN,CAAuBrB,IAAvB,CAA4BrI,CAAC,CAAC+D,MAAD,CAA7B,EAAuC3B,MAAvC,EAA+C,IAA/C;EACD,CAhCD;EAkCA;;;;;;EAMApC,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAawC,KAAK,CAACwH,gBAAnB;EACA1J,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWsK,WAAX,GAAyB9H,KAAzB;;EACAlC,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWuK,UAAX,GAAwB,YAAM;EAC5BjK,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOmC,KAAK,CAACwH,gBAAb;EACD,CAHD;;;;;;;;"}