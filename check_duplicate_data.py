#!/usr/bin/env python3
"""
检查重复数据的具体情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def check_duplicate_data():
    app = create_app()
    
    with app.app_context():
        print("=== 检查城南小学重复菜单数据 ===\n")
        
        area_id = 22
        day_of_week = 5
        
        # 查询详细的重复数据
        detail_sql = text("""
        SELECT 
            wm.id as weekly_menu_id,
            wm.week_start,
            wm.week_end,
            wm.status,
            wmr.id as recipe_id,
            wmr.day_of_week,
            wmr.meal_type,
            wmr.recipe_name
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        WHERE wmr.day_of_week = :day_of_week
        AND wm.area_id = :area_id
        AND wm.status IN ('已发布', '计划中')
        ORDER BY wm.id, wmr.meal_type, wmr.id
        """)
        
        result = db.session.execute(detail_sql, {
            'day_of_week': day_of_week,
            'area_id': area_id
        })
        
        print("详细的重复数据分析:")
        for row in result:
            print(f"周菜单ID: {row.weekly_menu_id}, 周期: {row.week_start}~{row.week_end}")
            print(f"  菜谱记录ID: {row.recipe_id}, 餐次: {row.meal_type}, 菜谱: {row.recipe_name}")
            print(f"  状态: {row.status}")
            print()
        
        # 统计重复情况
        print("=== 重复统计 ===")
        count_sql = text("""
        SELECT 
            wmr.meal_type,
            wmr.recipe_name,
            COUNT(*) as count,
            STRING_AGG(CAST(wm.id AS VARCHAR), ', ') as weekly_menu_ids
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        WHERE wmr.day_of_week = :day_of_week
        AND wm.area_id = :area_id
        AND wm.status IN ('已发布', '计划中')
        GROUP BY wmr.meal_type, wmr.recipe_name
        HAVING COUNT(*) > 1
        ORDER BY wmr.meal_type
        """)
        
        count_result = db.session.execute(count_sql, {
            'day_of_week': day_of_week,
            'area_id': area_id
        })
        
        for row in count_result:
            print(f"{row.meal_type} - {row.recipe_name}: 重复{row.count}次")
            print(f"  来自周菜单ID: {row.weekly_menu_ids}")
        
        # 检查是否有多个活跃的周菜单
        print("\n=== 检查活跃周菜单 ===")
        active_menus_sql = text("""
        SELECT 
            id,
            week_start,
            week_end,
            status,
            created_at
        FROM weekly_menus
        WHERE area_id = :area_id
        AND status IN ('已发布', '计划中')
        AND week_start <= GETDATE()
        AND week_end >= GETDATE()
        ORDER BY created_at DESC
        """)
        
        active_result = db.session.execute(active_menus_sql, {
            'area_id': area_id
        })
        
        print("当前活跃的周菜单:")
        for row in active_result:
            print(f"ID: {row.id}, 周期: {row.week_start}~{row.week_end}, 状态: {row.status}")
            print(f"  创建时间: {row.created_at}")

if __name__ == '__main__':
    check_duplicate_data()
