/**
 * SweetAlert2 中文本地化
 */
(function() {
    'use strict';
    
    if (typeof Swal !== 'undefined') {
        // 设置全局默认值
        Swal.mixin({
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeButtonAriaLabel: '关闭',
            
            // 常用的预设配置
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-secondary',
                denyButton: 'btn btn-danger'
            },
            buttonsStyling: false
        });
        
        // 添加中文版本的常用方法
        if (!Swal.cn) {
            Swal.cn = {
                // 成功提示
                success: function(message, title) {
                    return Swal.fire({
                        icon: 'success',
                        title: title || '成功',
                        text: message,
                        confirmButtonText: '确定'
                    });
                },
                
                // 错误提示
                error: function(message, title) {
                    return Swal.fire({
                        icon: 'error',
                        title: title || '错误',
                        text: message,
                        confirmButtonText: '确定'
                    });
                },
                
                // 警告提示
                warning: function(message, title) {
                    return Swal.fire({
                        icon: 'warning',
                        title: title || '警告',
                        text: message,
                        confirmButtonText: '确定'
                    });
                },
                
                // 信息提示
                info: function(message, title) {
                    return Swal.fire({
                        icon: 'info',
                        title: title || '信息',
                        text: message,
                        confirmButtonText: '确定'
                    });
                },
                
                // 确认对话框
                confirm: function(message, title, options) {
                    options = options || {};
                    
                    return Swal.fire({
                        icon: 'question',
                        title: title || '确认',
                        text: message,
                        showCancelButton: true,
                        confirmButtonText: options.confirmButtonText || '确定',
                        cancelButtonText: options.cancelButtonText || '取消',
                        reverseButtons: options.reverseButtons || false
                    });
                },
                
                // 输入对话框
                prompt: function(message, title, options) {
                    options = options || {};
                    
                    return Swal.fire({
                        title: title || '请输入',
                        text: message,
                        input: options.input || 'text',
                        inputPlaceholder: options.placeholder || '',
                        inputValue: options.value || '',
                        showCancelButton: true,
                        confirmButtonText: options.confirmButtonText || '确定',
                        cancelButtonText: options.cancelButtonText || '取消',
                        inputValidator: options.validator || function(value) {
                            if (!value) {
                                return '请输入内容';
                            }
                        }
                    });
                },
                
                // 加载提示
                loading: function(message) {
                    return Swal.fire({
                        title: message || '正在加载...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: function() {
                            Swal.showLoading();
                        }
                    });
                },
                
                // 关闭加载提示
                closeLoading: function() {
                    Swal.close();
                },
                
                // 自动关闭的提示
                toast: function(message, type, position, timer) {
                    return Swal.fire({
                        toast: true,
                        position: position || 'top-end',
                        icon: type || 'success',
                        title: message,
                        showConfirmButton: false,
                        timer: timer || 3000
                    });
                }
            };
        }
    }
})();
