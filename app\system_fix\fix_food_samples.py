"""
修复food_samples表结构
添加缺少的列
"""

from app import create_app, db
from sqlalchemy import text

def fix_food_samples_table():
    """添加food_samples表中缺少的列"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否存在
            result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'food_samples'"))
            if result.scalar() == 0:
                print("food_samples表不存在，请先创建表")
                return
            
            # 获取当前表结构
            result = db.session.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'food_samples'"))
            existing_columns = [row[0].lower() for row in result.fetchall()]
            print(f"现有列: {existing_columns}")
            
            # 需要添加的列
            columns_to_add = {
                'operator_id': 'INT',
                'destruction_time': 'DATETIME2',
                'destruction_operator_id': 'INT'
            }
            
            # 添加缺少的列
            for column, data_type in columns_to_add.items():
                if column.lower() not in existing_columns:
                    try:
                        sql = f"ALTER TABLE food_samples ADD {column} {data_type}"
                        db.session.execute(text(sql))
                        print(f"已添加列: {column} ({data_type})")
                    except Exception as e:
                        print(f"添加列 {column} 时出错: {str(e)}")
            
            # 提交事务
            db.session.commit()
            print("food_samples表结构修复完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"修复food_samples表时出错: {str(e)}")

if __name__ == "__main__":
    fix_food_samples_table()
