{"version": 3, "file": "tooltip.js", "sources": ["../src/tools/sanitizer.js", "../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): tools/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i\n\nfunction allowedAttribute(attr, allowedAttributeList) {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter((attrRegex) => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, l = regExp.length; i < l; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (unsafeHtml.length === 0) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].slice.call(createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].slice.call(el.attributes)\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach((attr) => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  DefaultWhitelist,\n  sanitizeHtml\n} from './tools/sanitizer'\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                  = 'tooltip'\nconst VERSION               = '4.3.1'\nconst DATA_KEY              = 'bs.tooltip'\nconst EVENT_KEY             = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT    = $.fn[NAME]\nconst CLASS_PREFIX          = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX    = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation         : 'boolean',\n  template          : 'string',\n  title             : '(string|element|function)',\n  trigger           : 'string',\n  delay             : '(number|object)',\n  html              : 'boolean',\n  selector          : '(string|boolean)',\n  placement         : '(string|function)',\n  offset            : '(number|string|function)',\n  container         : '(string|element|boolean)',\n  fallbackPlacement : '(string|array)',\n  boundary          : '(string|element)',\n  sanitize          : 'boolean',\n  sanitizeFn        : '(null|function)',\n  whiteList         : 'object'\n}\n\nconst AttachmentMap = {\n  AUTO   : 'auto',\n  TOP    : 'top',\n  RIGHT  : 'right',\n  BOTTOM : 'bottom',\n  LEFT   : 'left'\n}\n\nconst Default = {\n  animation         : true,\n  template          : '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger           : 'hover focus',\n  title             : '',\n  delay             : 0,\n  html              : false,\n  selector          : false,\n  placement         : 'top',\n  offset            : 0,\n  container         : false,\n  fallbackPlacement : 'flip',\n  boundary          : 'scrollParent',\n  sanitize          : true,\n  sanitizeFn        : null,\n  whiteList         : DefaultWhitelist\n}\n\nconst HoverState = {\n  SHOW : 'show',\n  OUT  : 'out'\n}\n\nconst Event = {\n  HIDE       : `hide${EVENT_KEY}`,\n  HIDDEN     : `hidden${EVENT_KEY}`,\n  SHOW       : `show${EVENT_KEY}`,\n  SHOWN      : `shown${EVENT_KEY}`,\n  INSERTED   : `inserted${EVENT_KEY}`,\n  CLICK      : `click${EVENT_KEY}`,\n  FOCUSIN    : `focusin${EVENT_KEY}`,\n  FOCUSOUT   : `focusout${EVENT_KEY}`,\n  MOUSEENTER : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE : `mouseleave${EVENT_KEY}`\n}\n\nconst ClassName = {\n  FADE : 'fade',\n  SHOW : 'show'\n}\n\nconst Selector = {\n  TOOLTIP       : '.tooltip',\n  TOOLTIP_INNER : '.tooltip-inner',\n  ARROW         : '.arrow'\n}\n\nconst Trigger = {\n  HOVER  : 'hover',\n  FOCUS  : 'focus',\n  CLICK  : 'click',\n  MANUAL : 'manual'\n}\n\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    /**\n     * Check for Popper dependency\n     * Popper - https://popper.js.org\n     */\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org/)')\n    }\n\n    // private\n    this._isEnabled     = true\n    this._timeout       = 0\n    this._hoverState    = ''\n    this._activeTrigger = {}\n    this._popper        = null\n\n    // Protected\n    this.element = element\n    this.config  = this._getConfig(config)\n    this.tip     = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    $.removeData(this.element, this.constructor.DATA_KEY)\n\n    $(this.element).off(this.constructor.EVENT_KEY)\n    $(this.element).closest('.modal').off('hide.bs.modal')\n\n    if (this.tip) {\n      $(this.tip).remove()\n    }\n\n    this._isEnabled     = null\n    this._timeout       = null\n    this._hoverState    = null\n    this._activeTrigger = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config  = null\n    this.tip     = null\n  }\n\n  show() {\n    if ($(this.element).css('display') === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    const showEvent = $.Event(this.constructor.Event.SHOW)\n    if (this.isWithContent() && this._isEnabled) {\n      $(this.element).trigger(showEvent)\n\n      const shadowRoot = Util.findShadowRoot(this.element)\n      const isInTheDom = $.contains(\n        shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement,\n        this.element\n      )\n\n      if (showEvent.isDefaultPrevented() || !isInTheDom) {\n        return\n      }\n\n      const tip   = this.getTipElement()\n      const tipId = Util.getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        $(tip).addClass(ClassName.FADE)\n      }\n\n      const placement  = typeof this.config.placement === 'function'\n        ? this.config.placement.call(this, tip, this.element)\n        : this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this.addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      $(tip).data(this.constructor.DATA_KEY, this)\n\n      if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n        $(tip).appendTo(container)\n      }\n\n      $(this.element).trigger(this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, {\n        placement: attachment,\n        modifiers: {\n          offset: this._getOffset(),\n          flip: {\n            behavior: this.config.fallbackPlacement\n          },\n          arrow: {\n            element: Selector.ARROW\n          },\n          preventOverflow: {\n            boundariesElement: this.config.boundary\n          }\n        },\n        onCreate: (data) => {\n          if (data.originalPlacement !== data.placement) {\n            this._handlePopperPlacementChange(data)\n          }\n        },\n        onUpdate: (data) => this._handlePopperPlacementChange(data)\n      })\n\n      $(tip).addClass(ClassName.SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().on('mouseover', null, $.noop)\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n        const prevHoverState = this._hoverState\n        this._hoverState     = null\n\n        $(this.element).trigger(this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HoverState.OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if ($(this.tip).hasClass(ClassName.FADE)) {\n        const transitionDuration = Util.getTransitionDurationFromElement(this.tip)\n\n        $(this.tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide(callback) {\n    const tip       = this.getTipElement()\n    const hideEvent = $.Event(this.constructor.Event.HIDE)\n    const complete = () => {\n      if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      $(this.element).trigger(this.constructor.Event.HIDDEN)\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    $(this.element).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(tip).removeClass(ClassName.SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      $(document.body).children().off('mouseover', null, $.noop)\n    }\n\n    this._activeTrigger[Trigger.CLICK] = false\n    this._activeTrigger[Trigger.FOCUS] = false\n    this._activeTrigger[Trigger.HOVER] = false\n\n    if ($(this.tip).hasClass(ClassName.FADE)) {\n      const transitionDuration = Util.getTransitionDurationFromElement(tip)\n\n      $(tip)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent($(tip.querySelectorAll(Selector.TOOLTIP_INNER)), this.getTitle())\n    $(tip).removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n  }\n\n  setElementContent($element, content) {\n    if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n      // Content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (!$(content).parent().is($element)) {\n          $element.empty().append(content)\n        }\n      } else {\n        $element.text($(content).text())\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      $element.html(content)\n    } else {\n      $element.text(content)\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function'\n        ? this.config.title.call(this.element)\n        : this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = (data) => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (Util.isElement(this.config.container)) {\n      return $(this.config.container)\n    }\n\n    return $(document).find(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach((trigger) => {\n      if (trigger === 'click') {\n        $(this.element).on(\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          (event) => this.toggle(event)\n        )\n      } else if (trigger !== Trigger.MANUAL) {\n        const eventIn = trigger === Trigger.HOVER\n          ? this.constructor.Event.MOUSEENTER\n          : this.constructor.Event.FOCUSIN\n        const eventOut = trigger === Trigger.HOVER\n          ? this.constructor.Event.MOUSELEAVE\n          : this.constructor.Event.FOCUSOUT\n\n        $(this.element)\n          .on(\n            eventIn,\n            this.config.selector,\n            (event) => this._enter(event)\n          )\n          .on(\n            eventOut,\n            this.config.selector,\n            (event) => this._leave(event)\n          )\n      }\n    })\n\n    $(this.element).closest('.modal').on(\n      'hide.bs.modal',\n      () => {\n        if (this.element) {\n          this.hide()\n        }\n      }\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n      ] = true\n    }\n\n    if ($(context.getTipElement()).hasClass(ClassName.SHOW) || context._hoverState === HoverState.SHOW) {\n      context._hoverState = HoverState.SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HoverState.SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HoverState.SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HoverState.OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HoverState.OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = $(this.element).data()\n\n    Object.keys(dataAttributes)\n      .forEach((dataAttr) => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const popperInstance = popperData.instance\n    this.tip = popperInstance.popper\n    this._cleanTipClass()\n    this.addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    $(tip).removeClass(ClassName.FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tooltip._jQueryInterface\n$.fn[NAME].Constructor = Tooltip\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tooltip._jQueryInterface\n}\n\nexport default Tooltip\n"], "names": ["uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "toLowerCase", "indexOf", "Boolean", "nodeValue", "match", "regExp", "filter", "attrRegex", "RegExp", "l", "length", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "whitelist<PERSON><PERSON>s", "Object", "keys", "elements", "slice", "call", "body", "querySelectorAll", "len", "el", "el<PERSON>ame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "attributeList", "attributes", "whitelistedAttributes", "concat", "for<PERSON>ach", "removeAttribute", "innerHTML", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "DefaultType", "animation", "template", "title", "trigger", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacement", "boundary", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "HoverState", "SHOW", "OUT", "Event", "HIDE", "HIDDEN", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "ClassName", "FADE", "Selector", "TOOLTIP", "TOOLTIP_INNER", "ARROW", "<PERSON><PERSON>", "HOVER", "FOCUS", "MANUAL", "<PERSON><PERSON><PERSON>", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "dataKey", "constructor", "context", "currentTarget", "data", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "hasClass", "dispose", "clearTimeout", "removeData", "off", "closest", "remove", "destroy", "show", "css", "Error", "showEvent", "isWithContent", "shadowRoot", "<PERSON><PERSON>", "findShadowRoot", "isInTheDom", "contains", "ownerDocument", "documentElement", "isDefaultPrevented", "tipId", "getUID", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addClass", "attachment", "_getAttachment", "addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "appendTo", "modifiers", "_getOffset", "flip", "behavior", "arrow", "preventOverflow", "boundariesElement", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "document", "children", "on", "noop", "complete", "_fixTransition", "prevHoverState", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "hide", "callback", "hideEvent", "_cleanTipClass", "removeClass", "update", "scheduleUpdate", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$element", "content", "nodeType", "j<PERSON>y", "parent", "is", "empty", "append", "text", "getAttribute", "offsets", "isElement", "find", "toUpperCase", "triggers", "split", "eventIn", "eventOut", "_fixTitle", "titleType", "type", "setTimeout", "dataAttributes", "dataAttr", "toString", "typeCheckConfig", "key", "$tip", "tabClass", "join", "popperData", "popperInstance", "instance", "popper", "initConfigAnimation", "_jQueryInterface", "each", "_config", "test", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;EAOA,IAAMA,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA,EAAO,IAAMC,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCD,sBAAvC,CAFyB;EAG9BE,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BC,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BC,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,OAAf,EAAwB,OAAxB,EAAiC,QAAjC,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EAGN;;;;;;EAlCgC,CAAzB;EAuCP,IAAMC,gBAAgB,GAAG,6DAAzB;EAEA;;;;;;EAKA,IAAMC,gBAAgB,GAAG,qIAAzB;;EAEA,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,oBAAhC,EAAsD;EACpD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcC,WAAd,EAAjB;;EAEA,MAAIH,oBAAoB,CAACI,OAArB,CAA6BH,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;EACjD,QAAIrC,QAAQ,CAACwC,OAAT,CAAiBH,QAAjB,MAA+B,CAAC,CAApC,EAAuC;EACrC,aAAOI,OAAO,CAACN,IAAI,CAACO,SAAL,CAAeC,KAAf,CAAqBX,gBAArB,KAA0CG,IAAI,CAACO,SAAL,CAAeC,KAAf,CAAqBV,gBAArB,CAA3C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,MAAMW,MAAM,GAAGR,oBAAoB,CAACS,MAArB,CAA4B,UAACC,SAAD;EAAA,WAAeA,SAAS,YAAYC,MAApC;EAAA,GAA5B,CAAf,CAXoD;;EAcpD,OAAK,IAAI7B,CAAC,GAAG,CAAR,EAAW8B,CAAC,GAAGJ,MAAM,CAACK,MAA3B,EAAmC/B,CAAC,GAAG8B,CAAvC,EAA0C9B,CAAC,EAA3C,EAA+C;EAC7C,QAAImB,QAAQ,CAACM,KAAT,CAAeC,MAAM,CAAC1B,CAAD,CAArB,CAAJ,EAA+B;EAC7B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD;;AAED,EAAO,SAASgC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAIF,UAAU,CAACF,MAAX,KAAsB,CAA1B,EAA6B;EAC3B,WAAOE,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,MAAMG,SAAS,GAAG,IAAIC,MAAM,CAACC,SAAX,EAAlB;EACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,eAAV,CAA0BP,UAA1B,EAAsC,WAAtC,CAAxB;EACA,MAAMQ,aAAa,GAAGC,MAAM,CAACC,IAAP,CAAYT,SAAZ,CAAtB;EACA,MAAMU,QAAQ,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcP,eAAe,CAACQ,IAAhB,CAAqBC,gBAArB,CAAsC,GAAtC,CAAd,CAAjB;;EAZ8D,6BAcrDhD,CAdqD,EAc9CiD,GAd8C;EAe5D,QAAMC,EAAE,GAAGN,QAAQ,CAAC5C,CAAD,CAAnB;EACA,QAAMmD,MAAM,GAAGD,EAAE,CAAC9B,QAAH,CAAYC,WAAZ,EAAf;;EAEA,QAAIoB,aAAa,CAACnB,OAAd,CAAsB4B,EAAE,CAAC9B,QAAH,CAAYC,WAAZ,EAAtB,MAAqD,CAAC,CAA1D,EAA6D;EAC3D6B,MAAAA,EAAE,CAACE,UAAH,CAAcC,WAAd,CAA0BH,EAA1B;EAEA;EACD;;EAED,QAAMI,aAAa,GAAG,GAAGT,KAAH,CAASC,IAAT,CAAcI,EAAE,CAACK,UAAjB,CAAtB;EACA,QAAMC,qBAAqB,GAAG,GAAGC,MAAH,CAAUvB,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACiB,MAAD,CAAT,IAAqB,EAArD,CAA9B;EAEAG,IAAAA,aAAa,CAACI,OAAd,CAAsB,UAACzC,IAAD,EAAU;EAC9B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOuC,qBAAP,CAArB,EAAoD;EAClDN,QAAAA,EAAE,CAACS,eAAH,CAAmB1C,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EA3B4D;;EAc9D,OAAK,IAAIpB,CAAC,GAAG,CAAR,EAAWiD,GAAG,GAAGL,QAAQ,CAACb,MAA/B,EAAuC/B,CAAC,GAAGiD,GAA3C,EAAgDjD,CAAC,EAAjD,EAAqD;EAAA,qBAA5CA,CAA4C,EAArCiD,GAAqC;;EAAA,6BAOjD;EAWH;;EAED,SAAOV,eAAe,CAACQ,IAAhB,CAAqBa,SAA5B;EACD;;EC/GD;;;;;;EAMA,IAAMC,IAAI,GAAoB,SAA9B;EACA,IAAMC,OAAO,GAAiB,OAA9B;EACA,IAAMC,QAAQ,GAAgB,YAA9B;EACA,IAAMC,SAAS,SAAmBD,QAAlC;EACA,IAAME,kBAAkB,GAAMC,CAAC,CAACC,EAAF,CAAKN,IAAL,CAA9B;EACA,IAAMO,YAAY,GAAY,YAA9B;EACA,IAAMC,kBAAkB,GAAM,IAAIxC,MAAJ,aAAqBuC,YAArB,WAAyC,GAAzC,CAA9B;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAW,SADF;EAElBC,EAAAA,QAAQ,EAAY,QAFF;EAGlBC,EAAAA,KAAK,EAAe,2BAHF;EAIlBC,EAAAA,OAAO,EAAa,QAJF;EAKlBC,EAAAA,KAAK,EAAe,iBALF;EAMlBC,EAAAA,IAAI,EAAgB,SANF;EAOlBC,EAAAA,QAAQ,EAAY,kBAPF;EAQlBC,EAAAA,SAAS,EAAW,mBARF;EASlBC,EAAAA,MAAM,EAAc,0BATF;EAUlBC,EAAAA,SAAS,EAAW,0BAVF;EAWlBC,EAAAA,iBAAiB,EAAG,gBAXF;EAYlBC,EAAAA,QAAQ,EAAY,kBAZF;EAalBC,EAAAA,QAAQ,EAAY,SAbF;EAclBjD,EAAAA,UAAU,EAAU,iBAdF;EAelBD,EAAAA,SAAS,EAAW;EAfF,CAApB;EAkBA,IAAMmD,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAK,MADW;EAEpBC,EAAAA,GAAG,EAAM,KAFW;EAGpBC,EAAAA,KAAK,EAAI,OAHW;EAIpBC,EAAAA,MAAM,EAAG,QAJW;EAKpBC,EAAAA,IAAI,EAAK;EALW,CAAtB;EAQA,IAAMC,OAAO,GAAG;EACdnB,EAAAA,SAAS,EAAW,IADN;EAEdC,EAAAA,QAAQ,EAAY,yCACF,2BADE,GAEF,yCAJJ;EAKdE,EAAAA,OAAO,EAAa,aALN;EAMdD,EAAAA,KAAK,EAAe,EANN;EAOdE,EAAAA,KAAK,EAAe,CAPN;EAQdC,EAAAA,IAAI,EAAgB,KARN;EASdC,EAAAA,QAAQ,EAAY,KATN;EAUdC,EAAAA,SAAS,EAAW,KAVN;EAWdC,EAAAA,MAAM,EAAc,CAXN;EAYdC,EAAAA,SAAS,EAAW,KAZN;EAadC,EAAAA,iBAAiB,EAAG,MAbN;EAcdC,EAAAA,QAAQ,EAAY,cAdN;EAedC,EAAAA,QAAQ,EAAY,IAfN;EAgBdjD,EAAAA,UAAU,EAAU,IAhBN;EAiBdD,EAAAA,SAAS,EAAWlD;EAjBN,CAAhB;EAoBA,IAAM4G,UAAU,GAAG;EACjBC,EAAAA,IAAI,EAAG,MADU;EAEjBC,EAAAA,GAAG,EAAI;EAFU,CAAnB;EAKA,IAAMC,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAgBhC,SADR;EAEZiC,EAAAA,MAAM,aAAgBjC,SAFV;EAGZ6B,EAAAA,IAAI,WAAgB7B,SAHR;EAIZkC,EAAAA,KAAK,YAAgBlC,SAJT;EAKZmC,EAAAA,QAAQ,eAAgBnC,SALZ;EAMZoC,EAAAA,KAAK,YAAgBpC,SANT;EAOZqC,EAAAA,OAAO,cAAgBrC,SAPX;EAQZsC,EAAAA,QAAQ,eAAgBtC,SARZ;EASZuC,EAAAA,UAAU,iBAAgBvC,SATd;EAUZwC,EAAAA,UAAU,iBAAgBxC;EAVd,CAAd;EAaA,IAAMyC,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAG,MADS;EAEhBb,EAAAA,IAAI,EAAG;EAFS,CAAlB;EAKA,IAAMc,QAAQ,GAAG;EACfC,EAAAA,OAAO,EAAS,UADD;EAEfC,EAAAA,aAAa,EAAG,gBAFD;EAGfC,EAAAA,KAAK,EAAW;EAHD,CAAjB;EAMA,IAAMC,OAAO,GAAG;EACdC,EAAAA,KAAK,EAAI,OADK;EAEdC,EAAAA,KAAK,EAAI,OAFK;EAGdb,EAAAA,KAAK,EAAI,OAHK;EAIdc,EAAAA,MAAM,EAAG;EAIX;;;;;;EARgB,CAAhB;;MAcMC;;;EACJ,mBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B;;;;EAIA,QAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAIC,SAAJ,CAAc,kEAAd,CAAN;EACD,KAP0B;;;EAU3B,SAAKC,UAAL,GAAsB,IAAtB;EACA,SAAKC,QAAL,GAAsB,CAAtB;EACA,SAAKC,WAAL,GAAsB,EAAtB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKC,OAAL,GAAsB,IAAtB,CAd2B;;EAiB3B,SAAKR,OAAL,GAAeA,OAAf;EACA,SAAKC,MAAL,GAAe,KAAKQ,UAAL,CAAgBR,MAAhB,CAAf;EACA,SAAKS,GAAL,GAAe,IAAf;;EAEA,SAAKC,aAAL;EACD;;;;;EAgCD;WAEAC,SAAA,kBAAS;EACP,SAAKR,UAAL,GAAkB,IAAlB;EACD;;WAEDS,UAAA,mBAAU;EACR,SAAKT,UAAL,GAAkB,KAAlB;EACD;;WAEDU,gBAAA,yBAAgB;EACd,SAAKV,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;WAEDW,SAAA,gBAAOC,KAAP,EAAc;EACZ,QAAI,CAAC,KAAKZ,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIY,KAAJ,EAAW;EACT,UAAMC,OAAO,GAAG,KAAKC,WAAL,CAAiBvE,QAAjC;EACA,UAAIwE,OAAO,GAAGrE,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAAd;;EAEA,UAAI,CAACE,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAxE,QAAAA,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAEDA,MAAAA,OAAO,CAACZ,cAAR,CAAuBgB,KAAvB,GAA+B,CAACJ,OAAO,CAACZ,cAAR,CAAuBgB,KAAvD;;EAEA,UAAIJ,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClCL,QAAAA,OAAO,CAACM,MAAR,CAAe,IAAf,EAAqBN,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACO,MAAR,CAAe,IAAf,EAAqBP,OAArB;EACD;EACF,KAnBD,MAmBO;EACL,UAAIrE,CAAC,CAAC,KAAK6E,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAiCvC,SAAS,CAACZ,IAA3C,CAAJ,EAAsD;EACpD,aAAKiD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;WAEDI,UAAA,mBAAU;EACRC,IAAAA,YAAY,CAAC,KAAKzB,QAAN,CAAZ;EAEAvD,IAAAA,CAAC,CAACiF,UAAF,CAAa,KAAK/B,OAAlB,EAA2B,KAAKkB,WAAL,CAAiBvE,QAA5C;EAEAG,IAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBgC,GAAhB,CAAoB,KAAKd,WAAL,CAAiBtE,SAArC;EACAE,IAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,EAAkCD,GAAlC,CAAsC,eAAtC;;EAEA,QAAI,KAAKtB,GAAT,EAAc;EACZ5D,MAAAA,CAAC,CAAC,KAAK4D,GAAN,CAAD,CAAYwB,MAAZ;EACD;;EAED,SAAK9B,UAAL,GAAsB,IAAtB;EACA,SAAKC,QAAL,GAAsB,IAAtB;EACA,SAAKC,WAAL,GAAsB,IAAtB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAKC,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa2B,OAAb;EACD;;EAED,SAAK3B,OAAL,GAAe,IAAf;EACA,SAAKR,OAAL,GAAe,IAAf;EACA,SAAKC,MAAL,GAAe,IAAf;EACA,SAAKS,GAAL,GAAe,IAAf;EACD;;WAED0B,OAAA,gBAAO;EAAA;;EACL,QAAItF,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBqC,GAAhB,CAAoB,SAApB,MAAmC,MAAvC,EAA+C;EAC7C,YAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAMC,SAAS,GAAGzF,CAAC,CAAC6B,KAAF,CAAQ,KAAKuC,WAAL,CAAiBvC,KAAjB,CAAuBF,IAA/B,CAAlB;;EACA,QAAI,KAAK+D,aAAL,MAAwB,KAAKpC,UAAjC,EAA6C;EAC3CtD,MAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBzC,OAAhB,CAAwBgF,SAAxB;EAEA,UAAME,UAAU,GAAGC,IAAI,CAACC,cAAL,CAAoB,KAAK3C,OAAzB,CAAnB;EACA,UAAM4C,UAAU,GAAG9F,CAAC,CAAC+F,QAAF,CACjBJ,UAAU,KAAK,IAAf,GAAsBA,UAAtB,GAAmC,KAAKzC,OAAL,CAAa8C,aAAb,CAA2BC,eAD7C,EAEjB,KAAK/C,OAFY,CAAnB;;EAKA,UAAIuC,SAAS,CAACS,kBAAV,MAAkC,CAACJ,UAAvC,EAAmD;EACjD;EACD;;EAED,UAAMlC,GAAG,GAAK,KAAKiB,aAAL,EAAd;EACA,UAAMsB,KAAK,GAAGP,IAAI,CAACQ,MAAL,CAAY,KAAKhC,WAAL,CAAiBzE,IAA7B,CAAd;EAEAiE,MAAAA,GAAG,CAACyC,YAAJ,CAAiB,IAAjB,EAAuBF,KAAvB;EACA,WAAKjD,OAAL,CAAamD,YAAb,CAA0B,kBAA1B,EAA8CF,KAA9C;EAEA,WAAKG,UAAL;;EAEA,UAAI,KAAKnD,MAAL,CAAY7C,SAAhB,EAA2B;EACzBN,QAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAO2C,QAAP,CAAgBhE,SAAS,CAACC,IAA1B;EACD;;EAED,UAAM3B,SAAS,GAAI,OAAO,KAAKsC,MAAL,CAAYtC,SAAnB,KAAiC,UAAjC,GACf,KAAKsC,MAAL,CAAYtC,SAAZ,CAAsBjC,IAAtB,CAA2B,IAA3B,EAAiCgF,GAAjC,EAAsC,KAAKV,OAA3C,CADe,GAEf,KAAKC,MAAL,CAAYtC,SAFhB;;EAIA,UAAM2F,UAAU,GAAG,KAAKC,cAAL,CAAoB5F,SAApB,CAAnB;;EACA,WAAK6F,kBAAL,CAAwBF,UAAxB;;EAEA,UAAMzF,SAAS,GAAG,KAAK4F,aAAL,EAAlB;;EACA3G,MAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAOW,IAAP,CAAY,KAAKH,WAAL,CAAiBvE,QAA7B,EAAuC,IAAvC;;EAEA,UAAI,CAACG,CAAC,CAAC+F,QAAF,CAAW,KAAK7C,OAAL,CAAa8C,aAAb,CAA2BC,eAAtC,EAAuD,KAAKrC,GAA5D,CAAL,EAAuE;EACrE5D,QAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAOgD,QAAP,CAAgB7F,SAAhB;EACD;;EAEDf,MAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBzC,OAAhB,CAAwB,KAAK2D,WAAL,CAAiBvC,KAAjB,CAAuBI,QAA/C;EAEA,WAAKyB,OAAL,GAAe,IAAIN,MAAJ,CAAW,KAAKF,OAAhB,EAAyBU,GAAzB,EAA8B;EAC3C/C,QAAAA,SAAS,EAAE2F,UADgC;EAE3CK,QAAAA,SAAS,EAAE;EACT/F,UAAAA,MAAM,EAAE,KAAKgG,UAAL,EADC;EAETC,UAAAA,IAAI,EAAE;EACJC,YAAAA,QAAQ,EAAE,KAAK7D,MAAL,CAAYnC;EADlB,WAFG;EAKTiG,UAAAA,KAAK,EAAE;EACL/D,YAAAA,OAAO,EAAET,QAAQ,CAACG;EADb,WALE;EAQTsE,UAAAA,eAAe,EAAE;EACfC,YAAAA,iBAAiB,EAAE,KAAKhE,MAAL,CAAYlC;EADhB;EARR,SAFgC;EAc3CmG,QAAAA,QAAQ,EAAE,kBAAC7C,IAAD,EAAU;EAClB,cAAIA,IAAI,CAAC8C,iBAAL,KAA2B9C,IAAI,CAAC1D,SAApC,EAA+C;EAC7C,YAAA,KAAI,CAACyG,4BAAL,CAAkC/C,IAAlC;EACD;EACF,SAlB0C;EAmB3CgD,QAAAA,QAAQ,EAAE,kBAAChD,IAAD;EAAA,iBAAU,KAAI,CAAC+C,4BAAL,CAAkC/C,IAAlC,CAAV;EAAA;EAnBiC,OAA9B,CAAf;EAsBAvE,MAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAO2C,QAAP,CAAgBhE,SAAS,CAACZ,IAA1B,EA/D2C;EAkE3C;EACA;EACA;;EACA,UAAI,kBAAkB6F,QAAQ,CAACvB,eAA/B,EAAgD;EAC9CjG,QAAAA,CAAC,CAACwH,QAAQ,CAAC3I,IAAV,CAAD,CAAiB4I,QAAjB,GAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkD1H,CAAC,CAAC2H,IAApD;EACD;;EAED,UAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,YAAI,KAAI,CAACzE,MAAL,CAAY7C,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAACuH,cAAL;EACD;;EACD,YAAMC,cAAc,GAAG,KAAI,CAACtE,WAA5B;EACA,QAAA,KAAI,CAACA,WAAL,GAAuB,IAAvB;EAEAxD,QAAAA,CAAC,CAAC,KAAI,CAACkD,OAAN,CAAD,CAAgBzC,OAAhB,CAAwB,KAAI,CAAC2D,WAAL,CAAiBvC,KAAjB,CAAuBG,KAA/C;;EAEA,YAAI8F,cAAc,KAAKpG,UAAU,CAACE,GAAlC,EAAuC;EACrC,UAAA,KAAI,CAACgD,MAAL,CAAY,IAAZ,EAAkB,KAAlB;EACD;EACF,OAZD;;EAcA,UAAI5E,CAAC,CAAC,KAAK4D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBvC,SAAS,CAACC,IAA/B,CAAJ,EAA0C;EACxC,YAAMuF,kBAAkB,GAAGnC,IAAI,CAACoC,gCAAL,CAAsC,KAAKpE,GAA3C,CAA3B;EAEA5D,QAAAA,CAAC,CAAC,KAAK4D,GAAN,CAAD,CACGqE,GADH,CACOrC,IAAI,CAACsC,cADZ,EAC4BN,QAD5B,EAEGO,oBAFH,CAEwBJ,kBAFxB;EAGD,OAND,MAMO;EACLH,QAAAA,QAAQ;EACT;EACF;EACF;;WAEDQ,OAAA,cAAKC,QAAL,EAAe;EAAA;;EACb,QAAMzE,GAAG,GAAS,KAAKiB,aAAL,EAAlB;EACA,QAAMyD,SAAS,GAAGtI,CAAC,CAAC6B,KAAF,CAAQ,KAAKuC,WAAL,CAAiBvC,KAAjB,CAAuBC,IAA/B,CAAlB;;EACA,QAAM8F,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAI,MAAI,CAACpE,WAAL,KAAqB9B,UAAU,CAACC,IAAhC,IAAwCiC,GAAG,CAAC1E,UAAhD,EAA4D;EAC1D0E,QAAAA,GAAG,CAAC1E,UAAJ,CAAeC,WAAf,CAA2ByE,GAA3B;EACD;;EAED,MAAA,MAAI,CAAC2E,cAAL;;EACA,MAAA,MAAI,CAACrF,OAAL,CAAazD,eAAb,CAA6B,kBAA7B;;EACAO,MAAAA,CAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CAAgBzC,OAAhB,CAAwB,MAAI,CAAC2D,WAAL,CAAiBvC,KAAjB,CAAuBE,MAA/C;;EACA,UAAI,MAAI,CAAC2B,OAAL,KAAiB,IAArB,EAA2B;EACzB,QAAA,MAAI,CAACA,OAAL,CAAa2B,OAAb;EACD;;EAED,UAAIgD,QAAJ,EAAc;EACZA,QAAAA,QAAQ;EACT;EACF,KAfD;;EAiBArI,IAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBzC,OAAhB,CAAwB6H,SAAxB;;EAEA,QAAIA,SAAS,CAACpC,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAEDlG,IAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAO4E,WAAP,CAAmBjG,SAAS,CAACZ,IAA7B,EA1Ba;EA6Bb;;EACA,QAAI,kBAAkB6F,QAAQ,CAACvB,eAA/B,EAAgD;EAC9CjG,MAAAA,CAAC,CAACwH,QAAQ,CAAC3I,IAAV,CAAD,CAAiB4I,QAAjB,GAA4BvC,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmDlF,CAAC,CAAC2H,IAArD;EACD;;EAED,SAAKlE,cAAL,CAAoBZ,OAAO,CAACX,KAA5B,IAAqC,KAArC;EACA,SAAKuB,cAAL,CAAoBZ,OAAO,CAACE,KAA5B,IAAqC,KAArC;EACA,SAAKU,cAAL,CAAoBZ,OAAO,CAACC,KAA5B,IAAqC,KAArC;;EAEA,QAAI9C,CAAC,CAAC,KAAK4D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBvC,SAAS,CAACC,IAA/B,CAAJ,EAA0C;EACxC,UAAMuF,kBAAkB,GAAGnC,IAAI,CAACoC,gCAAL,CAAsCpE,GAAtC,CAA3B;EAEA5D,MAAAA,CAAC,CAAC4D,GAAD,CAAD,CACGqE,GADH,CACOrC,IAAI,CAACsC,cADZ,EAC4BN,QAD5B,EAEGO,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLH,MAAAA,QAAQ;EACT;;EAED,SAAKpE,WAAL,GAAmB,EAAnB;EACD;;WAEDiF,SAAA,kBAAS;EACP,QAAI,KAAK/E,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAagF,cAAb;EACD;EACF;;;WAIDhD,gBAAA,yBAAgB;EACd,WAAOrI,OAAO,CAAC,KAAKsL,QAAL,EAAD,CAAd;EACD;;WAEDjC,qBAAA,4BAAmBF,UAAnB,EAA+B;EAC7BxG,IAAAA,CAAC,CAAC,KAAK6E,aAAL,EAAD,CAAD,CAAwB0B,QAAxB,CAAoCrG,YAApC,SAAoDsG,UAApD;EACD;;WAED3B,gBAAA,yBAAgB;EACd,SAAKjB,GAAL,GAAW,KAAKA,GAAL,IAAY5D,CAAC,CAAC,KAAKmD,MAAL,CAAY5C,QAAb,CAAD,CAAwB,CAAxB,CAAvB;EACA,WAAO,KAAKqD,GAAZ;EACD;;WAED0C,aAAA,sBAAa;EACX,QAAM1C,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,SAAK+D,iBAAL,CAAuB5I,CAAC,CAAC4D,GAAG,CAAC9E,gBAAJ,CAAqB2D,QAAQ,CAACE,aAA9B,CAAD,CAAxB,EAAwE,KAAKgG,QAAL,EAAxE;EACA3I,IAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAO4E,WAAP,CAAsBjG,SAAS,CAACC,IAAhC,SAAwCD,SAAS,CAACZ,IAAlD;EACD;;WAEDiH,oBAAA,2BAAkBC,QAAlB,EAA4BC,OAA5B,EAAqC;EACnC,QAAI,OAAOA,OAAP,KAAmB,QAAnB,KAAgCA,OAAO,CAACC,QAAR,IAAoBD,OAAO,CAACE,MAA5D,CAAJ,EAAyE;EACvE;EACA,UAAI,KAAK7F,MAAL,CAAYxC,IAAhB,EAAsB;EACpB,YAAI,CAACX,CAAC,CAAC8I,OAAD,CAAD,CAAWG,MAAX,GAAoBC,EAApB,CAAuBL,QAAvB,CAAL,EAAuC;EACrCA,UAAAA,QAAQ,CAACM,KAAT,GAAiBC,MAAjB,CAAwBN,OAAxB;EACD;EACF,OAJD,MAIO;EACLD,QAAAA,QAAQ,CAACQ,IAAT,CAAcrJ,CAAC,CAAC8I,OAAD,CAAD,CAAWO,IAAX,EAAd;EACD;;EAED;EACD;;EAED,QAAI,KAAKlG,MAAL,CAAYxC,IAAhB,EAAsB;EACpB,UAAI,KAAKwC,MAAL,CAAYjC,QAAhB,EAA0B;EACxB4H,QAAAA,OAAO,GAAGhL,YAAY,CAACgL,OAAD,EAAU,KAAK3F,MAAL,CAAYnF,SAAtB,EAAiC,KAAKmF,MAAL,CAAYlF,UAA7C,CAAtB;EACD;;EAED4K,MAAAA,QAAQ,CAAClI,IAAT,CAAcmI,OAAd;EACD,KAND,MAMO;EACLD,MAAAA,QAAQ,CAACQ,IAAT,CAAcP,OAAd;EACD;EACF;;WAEDH,WAAA,oBAAW;EACT,QAAInI,KAAK,GAAG,KAAK0C,OAAL,CAAaoG,YAAb,CAA0B,qBAA1B,CAAZ;;EAEA,QAAI,CAAC9I,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAK2C,MAAL,CAAY3C,KAAnB,KAA6B,UAA7B,GACJ,KAAK2C,MAAL,CAAY3C,KAAZ,CAAkB5B,IAAlB,CAAuB,KAAKsE,OAA5B,CADI,GAEJ,KAAKC,MAAL,CAAY3C,KAFhB;EAGD;;EAED,WAAOA,KAAP;EACD;;;WAIDsG,aAAA,sBAAa;EAAA;;EACX,QAAMhG,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKqC,MAAL,CAAYrC,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAACb,EAAP,GAAY,UAACsE,IAAD,EAAU;EACpBA,QAAAA,IAAI,CAACgF,OAAL,qBACKhF,IAAI,CAACgF,OADV,EAEK,MAAI,CAACpG,MAAL,CAAYrC,MAAZ,CAAmByD,IAAI,CAACgF,OAAxB,EAAiC,MAAI,CAACrG,OAAtC,KAAkD,EAFvD;EAKA,eAAOqB,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACLzD,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKqC,MAAL,CAAYrC,MAA5B;EACD;;EAED,WAAOA,MAAP;EACD;;WAED6F,gBAAA,yBAAgB;EACd,QAAI,KAAKxD,MAAL,CAAYpC,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOyG,QAAQ,CAAC3I,IAAhB;EACD;;EAED,QAAI+G,IAAI,CAAC4D,SAAL,CAAe,KAAKrG,MAAL,CAAYpC,SAA3B,CAAJ,EAA2C;EACzC,aAAOf,CAAC,CAAC,KAAKmD,MAAL,CAAYpC,SAAb,CAAR;EACD;;EAED,WAAOf,CAAC,CAACwH,QAAD,CAAD,CAAYiC,IAAZ,CAAiB,KAAKtG,MAAL,CAAYpC,SAA7B,CAAP;EACD;;WAED0F,iBAAA,wBAAe5F,SAAf,EAA0B;EACxB,WAAOM,aAAa,CAACN,SAAS,CAAC6I,WAAV,EAAD,CAApB;EACD;;WAED7F,gBAAA,yBAAgB;EAAA;;EACd,QAAM8F,QAAQ,GAAG,KAAKxG,MAAL,CAAY1C,OAAZ,CAAoBmJ,KAApB,CAA0B,GAA1B,CAAjB;EAEAD,IAAAA,QAAQ,CAACnK,OAAT,CAAiB,UAACiB,OAAD,EAAa;EAC5B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBT,QAAAA,CAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CAAgBwE,EAAhB,CACE,MAAI,CAACtD,WAAL,CAAiBvC,KAAjB,CAAuBK,KADzB,EAEE,MAAI,CAACiB,MAAL,CAAYvC,QAFd,EAGE,UAACsD,KAAD;EAAA,iBAAW,MAAI,CAACD,MAAL,CAAYC,KAAZ,CAAX;EAAA,SAHF;EAKD,OAND,MAMO,IAAIzD,OAAO,KAAKoC,OAAO,CAACG,MAAxB,EAAgC;EACrC,YAAM6G,OAAO,GAAGpJ,OAAO,KAAKoC,OAAO,CAACC,KAApB,GACZ,MAAI,CAACsB,WAAL,CAAiBvC,KAAjB,CAAuBQ,UADX,GAEZ,MAAI,CAAC+B,WAAL,CAAiBvC,KAAjB,CAAuBM,OAF3B;EAGA,YAAM2H,QAAQ,GAAGrJ,OAAO,KAAKoC,OAAO,CAACC,KAApB,GACb,MAAI,CAACsB,WAAL,CAAiBvC,KAAjB,CAAuBS,UADV,GAEb,MAAI,CAAC8B,WAAL,CAAiBvC,KAAjB,CAAuBO,QAF3B;EAIApC,QAAAA,CAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CACGwE,EADH,CAEImC,OAFJ,EAGI,MAAI,CAAC1G,MAAL,CAAYvC,QAHhB,EAII,UAACsD,KAAD;EAAA,iBAAW,MAAI,CAACS,MAAL,CAAYT,KAAZ,CAAX;EAAA,SAJJ,EAMGwD,EANH,CAOIoC,QAPJ,EAQI,MAAI,CAAC3G,MAAL,CAAYvC,QARhB,EASI,UAACsD,KAAD;EAAA,iBAAW,MAAI,CAACU,MAAL,CAAYV,KAAZ,CAAX;EAAA,SATJ;EAWD;EACF,KA3BD;EA6BAlE,IAAAA,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,EAAkCuC,EAAlC,CACE,eADF,EAEE,YAAM;EACJ,UAAI,MAAI,CAACxE,OAAT,EAAkB;EAChB,QAAA,MAAI,CAACkF,IAAL;EACD;EACF,KANH;;EASA,QAAI,KAAKjF,MAAL,CAAYvC,QAAhB,EAA0B;EACxB,WAAKuC,MAAL,qBACK,KAAKA,MADV;EAEE1C,QAAAA,OAAO,EAAE,QAFX;EAGEG,QAAAA,QAAQ,EAAE;EAHZ;EAKD,KAND,MAMO;EACL,WAAKmJ,SAAL;EACD;EACF;;WAEDA,YAAA,qBAAY;EACV,QAAMC,SAAS,GAAG,OAAO,KAAK9G,OAAL,CAAaoG,YAAb,CAA0B,qBAA1B,CAAzB;;EAEA,QAAI,KAAKpG,OAAL,CAAaoG,YAAb,CAA0B,OAA1B,KAAsCU,SAAS,KAAK,QAAxD,EAAkE;EAChE,WAAK9G,OAAL,CAAamD,YAAb,CACE,qBADF,EAEE,KAAKnD,OAAL,CAAaoG,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;EAKA,WAAKpG,OAAL,CAAamD,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;EACD;EACF;;WAED1B,SAAA,gBAAOT,KAAP,EAAcG,OAAd,EAAuB;EACrB,QAAMF,OAAO,GAAG,KAAKC,WAAL,CAAiBvE,QAAjC;EACAwE,IAAAA,OAAO,GAAGA,OAAO,IAAIrE,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;EAEA,QAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAxE,MAAAA,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAED,QAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAAC+F,IAAN,KAAe,SAAf,GAA2BpH,OAAO,CAACE,KAAnC,GAA2CF,OAAO,CAACC,KADrD,IAEI,IAFJ;EAGD;;EAED,QAAI9C,CAAC,CAACqE,OAAO,CAACQ,aAAR,EAAD,CAAD,CAA2BC,QAA3B,CAAoCvC,SAAS,CAACZ,IAA9C,KAAuD0C,OAAO,CAACb,WAAR,KAAwB9B,UAAU,CAACC,IAA9F,EAAoG;EAClG0C,MAAAA,OAAO,CAACb,WAAR,GAAsB9B,UAAU,CAACC,IAAjC;EACA;EACD;;EAEDqD,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ;EAEAc,IAAAA,OAAO,CAACb,WAAR,GAAsB9B,UAAU,CAACC,IAAjC;;EAEA,QAAI,CAAC0C,OAAO,CAAClB,MAAR,CAAezC,KAAhB,IAAyB,CAAC2D,OAAO,CAAClB,MAAR,CAAezC,KAAf,CAAqB4E,IAAnD,EAAyD;EACvDjB,MAAAA,OAAO,CAACiB,IAAR;EACA;EACD;;EAEDjB,IAAAA,OAAO,CAACd,QAAR,GAAmB2G,UAAU,CAAC,YAAM;EAClC,UAAI7F,OAAO,CAACb,WAAR,KAAwB9B,UAAU,CAACC,IAAvC,EAA6C;EAC3C0C,QAAAA,OAAO,CAACiB,IAAR;EACD;EACF,KAJ4B,EAI1BjB,OAAO,CAAClB,MAAR,CAAezC,KAAf,CAAqB4E,IAJK,CAA7B;EAKD;;WAEDV,SAAA,gBAAOV,KAAP,EAAcG,OAAd,EAAuB;EACrB,QAAMF,OAAO,GAAG,KAAKC,WAAL,CAAiBvE,QAAjC;EACAwE,IAAAA,OAAO,GAAGA,OAAO,IAAIrE,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;EAEA,QAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKD,WAAT,CACRF,KAAK,CAACI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;EAIAxE,MAAAA,CAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;EACD;;EAED,QAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAAC+F,IAAN,KAAe,UAAf,GAA4BpH,OAAO,CAACE,KAApC,GAA4CF,OAAO,CAACC,KADtD,IAEI,KAFJ;EAGD;;EAED,QAAIuB,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDM,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ;EAEAc,IAAAA,OAAO,CAACb,WAAR,GAAsB9B,UAAU,CAACE,GAAjC;;EAEA,QAAI,CAACyC,OAAO,CAAClB,MAAR,CAAezC,KAAhB,IAAyB,CAAC2D,OAAO,CAAClB,MAAR,CAAezC,KAAf,CAAqB0H,IAAnD,EAAyD;EACvD/D,MAAAA,OAAO,CAAC+D,IAAR;EACA;EACD;;EAED/D,IAAAA,OAAO,CAACd,QAAR,GAAmB2G,UAAU,CAAC,YAAM;EAClC,UAAI7F,OAAO,CAACb,WAAR,KAAwB9B,UAAU,CAACE,GAAvC,EAA4C;EAC1CyC,QAAAA,OAAO,CAAC+D,IAAR;EACD;EACF,KAJ4B,EAI1B/D,OAAO,CAAClB,MAAR,CAAezC,KAAf,CAAqB0H,IAJK,CAA7B;EAKD;;WAED1D,uBAAA,gCAAuB;EACrB,SAAK,IAAMjE,OAAX,IAAsB,KAAKgD,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoBhD,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;WAEDkD,aAAA,oBAAWR,MAAX,EAAmB;EACjB,QAAMgH,cAAc,GAAGnK,CAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBqB,IAAhB,EAAvB;EAEA/F,IAAAA,MAAM,CAACC,IAAP,CAAY0L,cAAZ,EACG3K,OADH,CACW,UAAC4K,QAAD,EAAc;EACrB,UAAIhK,qBAAqB,CAAChD,OAAtB,CAA8BgN,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;EAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KALH;EAOAjH,IAAAA,MAAM,qBACD,KAAKiB,WAAL,CAAiB3C,OADhB,EAED0I,cAFC,EAGD,OAAOhH,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;;EAMA,QAAI,OAAOA,MAAM,CAACzC,KAAd,KAAwB,QAA5B,EAAsC;EACpCyC,MAAAA,MAAM,CAACzC,KAAP,GAAe;EACb4E,QAAAA,IAAI,EAAEnC,MAAM,CAACzC,KADA;EAEb0H,QAAAA,IAAI,EAAEjF,MAAM,CAACzC;EAFA,OAAf;EAID;;EAED,QAAI,OAAOyC,MAAM,CAAC3C,KAAd,KAAwB,QAA5B,EAAsC;EACpC2C,MAAAA,MAAM,CAAC3C,KAAP,GAAe2C,MAAM,CAAC3C,KAAP,CAAa6J,QAAb,EAAf;EACD;;EAED,QAAI,OAAOlH,MAAM,CAAC2F,OAAd,KAA0B,QAA9B,EAAwC;EACtC3F,MAAAA,MAAM,CAAC2F,OAAP,GAAiB3F,MAAM,CAAC2F,OAAP,CAAeuB,QAAf,EAAjB;EACD;;EAEDzE,IAAAA,IAAI,CAAC0E,eAAL,CACE3K,IADF,EAEEwD,MAFF,EAGE,KAAKiB,WAAL,CAAiB/D,WAHnB;;EAMA,QAAI8C,MAAM,CAACjC,QAAX,EAAqB;EACnBiC,MAAAA,MAAM,CAAC5C,QAAP,GAAkBzC,YAAY,CAACqF,MAAM,CAAC5C,QAAR,EAAkB4C,MAAM,CAACnF,SAAzB,EAAoCmF,MAAM,CAAClF,UAA3C,CAA9B;EACD;;EAED,WAAOkF,MAAP;EACD;;WAEDqB,qBAAA,8BAAqB;EACnB,QAAMrB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,IAAMoH,GAAX,IAAkB,KAAKpH,MAAvB,EAA+B;EAC7B,YAAI,KAAKiB,WAAL,CAAiB3C,OAAjB,CAAyB8I,GAAzB,MAAkC,KAAKpH,MAAL,CAAYoH,GAAZ,CAAtC,EAAwD;EACtDpH,UAAAA,MAAM,CAACoH,GAAD,CAAN,GAAc,KAAKpH,MAAL,CAAYoH,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOpH,MAAP;EACD;;WAEDoF,iBAAA,0BAAiB;EACf,QAAMiC,IAAI,GAAGxK,CAAC,CAAC,KAAK6E,aAAL,EAAD,CAAd;EACA,QAAM4F,QAAQ,GAAGD,IAAI,CAACzN,IAAL,CAAU,OAAV,EAAmBQ,KAAnB,CAAyB4C,kBAAzB,CAAjB;;EACA,QAAIsK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC5M,MAAlC,EAA0C;EACxC2M,MAAAA,IAAI,CAAChC,WAAL,CAAiBiC,QAAQ,CAACC,IAAT,CAAc,EAAd,CAAjB;EACD;EACF;;WAEDpD,+BAAA,sCAA6BqD,UAA7B,EAAyC;EACvC,QAAMC,cAAc,GAAGD,UAAU,CAACE,QAAlC;EACA,SAAKjH,GAAL,GAAWgH,cAAc,CAACE,MAA1B;;EACA,SAAKvC,cAAL;;EACA,SAAK7B,kBAAL,CAAwB,KAAKD,cAAL,CAAoBkE,UAAU,CAAC9J,SAA/B,CAAxB;EACD;;WAEDgH,iBAAA,0BAAiB;EACf,QAAMjE,GAAG,GAAG,KAAKiB,aAAL,EAAZ;EACA,QAAMkG,mBAAmB,GAAG,KAAK5H,MAAL,CAAY7C,SAAxC;;EAEA,QAAIsD,GAAG,CAAC0F,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;EAC5C;EACD;;EAEDtJ,IAAAA,CAAC,CAAC4D,GAAD,CAAD,CAAO4E,WAAP,CAAmBjG,SAAS,CAACC,IAA7B;EACA,SAAKW,MAAL,CAAY7C,SAAZ,GAAwB,KAAxB;EACA,SAAK8H,IAAL;EACA,SAAK9C,IAAL;EACA,SAAKnC,MAAL,CAAY7C,SAAZ,GAAwByK,mBAAxB;EACD;;;YAIMC,mBAAP,0BAAwB7H,MAAxB,EAAgC;EAC9B,WAAO,KAAK8H,IAAL,CAAU,YAAY;EAC3B,UAAI1G,IAAI,GAAGvE,CAAC,CAAC,IAAD,CAAD,CAAQuE,IAAR,CAAa1E,QAAb,CAAX;;EACA,UAAMqL,OAAO,GAAG,OAAO/H,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACoB,IAAD,IAAS,eAAe4G,IAAf,CAAoBhI,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACoB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItB,OAAJ,CAAY,IAAZ,EAAkBiI,OAAlB,CAAP;EACAlL,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQuE,IAAR,CAAa1E,QAAb,EAAuB0E,IAAvB;EACD;;EAED,UAAI,OAAOpB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOoB,IAAI,CAACpB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIE,SAAJ,wBAAkCF,MAAlC,QAAN;EACD;;EACDoB,QAAAA,IAAI,CAACpB,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;;;0BA9mBoB;EACnB,aAAOvD,OAAP;EACD;;;0BAEoB;EACnB,aAAO6B,OAAP;EACD;;;0BAEiB;EAChB,aAAO9B,IAAP;EACD;;;0BAEqB;EACpB,aAAOE,QAAP;EACD;;;0BAEkB;EACjB,aAAOgC,KAAP;EACD;;;0BAEsB;EACrB,aAAO/B,SAAP;EACD;;;0BAEwB;EACvB,aAAOO,WAAP;EACD;;;;;EAulBH;;;;;;;EAMAL,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAasD,OAAO,CAAC+H,gBAArB;EACAhL,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAWyL,WAAX,GAAyBnI,OAAzB;;EACAjD,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAW0L,UAAX,GAAwB,YAAM;EAC5BrL,EAAAA,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAOkD,OAAO,CAAC+H,gBAAf;EACD,CAHD;;;;;;;;"}