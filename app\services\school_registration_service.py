#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校注册服务
处理用户注册时自动创建学校和分配权限的逻辑
"""

import uuid
import json
from datetime import datetime
from sqlalchemy import text
from app import db
from app.models import User, Role, UserRole, AdministrativeArea, Warehouse, StorageLocation


class SchoolRegistrationService:
    """学校注册服务类"""

    @staticmethod
    def register_school_and_user(form_data):
        """
        注册学校和用户

        Args:
            form_data: 包含学校名称和用户信息的字典

        Returns:
            tuple: (user, school_area) 创建的用户和学校区域对象

        Raises:
            ValueError: 当创建失败时抛出异常
        """
        try:
            # 1. 创建学校区域
            school_area = SchoolRegistrationService._create_school_area(
                form_data['school_name']
            )

            # 2. 创建用户
            user = SchoolRegistrationService._create_user(
                form_data, school_area.id
            )

            # 3. 分配学校管理员角色
            SchoolRegistrationService._assign_school_admin_role(user)

            # 4. 创建默认仓库（直接关联到学校区域）
            warehouse = SchoolRegistrationService._create_default_warehouse(
                school_area, user
            )

            # 5. 创建默认储存位置
            SchoolRegistrationService._create_default_storage_locations(warehouse)

            # 6. 创建会计科目体系
            SchoolRegistrationService._create_accounting_subjects(school_area)

            # 提交事务
            db.session.commit()

            return user, school_area

        except Exception as e:
            # 回滚事务
            db.session.rollback()
            raise ValueError(f"注册失败: {str(e)}")

    @staticmethod
    def _create_school_area(school_name):
        """
        创建学校区域

        Args:
            school_name: 学校名称

        Returns:
            AdministrativeArea: 创建的学校区域对象
        """
        # 生成唯一的学校代码
        school_code = SchoolRegistrationService._generate_school_code(school_name)

        # 使用原始SQL创建学校区域，避免时间精度问题
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO administrative_areas
        (name, code, level, parent_id, description, status, is_township_school, created_at)
        OUTPUT inserted.id
        VALUES
        (:name, :code, :level, :parent_id, :description, :status, :is_township_school, GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': school_name,
            'code': school_code,
            'level': 3,  # 学校级别
            'parent_id': None,  # 独立学校，无上级区域
            'description': f'通过开放注册创建的学校：{school_name}',
            'status': 1,  # 启用状态
            'is_township_school': False
        })

        # 获取新创建的区域ID
        area_id = result.scalar()

        # 返回区域对象
        return AdministrativeArea.query.get(area_id)

    @staticmethod
    def _create_user(form_data, area_id):
        """
        创建用户

        Args:
            form_data: 表单数据
            area_id: 学校区域ID

        Returns:
            User: 创建的用户对象
        """
        # 使用原始SQL创建用户，避免时间精度问题
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO users
        (username, password_hash, email, real_name, phone, status, area_id, area_level, created_at)
        OUTPUT inserted.id
        VALUES
        (:username, :password_hash, :email, :real_name, :phone, :status, :area_id, :area_level, GETDATE())
        ''')

        # 创建临时用户对象来生成密码哈希
        temp_user = User()
        temp_user.set_password(form_data['password'])

        result = db.session.execute(sql, {
            'username': form_data['username'],
            'password_hash': temp_user.password_hash,
            'email': form_data['email'],
            'real_name': form_data['real_name'],
            'phone': form_data['phone'],
            'status': 1,  # 启用状态
            'area_id': area_id,
            'area_level': 3  # 学校级别
        })

        # 获取新创建的用户ID
        user_id = result.scalar()

        # 返回用户对象
        return User.query.get(user_id)

    @staticmethod
    def _assign_school_admin_role(user):
        """
        为用户分配学校管理员角色

        Args:
            user: 用户对象
        """
        # 查找或创建学校管理员角色
        school_admin_role = Role.query.filter_by(name='学校管理员').first()

        if not school_admin_role:
            # 如果不存在学校管理员角色，创建一个
            school_admin_role = SchoolRegistrationService._create_school_admin_role()

        # 分配角色
        user_role = UserRole(user_id=user.id, role_id=school_admin_role.id)
        db.session.add(user_role)

    @staticmethod
    def _create_school_admin_role():
        """
        创建学校管理员角色

        Returns:
            Role: 创建的角色对象
        """
        # 学校管理员的权限配置
        permissions = {
            "*": ["*"]  # 给予所有权限，让用户感觉这是他们的专用系统
        }

        # 使用原始SQL创建角色
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO roles
        (name, description, permissions, created_at)
        OUTPUT inserted.id
        VALUES
        (:name, :description, :permissions, GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': '学校管理员',
            'description': '学校管理员，负责管理学校信息',
            'permissions': json.dumps(permissions)  # 转换为JSON格式
        })

        # 获取新创建的角色ID
        role_id = result.scalar()

        # 返回角色对象
        return Role.query.get(role_id)

    # 注释：不再创建默认食堂区域，直接使用学校区域
    # @staticmethod
    # def _create_default_canteen(school_area):
    #     """
    #     为学校创建默认食堂区域（已废弃）
    #     现在直接使用学校区域，不再创建额外的食堂区域
    #     """
    #     pass

    @staticmethod
    def _create_default_warehouse(school_area, user):
        """
        为学校创建默认仓库

        Args:
            school_area: 学校区域对象
            user: 用户对象（作为仓库管理员）

        Returns:
            Warehouse: 创建的仓库对象
        """
        # 使用原始SQL创建仓库
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO warehouses
        (name, area_id, location, manager_id, capacity, capacity_unit,
         temperature_range, humidity_range, status, notes, created_at, updated_at)
        OUTPUT inserted.id
        VALUES
        (:name, :area_id, :location, :manager_id, :capacity, :capacity_unit,
         :temperature_range, :humidity_range, :status, :notes, GETDATE(), GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': f'{school_area.name}中心仓库',
            'area_id': school_area.id,
            'location': '食堂一楼',
            'manager_id': user.id,
            'capacity': 500.0,
            'capacity_unit': '立方米',
            'temperature_range': '-18°C ~ 25°C',
            'humidity_range': '45% ~ 65%',
            'status': '正常',
            'notes': '学校默认仓库，包含常温、冷藏、冷冻三个储存区域'
        })

        # 获取新创建的仓库ID
        warehouse_id = result.scalar()

        # 返回仓库对象
        return Warehouse.query.get(warehouse_id)

    @staticmethod
    def _create_accounting_subjects(school_area):
        """
        为学校创建完整的会计科目体系

        Args:
            school_area: 学校区域对象
        """
        try:
            # 导入会计科目模型
            from app.models_financial import AccountingSubject

            # 学校食堂专用会计科目体系
            subjects_data = [
                # 资产类科目 (1001-1999)
                {'code': '1001', 'name': '库存现金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '食堂现金收支管理'},
                {'code': '1002', 'name': '银行存款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '食堂银行账户资金'},
                {'code': '1003', 'name': '其他货币资金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '支付宝、微信等电子支付资金'},
                {'code': '1101', 'name': '应收账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '应收学生、教师餐费'},
                {'code': '1102', 'name': '预付账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '预付供应商货款'},
                {'code': '1103', 'name': '其他应收款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '其他应收款项'},
                {'code': '1201', 'name': '原材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '食材原料库存'},
                {'code': '120101', 'name': '蔬菜类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '各类蔬菜原料'},
                {'code': '120102', 'name': '肉类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '猪肉、牛肉、鸡肉等'},
                {'code': '120103', 'name': '水产类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '鱼类、虾类等水产品'},
                {'code': '120104', 'name': '粮油类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '大米、面粉、食用油等'},
                {'code': '120105', 'name': '调料类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '盐、糖、酱油等调料'},
                {'code': '120106', 'name': '冷冻食品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1201', 'description': '冷冻肉类、速冻食品等'},
                {'code': '1202', 'name': '低值易耗品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '餐具、清洁用品等'},
                {'code': '1301', 'name': '固定资产', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '食堂设备设施'},
                {'code': '130101', 'name': '厨房设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1301', 'description': '炉灶、蒸箱、炒锅等'},
                {'code': '130102', 'name': '制冷设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1301', 'description': '冰箱、冷库等'},
                {'code': '130103', 'name': '餐厅设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1301', 'description': '餐桌、餐椅、餐具等'},
                {'code': '130104', 'name': '清洗设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'parent_code': '1301', 'description': '洗碗机、消毒柜等'},
                {'code': '1302', 'name': '累计折旧', 'subject_type': '资产', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '固定资产累计折旧'},

                # 负债类科目 (2001-2999)
                {'code': '2001', 'name': '应付账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '应付供应商货款'},
                {'code': '2002', 'name': '预收账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '预收学生餐费'},
                {'code': '2003', 'name': '其他应付款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '其他应付款项'},
                {'code': '2101', 'name': '应付职工薪酬', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '食堂员工工资福利'},
                {'code': '210101', 'name': '应付工资', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 2, 'parent_code': '2101', 'description': '应付员工基本工资'},
                {'code': '210102', 'name': '应付福利费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 2, 'parent_code': '2101', 'description': '应付员工福利费用'},
                {'code': '2201', 'name': '应交税费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '应交各项税费'},
                {'code': '2301', 'name': '短期借款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '短期借款'},

                # 净资产类科目 (3001-3999)
                {'code': '3001', 'name': '实收资本', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '学校投入食堂资本'},
                {'code': '3002', 'name': '资本公积', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '资本溢价等'},
                {'code': '3101', 'name': '本年利润', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '当年经营成果'},
                {'code': '3102', 'name': '利润分配', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '利润分配情况'},

                # 收入类科目 (4001-4999)
                {'code': '4001', 'name': '主营业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '餐饮服务收入'},
                {'code': '400101', 'name': '学生餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'parent_code': '4001', 'description': '学生用餐收入'},
                {'code': '400102', 'name': '教师餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'parent_code': '4001', 'description': '教师用餐收入'},
                {'code': '400103', 'name': '外来人员餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'parent_code': '4001', 'description': '外来人员用餐收入'},
                {'code': '4002', 'name': '其他业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '其他经营收入'},
                {'code': '4003', 'name': '营业外收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'parent_code': None, 'description': '非经营性收入'},
                {'code': '400301', 'name': '政府补贴收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'parent_code': '4003', 'description': '政府给予的补贴'},
                {'code': '400302', 'name': '捐赠收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'parent_code': '4003', 'description': '接受的捐赠'},

                # 费用类科目 (5001-5999)
                {'code': '5001', 'name': '主营业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '餐饮服务直接成本'},
                {'code': '500101', 'name': '食材成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5001', 'description': '食材采购成本'},
                {'code': '500102', 'name': '人工成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5001', 'description': '直接人工费用'},
                {'code': '500103', 'name': '制造费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5001', 'description': '间接制造费用'},
                {'code': '5002', 'name': '管理费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '管理费用'},
                {'code': '500201', 'name': '办公费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5002', 'description': '办公用品费用'},
                {'code': '500202', 'name': '水电费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5002', 'description': '水电气费用'},
                {'code': '500203', 'name': '维修费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5002', 'description': '设备维修费用'},
                {'code': '500204', 'name': '清洁费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5002', 'description': '清洁卫生费用'},
                {'code': '500205', 'name': '培训费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'parent_code': '5002', 'description': '员工培训费用'},
                {'code': '5003', 'name': '财务费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '财务费用'},
                {'code': '5004', 'name': '营业外支出', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '非经营性支出'},
                {'code': '5005', 'name': '所得税费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'parent_code': None, 'description': '所得税费用'}
            ]

            # 使用原始SQL批量插入会计科目
            for subject_data in subjects_data:
                # 处理父级科目ID
                parent_id = None
                if subject_data['parent_code']:
                    parent_subject = AccountingSubject.query.filter_by(
                        code=subject_data['parent_code'],
                        area_id=school_area.id
                    ).first()
                    if parent_subject:
                        parent_id = parent_subject.id

                sql = text('''
                INSERT INTO accounting_subjects
                (code, name, subject_type, balance_direction, level, parent_id, description,
                 is_active, is_system, area_id, created_by, created_at)
                VALUES
                (:code, :name, :subject_type, :balance_direction, :level, :parent_id, :description,
                 :is_active, :is_system, :area_id, :created_by, GETDATE())
                ''')

                db.session.execute(sql, {
                    'code': subject_data['code'],
                    'name': subject_data['name'],
                    'subject_type': subject_data['subject_type'],
                    'balance_direction': subject_data['balance_direction'],
                    'level': subject_data['level'],
                    'parent_id': parent_id,
                    'description': subject_data['description'],
                    'is_active': True,
                    'is_system': True,  # 标记为系统科目
                    'area_id': school_area.id,
                    'created_by': 1  # 系统创建，使用ID为1的用户
                })

        except Exception as e:
            # 如果创建会计科目失败，记录错误但不影响学校注册
            print(f"创建会计科目时出错: {str(e)}")
            # 可以选择回滚会计科目相关的操作，但保留学校注册

    @staticmethod
    def _create_default_storage_locations(warehouse):
        """
        为仓库创建默认储存位置

        Args:
            warehouse: 仓库对象
        """
        # 定义三个基本储存位置的配置
        storage_configs = [
            {
                'name': '储存室',
                'location_code': 'A001',
                'storage_type': '常温',
                'capacity': 200.0,
                'capacity_unit': '立方米',
                'temperature_range': '15°C ~ 25°C',
                'notes': '常温储存区域，适合储存干货、调料等常温食材'
            },
            {
                'name': '冷藏区',
                'location_code': 'B001',
                'storage_type': '冷藏',
                'capacity': 150.0,
                'capacity_unit': '立方米',
                'temperature_range': '0°C ~ 4°C',
                'notes': '冷藏储存区域，适合储存蔬菜、水果、乳制品等需要冷藏的食材'
            },
            {
                'name': '冷冻区',
                'location_code': 'C001',
                'storage_type': '冷冻',
                'capacity': 150.0,
                'capacity_unit': '立方米',
                'temperature_range': '-18°C ~ -12°C',
                'notes': '冷冻储存区域，适合储存肉类、海鲜、冷冻食品等需要冷冻的食材'
            }
        ]

        # 使用原始SQL创建储存位置
        sql = text('''
        INSERT INTO storage_locations
        (warehouse_id, name, location_code, storage_type, capacity, capacity_unit,
         temperature_range, status, notes, created_at, updated_at)
        VALUES
        (:warehouse_id, :name, :location_code, :storage_type, :capacity, :capacity_unit,
         :temperature_range, :status, :notes, GETDATE(), GETDATE())
        ''')

        # 批量创建储存位置
        for config in storage_configs:
            db.session.execute(sql, {
                'warehouse_id': warehouse.id,
                'name': config['name'],
                'location_code': config['location_code'],
                'storage_type': config['storage_type'],
                'capacity': config['capacity'],
                'capacity_unit': config['capacity_unit'],
                'temperature_range': config['temperature_range'],
                'status': '正常',
                'notes': config['notes']
            })

    @staticmethod
    def _generate_school_code(school_name):
        """
        生成唯一的学校代码

        Args:
            school_name: 学校名称

        Returns:
            str: 学校代码
        """
        # 使用时间戳和UUID生成唯一代码
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_id = str(uuid.uuid4())[:8].upper()

        # 取学校名称的前两个字符作为前缀
        name_prefix = school_name[:2] if len(school_name) >= 2 else school_name

        return f"SCHOOL_{name_prefix}_{timestamp}_{unique_id}"

    @staticmethod
    def check_school_name_exists(school_name):
        """
        检查学校名称是否已存在

        Args:
            school_name: 学校名称

        Returns:
            bool: 如果存在返回True，否则返回False
        """
        existing_school = AdministrativeArea.query.filter_by(
            name=school_name,
            level=3  # 学校级别
        ).first()

        return existing_school is not None
