"""
系统修复API - 提供通过API执行各种系统修复操作的功能
"""

from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from app import db
from app.system_fix import system_fix_bp
from sqlalchemy import text
import traceback
import json
import os
from datetime import datetime
from functools import wraps

# API密钥验证装饰器
def api_key_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key != current_app.config.get('API_KEY', 'default_api_key'):
            return jsonify({'error': '无效的API密钥'}), 401
        return f(*args, **kwargs)
    return decorated_function

# 修复操作日志
def log_api_operation(operation, status, details=None):
    """记录API修复操作日志"""
    log_dir = os.path.join(current_app.root_path, 'logs')
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, f'system_fix_api_{datetime.now().strftime("%Y%m%d")}.log')

    with open(log_file, 'a', encoding='utf-8') as f:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        client_ip = request.remote_addr
        log_entry = f"[{timestamp}] {client_ip} - {operation} - {status}"
        if details:
            log_entry += f" - {details}"
        f.write(log_entry + "\n")

@system_fix_bp.route('/api/fix_recipes_table', methods=['GET'])
@api_key_required
def api_fix_recipes_table():
    """API: 修复recipes表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'recipes'"))
        if result.scalar() == 0:
            log_api_operation("api_fix_recipes_table", "失败", "表不存在")
            return jsonify({'status': 'error', 'message': 'recipes表不存在，请先创建表'}), 404

        # 获取当前表结构
        result = db.session.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'recipes'"))
        existing_columns = [row[0].lower() for row in result.fetchall()]

        # 需要添加的列
        columns_to_add = {
            'category': 'NVARCHAR(50)',
            'meal_type': 'NVARCHAR(20)',
            'main_image': 'NVARCHAR(200)',
            'calories': 'FLOAT',
            'cooking_steps': 'NVARCHAR(MAX)',
            'parent_id': 'INT',
            'is_template': 'BIT DEFAULT 0',
            'template_type': 'NVARCHAR(20)',
            'variation_reason': 'NVARCHAR(200)',
            'version': 'INT DEFAULT 1'
        }

        # 添加缺少的列
        added_columns = []
        for column, data_type in columns_to_add.items():
            if column.lower() not in existing_columns:
                try:
                    sql = f"ALTER TABLE recipes ADD {column} {data_type}"
                    db.session.execute(text(sql))
                    added_columns.append(f"{column} ({data_type})")
                except Exception as e:
                    log_api_operation("api_fix_recipes_table", "部分失败", f"添加列 {column} 时出错: {str(e)}")
                    db.session.rollback()
                    return jsonify({
                        'status': 'error',
                        'message': f"添加列 {column} 时出错: {str(e)}",
                        'added_columns': added_columns
                    }), 500

        # 提交事务
        db.session.commit()

        if added_columns:
            log_api_operation("api_fix_recipes_table", "成功", f"已添加列: {', '.join(added_columns)}")
            return jsonify({
                'status': 'success',
                'message': 'recipes表结构修复完成',
                'added_columns': added_columns
            })
        else:
            log_api_operation("api_fix_recipes_table", "成功", "表结构已是最新")
            return jsonify({
                'status': 'success',
                'message': 'recipes表结构已是最新，无需修复'
            })

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        log_api_operation("api_fix_recipes_table", "失败", error_details)
        return jsonify({
            'status': 'error',
            'message': f"修复recipes表时出错: {str(e)}"
        }), 500

@system_fix_bp.route('/api/fix_food_samples_table', methods=['GET'])
@api_key_required
def api_fix_food_samples_table():
    """API: 修复food_samples表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'food_samples'"))
        if result.scalar() == 0:
            log_api_operation("api_fix_food_samples_table", "失败", "表不存在")
            return jsonify({'status': 'error', 'message': 'food_samples表不存在，请先创建表'}), 404

        # 获取当前表结构
        result = db.session.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'food_samples'"))
        existing_columns = [row[0].lower() for row in result.fetchall()]

        # 需要添加的列
        columns_to_add = {
            'operator_id': 'INT',
            'destruction_time': 'DATETIME2',
            'destruction_operator_id': 'INT'
        }

        # 添加缺少的列
        added_columns = []
        for column, data_type in columns_to_add.items():
            if column.lower() not in existing_columns:
                try:
                    sql = f"ALTER TABLE food_samples ADD {column} {data_type}"
                    db.session.execute(text(sql))
                    added_columns.append(f"{column} ({data_type})")
                except Exception as e:
                    log_api_operation("api_fix_food_samples_table", "部分失败", f"添加列 {column} 时出错: {str(e)}")
                    db.session.rollback()
                    return jsonify({
                        'status': 'error',
                        'message': f"添加列 {column} 时出错: {str(e)}",
                        'added_columns': added_columns
                    }), 500

        # 提交事务
        db.session.commit()

        if added_columns:
            log_api_operation("api_fix_food_samples_table", "成功", f"已添加列: {', '.join(added_columns)}")
            return jsonify({
                'status': 'success',
                'message': 'food_samples表结构修复完成',
                'added_columns': added_columns
            })
        else:
            log_api_operation("api_fix_food_samples_table", "成功", "表结构已是最新")
            return jsonify({
                'status': 'success',
                'message': 'food_samples表结构已是最新，无需修复'
            })

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        log_api_operation("api_fix_food_samples_table", "失败", error_details)
        return jsonify({
            'status': 'error',
            'message': f"修复food_samples表时出错: {str(e)}"
        }), 500

@system_fix_bp.route('/api/fix_purchase_orders_table', methods=['GET'])
@api_key_required
def api_fix_purchase_orders_table():
    """API: 修复purchase_orders表结构"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_orders'"))
        if result.scalar() > 0:
            log_api_operation("api_fix_purchase_orders_table", "成功", "表已存在")
            return jsonify({'status': 'success', 'message': 'purchase_orders表已存在，无需创建'})

        # 创建purchase_orders表
        create_table_sql = """
        CREATE TABLE purchase_orders (
            id INT IDENTITY(1,1) PRIMARY KEY,
            order_number NVARCHAR(50) NOT NULL,
            supplier_id INT NOT NULL,
            requisition_id INT NULL,
            area_id INT NOT NULL,
            total_amount DECIMAL(12, 2) NOT NULL,
            order_date DATETIME2 NOT NULL,
            expected_delivery_date DATE NULL,
            payment_terms NVARCHAR(200) NULL,
            delivery_terms NVARCHAR(200) NULL,
            status NVARCHAR(20) NOT NULL,
            delivery_date DATETIME2 NULL,
            created_by INT NOT NULL,
            approved_by INT NULL,
            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
            updated_at DATETIME2 NULL
        )
        """
        db.session.execute(text(create_table_sql))

        # 创建purchase_order_items表
        result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_order_items'"))
        created_tables = ['purchase_orders']
        if result.scalar() == 0:
            create_items_table_sql = """
            CREATE TABLE purchase_order_items (
                id INT IDENTITY(1,1) PRIMARY KEY,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                ingredient_id INT NOT NULL,
                quantity FLOAT NOT NULL,
                unit NVARCHAR(20) NOT NULL,
                unit_price DECIMAL(10, 2) NOT NULL,
                total_price DECIMAL(10, 2) NOT NULL,
                received_quantity FLOAT NULL DEFAULT 0,
                notes NVARCHAR(MAX) NULL,
                created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
                updated_at DATETIME2 NULL
            )
            """
            db.session.execute(text(create_items_table_sql))
            created_tables.append('purchase_order_items')

        # 提交事务
        db.session.commit()

        log_api_operation("api_fix_purchase_orders_table", "成功", f"创建了表: {', '.join(created_tables)}")
        return jsonify({
            'status': 'success',
            'message': 'purchase_orders表结构修复完成',
            'created_tables': created_tables
        })

    except Exception as e:
        db.session.rollback()
        error_details = traceback.format_exc()
        log_api_operation("api_fix_purchase_orders_table", "失败", error_details)
        return jsonify({
            'status': 'error',
            'message': f"修复purchase_orders表时出错: {str(e)}"
        }), 500

@system_fix_bp.route('/api/enhanced_auto_fix', methods=['GET'])
@api_key_required
def api_enhanced_auto_fix():
    """API: 增强版自动检测和修复所有问题"""
    try:
        from app.system_fix.enhanced_auto_fix import enhanced_auto_fix_all

        # 执行增强版自动修复
        result = enhanced_auto_fix_all()

        # 处理结果
        fixed_tables = result.get('fixed_tables', [])
        fixed_columns = result.get('fixed_columns', {})
        type_issues = result.get('type_issues', {})
        fixed_files = result.get('fixed_files', [])

        # 构建响应
        response = {
            'status': 'success',
            'message': '增强版自动修复完成',
            'fixed_tables': fixed_tables,
            'fixed_columns': fixed_columns,
            'type_issues': type_issues,
            'fixed_files': fixed_files
        }

        log_api_operation("api_enhanced_auto_fix", "成功",
                         f"已修复表: {fixed_tables}, 已修复列: {fixed_columns}, " +
                         f"类型问题: {len(type_issues)}, 修复文件: {len(fixed_files)}")
        return jsonify(response)

    except Exception as e:
        error_details = traceback.format_exc()
        log_api_operation("api_enhanced_auto_fix", "失败", error_details)
        return jsonify({
            'status': 'error',
            'message': f"增强版自动修复时出错: {str(e)}"
        }), 500

@system_fix_bp.route('/api/verify_database_structure', methods=['GET'])
@api_key_required
def api_verify_database_structure():
    """API: 验证数据库结构"""
    try:
        # 获取所有表
        result = db.session.execute(text("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"))
        tables = [row[0] for row in result.fetchall()]

        # 检查关键表是否存在
        required_tables = [
            'users', 'roles', 'user_roles', 'administrative_areas',
            'ingredients', 'recipes', 'suppliers', 'menu_plans'
        ]

        missing_tables = [table for table in required_tables if table.lower() not in [t.lower() for t in tables]]

        if missing_tables:
            log_api_operation("api_verify_database_structure", "失败", f"缺少表: {', '.join(missing_tables)}")
            return jsonify({
                'status': 'error',
                'message': f"缺少以下关键表: {', '.join(missing_tables)}",
                'missing_tables': missing_tables,
                'existing_tables': tables
            }), 404
        else:
            log_api_operation("api_verify_database_structure", "成功", f"检查了 {len(tables)} 个表")
            return jsonify({
                'status': 'success',
                'message': '所有关键表都存在',
                'tables': tables
            })

    except Exception as e:
        error_details = traceback.format_exc()
        log_api_operation("api_verify_database_structure", "失败", error_details)
        return jsonify({
            'status': 'error',
            'message': f"验证数据库结构时出错: {str(e)}"
        }), 500
