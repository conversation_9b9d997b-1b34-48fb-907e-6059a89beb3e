{"version": 3, "sources": ["../src/index.js"], "names": ["$", "TypeError", "version", "fn", "j<PERSON>y", "split", "min<PERSON><PERSON><PERSON>", "ltMajor", "minMinor", "minPatch", "max<PERSON><PERSON><PERSON>", "Error"], "mappings": "AAaA;;;;;;AAOA,CAAC,UAACA,CAAD,EAAO;AACN,MAAI,OAAOA,CAAP,KAAa,WAAjB,EAA8B;AAC5B,UAAM,IAAIC,SAAJ,CAAc,kGAAd,CAAN;AACD;;AAED,MAAMC,UAAUF,EAAEG,EAAF,CAAKC,MAAL,CAAYC,KAAZ,CAAkB,GAAlB,EAAuB,CAAvB,EAA0BA,KAA1B,CAAgC,GAAhC,CAAhB;AACA,MAAMC,WAAW,CAAjB;AACA,MAAMC,UAAU,CAAhB;AACA,MAAMC,WAAW,CAAjB;AACA,MAAMC,WAAW,CAAjB;AACA,MAAMC,WAAW,CAAjB;;AAEA,MAAIR,QAAQ,CAAR,IAAaK,OAAb,IAAwBL,QAAQ,CAAR,IAAaM,QAArC,IAAiDN,QAAQ,CAAR,MAAeI,QAAf,IAA2BJ,QAAQ,CAAR,MAAeM,QAA1C,IAAsDN,QAAQ,CAAR,IAAaO,QAApH,IAAgIP,QAAQ,CAAR,KAAcQ,QAAlJ,EAA4J;AAC1J,UAAM,IAAIC,KAAJ,CAAU,8EAAV,CAAN;AACD;AACF,CAfD,EAeGX,CAfH", "sourcesContent": ["import $ from 'jquery'\nimport Al<PERSON> from './alert'\nimport <PERSON><PERSON> from './button'\nimport Carousel from './carousel'\nimport Collapse from './collapse'\nimport Dropdown from './dropdown'\nimport Modal from './modal'\nimport Popover from './popover'\nimport Scrollspy from './scrollspy'\nimport Tab from './tab'\nimport Tooltip from './tooltip'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.1.2): index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n(($) => {\n  if (typeof $ === 'undefined') {\n    throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n  }\n\n  const version = $.fn.jquery.split(' ')[0].split('.')\n  const minMajor = 1\n  const ltMajor = 2\n  const minMinor = 9\n  const minPatch = 1\n  const maxMajor = 4\n\n  if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n    throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n  }\n})($)\n\nexport {\n  Util,\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  Scrollspy,\n  Tab,\n  Tooltip\n}\n"], "file": "index.js"}