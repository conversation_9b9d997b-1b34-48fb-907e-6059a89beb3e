.console {
	position: relative;
	width: 100%;
	min-height: 400px;
}

.console-container {
	border-radius: 4px;
	position: absolute;
	border: 1px solid #d4d4d4;
	padding: 2px;
	overflow: scroll;
	top: 2px;
	left: 2px;
	right: 2px;
	bottom: 5em;
}

.console-line {
	position: absolute;
	left: 2px;
	right: 2px;
	bottom: 2px;
}

.console-line input {
	width: 100%;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	height: 2em;
}

.console .cmd {
	background-color: #f5f5f5;
	padding: 2px;
	margin: 1px;
}

.console .response {
	background-color: #f0f0f0;
	padding: 2px;
	margin: 1px;
}

.console .error {
	color: red;
}