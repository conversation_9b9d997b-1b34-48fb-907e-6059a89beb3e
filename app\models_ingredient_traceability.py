"""
食材溯源模块的数据库模型
这些模型将用于实现食材批次管理和溯源功能
"""

from app import db
from datetime import datetime
from sqlalchemy.dialects.mssql import DATETIME2

class MaterialBatch(db.Model):
    """食材批次表 - 溯源的核心"""
    __tablename__ = 'material_batches'

    id = db.Column(db.Integer, primary_key=1)
    batch_number = db.Column(db.String(50), nullable=0, unique=1)  # 批次编号
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=0)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=0)
    production_date = db.Column(db.Date, nullable=0)  # 生产日期
    expiry_date = db.Column(db.Date, nullable=0)  # 过期日期
    production_batch_no = db.Column(db.String(50), nullable=1)  # 生产批号（供应商提供）
    origin_place = db.Column(db.String(100), nullable=1)  # 产地信息
    inspection_no = db.Column(db.String(50), nullable=1)  # 检验编号
    certificate_no = db.Column(db.String(50), nullable=1)  # 合格证编号
    initial_quantity = db.Column(db.Float, nullable=0)  # 初始数量
    current_quantity = db.Column(db.Float, nullable=0)  # 当前批次库存量
    unit = db.Column(db.String(20), nullable=0)  # 单位
    unit_price = db.Column(db.Numeric(10, 2), nullable=1)  # 单价
    status = db.Column(db.String(20), nullable=0, default='正常')  # 状态（正常/预警/过期）
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=0)  # 所属区域
    remark = db.Column(db.Text, nullable=1)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    ingredient = db.relationship('Ingredient', backref='batches')
    supplier = db.relationship('Supplier', backref='material_batches')
    area = db.relationship('AdministrativeArea', backref='material_batches')
    trace_documents = db.relationship('TraceDocument', backref='batch', lazy='dynamic')
    batch_flows = db.relationship('BatchFlow', backref='batch', lazy='dynamic')

    def to_dict(self):
        return {
            'id': self.id,
            'batch_number': self.batch_number,
            'ingredient_id': self.ingredient_id,
            'ingredient_name': self.ingredient.name if self.ingredient else None,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,
            'production_date': self.production_date.strftime('%Y-%m-%d') if self.production_date else None,
            'expiry_date': self.expiry_date.strftime('%Y-%m-%d') if self.expiry_date else None,
            'production_batch_no': self.production_batch_no,
            'origin_place': self.origin_place,
            'inspection_no': self.inspection_no,
            'certificate_no': self.certificate_no,
            'initial_quantity': self.initial_quantity,
            'current_quantity': self.current_quantity,
            'unit': self.unit,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'status': self.status,
            'area_id': self.area_id,
            'area_name': self.area.name if self.area else None,
            'remark': self.remark,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class TraceDocument(db.Model):
    """溯源文档表 - 存储与批次相关的证明文件"""
    __tablename__ = 'trace_documents'

    id = db.Column(db.Integer, primary_key=1)
    batch_id = db.Column(db.Integer, db.ForeignKey('material_batches.id'), nullable=0)
    document_type = db.Column(db.String(20), nullable=0)  # 文档类型（检验报告/合格证/其他）
    document_no = db.Column(db.String(50), nullable=1)  # 文档编号
    document_path = db.Column(db.String(200), nullable=0)  # 文档路径
    upload_time = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)  # 上传时间
    uploader_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)  # 上传人ID
    remark = db.Column(db.Text, nullable=1)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    uploader = db.relationship('User', backref='uploaded_documents')

    def to_dict(self):
        return {
            'id': self.id,
            'batch_id': self.batch_id,
            'document_type': self.document_type,
            'document_no': self.document_no,
            'document_path': self.document_path,
            'upload_time': self.upload_time.strftime('%Y-%m-%d %H:%M:%S'),
            'uploader_id': self.uploader_id,
            'uploader_name': self.uploader.real_name or self.uploader.username,
            'remark': self.remark,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class BatchFlow(db.Model):
    """批次流水表 - 记录批次的所有变动"""
    __tablename__ = 'batch_flows'

    id = db.Column(db.Integer, primary_key=1)
    batch_id = db.Column(db.Integer, db.ForeignKey('material_batches.id'), nullable=0)
    flow_type = db.Column(db.String(20), nullable=0)  # 流水类型（入库/出库/调整）
    flow_direction = db.Column(db.String(10), nullable=0)  # 流向（增加/减少）
    quantity = db.Column(db.Float, nullable=0)  # 数量
    unit = db.Column(db.String(20), nullable=0)  # 单位
    related_id = db.Column(db.Integer, nullable=1)  # 关联单据ID
    related_type = db.Column(db.String(20), nullable=1)  # 关联单据类型（入库单/出库单/消耗计划）
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)  # 操作人ID
    flow_date = db.Column(DATETIME2(precision=1), nullable=0, default=lambda: datetime.now().replace(microsecond=0))  # 流水日期
    remark = db.Column(db.Text, nullable=1)  # 备注
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    operator = db.relationship('User', backref='batch_flows')

    def to_dict(self):
        return {
            'id': self.id,
            'batch_id': self.batch_id,
            'batch_number': self.batch.batch_number if self.batch else None,
            'flow_type': self.flow_type,
            'flow_direction': self.flow_direction,
            'quantity': self.quantity,
            'unit': self.unit,
            'related_id': self.related_id,
            'related_type': self.related_type,
            'operator_id': self.operator_id,
            'operator_name': self.operator.real_name or self.operator.username,
            'flow_date': self.flow_date.strftime('%Y-%m-%d %H:%M:%S'),
            'remark': self.remark,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
