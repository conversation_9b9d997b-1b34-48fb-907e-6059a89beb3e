"""
系统设置模型模块
"""

from app import db
from datetime import datetime
import json
from sqlalchemy.dialects.mssql import DATETIME2

class SystemSetting(db.Model):
    """系统设置表"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    value_type = db.Column(db.String(20), nullable=False)  # 值类型：string, integer, boolean等
    description = db.Column(db.String(200), nullable=True)
    group = db.Column(db.String(50), nullable=True)  # 设置组
    is_public = db.Column(db.Integer, nullable=True)  # 是否公开，1表示是，0表示否
    created_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    updated_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0), nullable=False)
    setting_type = db.Column(db.String(50), nullable=True)  # 分类：基本设置、安全设置、显示设置等

    def __repr__(self):
        return f'<SystemSetting {self.key}>'

    @staticmethod
    def get_value(key, default=None):
        """获取设置值"""
        setting = SystemSetting.query.filter_by(key=key).first()
        if setting:
            return setting.value
        return default

    @staticmethod
    def set_value(key, value, description=None, category=None, user_id=None, value_type='string', group=None, is_public=True):
        """设置值"""
        # 确保 is_public 是 SQL Server 可以接受的类型
        if is_public is not None:
            # 将 is_public 转换为整数 1 或 0
            is_public = 1 if is_public else 0

        setting = SystemSetting.query.filter_by(key=key).first()
        if setting:
            # 使用原始SQL更新记录，避免SQLAlchemy的类型转换问题
            from sqlalchemy import text
            sql = text("""
                UPDATE system_settings
                SET value = :value,
                    updated_at = :updated_at
                WHERE id = :id
            """)

            now = datetime.now().replace(microsecond=0)

            db.session.execute(sql, {
                'value': value,
                'updated_at': now,
                'id': setting.id
            })

            # 如果提供了其他参数，也更新它们
            if description is not None or category is not None or group is not None or is_public is not None:
                update_fields = []
                params = {'id': setting.id}

                if description is not None:
                    update_fields.append("description = :description")
                    params['description'] = description

                if category is not None:
                    update_fields.append("setting_type = :setting_type")
                    params['setting_type'] = category

                if group is not None:
                    update_fields.append("[group] = :group")
                    params['group'] = group

                if is_public is not None:
                    update_fields.append("is_public = :is_public")
                    params['is_public'] = is_public

                if update_fields:
                    additional_sql = text(f"""
                        UPDATE system_settings
                        SET {', '.join(update_fields)}
                        WHERE id = :id
                    """)
                    db.session.execute(additional_sql, params)
        else:
            # 使用原始 SQL 插入记录，避免 SQLAlchemy 的类型转换
            from sqlalchemy import text
            sql = text("""
                INSERT INTO system_settings ([key], value, value_type, description, [group], is_public, created_at, updated_at, setting_type)
                VALUES (:key, :value, :value_type, :description, :group, :is_public, :created_at, :updated_at, :setting_type)
            """)

            now = datetime.now().replace(microsecond=0)

            db.session.execute(sql, {
                'key': key,
                'value': value,
                'value_type': value_type,
                'description': description,
                'group': group,
                'is_public': is_public,
                'created_at': now,
                'updated_at': now,
                'setting_type': category
            })

            # 获取刚刚插入的记录
            setting = SystemSetting.query.filter_by(key=key).first()

        db.session.commit()
        return setting

class DatabaseBackup(db.Model):
    """数据库备份记录表"""
    __tablename__ = 'database_backups'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(200), nullable=False)
    backup_type = db.Column(db.String(20), nullable=False)  # 完整备份、差异备份、日志备份
    size = db.Column(db.Integer, nullable=True)  # 文件大小（字节）
    description = db.Column(db.String(500), nullable=True)
    status = db.Column(db.String(20), nullable=False, default='成功')  # 成功、失败
    created_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    def __repr__(self):
        return f'<DatabaseBackup {self.filename}>'

    def to_dict(self):
        """转换为字典"""
        from app.utils.datetime_helper import format_datetime
        return {
            'id': self.id,
            'filename': self.filename,
            'backup_type': self.backup_type,
            'size': self.size,
            'description': self.description,
            'status': self.status,
            'created_at': format_datetime(self.created_at),
            'created_by': self.created_by
        }

class SystemLog(db.Model):
    """系统日志表"""
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    level = db.Column(db.String(20), nullable=False)  # INFO, WARNING, ERROR, CRITICAL
    module = db.Column(db.String(50), nullable=True)
    message = db.Column(db.Text, nullable=False)
    details = db.Column(db.Text, nullable=True)
    created_at = db.Column(DATETIME2(1), default=lambda: datetime.now().replace(microsecond=0), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    ip_address = db.Column(db.String(50), nullable=True)

    def __repr__(self):
        return f'<SystemLog {self.id}>'

    @staticmethod
    def log(level, message, module=None, details=None, user_id=None, ip_address=None):
        """记录日志"""
        log = SystemLog(
            level=level,
            module=module,
            message=message,
            details=details,
            user_id=user_id,
            ip_address=ip_address
        )
        db.session.add(log)
        db.session.commit()
        return log
