-- 检查数据库中的表结构状态
USE [StudentsCMSSP]
GO

PRINT '========================================'
PRINT '数据库表结构状态检查'
PRINT '========================================'
PRINT ''

-- 1. 检查核心周菜单表
PRINT '1. 核心周菜单表状态：'
PRINT '-----------------------------------------'

-- 检查 weekly_menus 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
BEGIN
    PRINT '✓ weekly_menus 表存在'
    DECLARE @weekly_count INT
    SELECT @weekly_count = COUNT(*) FROM weekly_menus
    PRINT '  记录数量: ' + CAST(@weekly_count AS NVARCHAR(10))

    -- 检查关键字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'area_id')
        PRINT '  ✓ area_id 字段存在'
    ELSE
        PRINT '  ❌ area_id 字段缺失'

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'status')
        PRINT '  ✓ status 字段存在'
    ELSE
        PRINT '  ❌ status 字段缺失'
END
ELSE
BEGIN
    PRINT '❌ weekly_menus 表不存在'
END

-- 检查 weekly_menu_recipes 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menu_recipes')
BEGIN
    PRINT '✓ weekly_menu_recipes 表存在'
    DECLARE @recipes_count INT
    SELECT @recipes_count = COUNT(*) FROM weekly_menu_recipes
    PRINT '  记录数量: ' + CAST(@recipes_count AS NVARCHAR(10))
END
ELSE
BEGIN
    PRINT '❌ weekly_menu_recipes 表不存在'
END

-- 2. 检查相关表
PRINT ''
PRINT '2. 相关表状态：'
PRINT '-----------------------------------------'

-- 检查 consumption_plans 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    PRINT '✓ consumption_plans 表存在'

    -- 检查关键字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
        PRINT '  ✓ area_id 字段存在'
    ELSE
        PRINT '  ⚠️ area_id 字段缺失（需要添加）'

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
        PRINT '  ⚠️ menu_plan_id 字段存在（旧字段，可能需要处理）'
    ELSE
        PRINT '  ✓ 没有旧的 menu_plan_id 字段'
END
ELSE
BEGIN
    PRINT '❌ consumption_plans 表不存在'
END

-- 3. 检查可能存在的旧表
PRINT ''
PRINT '3. 旧表状态（这些表可以保留）：'
PRINT '-----------------------------------------'

DECLARE @old_tables TABLE (table_name NVARCHAR(50), description NVARCHAR(100))
INSERT INTO @old_tables VALUES
    ('menu_plans', '旧菜单计划表'),
    ('weekly_menu_templates', '周菜单模板表'),
    ('daily_menus', '日菜单表'),
    ('weekly_menu_recipes_temp', '临时菜谱表')

DECLARE @table_name NVARCHAR(50), @description NVARCHAR(100)
DECLARE old_table_cursor CURSOR FOR SELECT table_name, description FROM @old_tables

OPEN old_table_cursor
FETCH NEXT FROM old_table_cursor INTO @table_name, @description

WHILE @@FETCH_STATUS = 0
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = @table_name)
    BEGIN
        PRINT '📋 ' + @table_name + ' 表存在 (' + @description + ')'
        DECLARE @count_sql NVARCHAR(200) = 'SELECT @count = COUNT(*) FROM ' + @table_name
        DECLARE @count INT
        EXEC sp_executesql @count_sql, N'@count INT OUTPUT', @count OUTPUT
        PRINT '  记录数量: ' + CAST(@count AS NVARCHAR(10))
    END
    ELSE
    BEGIN
        PRINT '✓ ' + @table_name + ' 表不存在'
    END

    FETCH NEXT FROM old_table_cursor INTO @table_name, @description
END

CLOSE old_table_cursor
DEALLOCATE old_table_cursor

-- 4. 检查索引状态
PRINT ''
PRINT '4. 关键索引状态：'
PRINT '-----------------------------------------'

-- 检查 weekly_menus 表的索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_optimized')
    PRINT '✓ weekly_menus 复合优化索引存在'
ELSE
    PRINT '⚠️ weekly_menus 复合优化索引缺失'

-- 检查 weekly_menu_recipes 表的索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_menu_day_meal')
    PRINT '✓ weekly_menu_recipes 复合优化索引存在'
ELSE
    PRINT '⚠️ weekly_menu_recipes 复合优化索引缺失'

PRINT ''
PRINT '========================================'
PRINT '检查完成！'
PRINT ''
PRINT '建议操作：'
PRINT '• 如果发现缺失的表或字段，运行 ensure_weekly_menu_structure.sql'
PRINT '• 如果发现缺失的索引，运行 ensure_weekly_menu_structure.sql'
PRINT '• 旧表可以保留，不会影响新系统运行'
PRINT '========================================'

-- 检查 consumption_plans 表结构
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    PRINT '✓ consumption_plans 表存在'
    
    -- 检查是否有 menu_plan_id 字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
    BEGIN
        PRINT '  - 有 menu_plan_id 字段'
    END
    ELSE
    BEGIN
        PRINT '  - 没有 menu_plan_id 字段'
    END
    
    -- 检查是否有 area_id 字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
    BEGIN
        PRINT '  - 有 area_id 字段'
    END
    ELSE
    BEGIN
        PRINT '  - 没有 area_id 字段'
    END
    
    SELECT COUNT(*) as consumption_plans_count FROM consumption_plans
END
ELSE
BEGIN
    PRINT '❌ consumption_plans 表不存在'
END

PRINT ''
PRINT '表检查完成！'

GO
