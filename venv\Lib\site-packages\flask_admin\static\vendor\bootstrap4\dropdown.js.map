{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                     = 'dropdown'\nconst VERSION                  = '4.3.1'\nconst DATA_KEY                 = 'bs.dropdown'\nconst EVENT_KEY                = `.${DATA_KEY}`\nconst DATA_API_KEY             = '.data-api'\nconst JQUERY_NO_CONFLICT       = $.fn[NAME]\nconst ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst Event = {\n  HIDE             : `hide${EVENT_KEY}`,\n  HIDDEN           : `hidden${EVENT_KEY}`,\n  SHOW             : `show${EVENT_KEY}`,\n  SHOWN            : `shown${EVENT_KEY}`,\n  CLICK            : `click${EVENT_KEY}`,\n  CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n  KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n  KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DISABLED        : 'disabled',\n  SHOW            : 'show',\n  DROPUP          : 'dropup',\n  DROPRIGHT       : 'dropright',\n  DROPLEFT        : 'dropleft',\n  MENURIGHT       : 'dropdown-menu-right',\n  MENULEFT        : 'dropdown-menu-left',\n  POSITION_STATIC : 'position-static'\n}\n\nconst Selector = {\n  DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n  FORM_CHILD    : '.dropdown form',\n  MENU          : '.dropdown-menu',\n  NAVBAR_NAV    : '.navbar-nav',\n  VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n}\n\nconst AttachmentMap = {\n  TOP       : 'top-start',\n  TOPEND    : 'top-end',\n  BOTTOM    : 'bottom-start',\n  BOTTOMEND : 'bottom-end',\n  RIGHT     : 'right-start',\n  RIGHTEND  : 'right-end',\n  LEFT      : 'left-start',\n  LEFTEND   : 'left-end'\n}\n\nconst Default = {\n  offset    : 0,\n  flip      : true,\n  boundary  : 'scrollParent',\n  reference : 'toggle',\n  display   : 'dynamic'\n}\n\nconst DefaultType = {\n  offset    : '(number|string|function)',\n  flip      : 'boolean',\n  boundary  : '(string|element)',\n  reference : '(string|element)',\n  display   : 'string'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element  = element\n    this._popper   = null\n    this._config   = this._getConfig(config)\n    this._menu     = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const parent   = Dropdown._getParentFromElement(this._element)\n    const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(Event.SHOW, relatedTarget)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org/)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(ClassName.POSITION_STATIC)\n      }\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(Selector.NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.SHOWN, relatedTarget))\n  }\n\n  show() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || $(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(Event.SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || !$(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(Event.HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    $(this._element).on(Event.CLICK, (event) => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(Selector.MENU)\n      }\n    }\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = AttachmentMap.BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n      placement = AttachmentMap.TOP\n      if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.TOPEND\n      }\n    } else if ($parentDropdown.hasClass(ClassName.DROPRIGHT)) {\n      placement = AttachmentMap.RIGHT\n    } else if ($parentDropdown.hasClass(ClassName.DROPLEFT)) {\n      placement = AttachmentMap.LEFT\n    } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n      placement = AttachmentMap.BOTTOMEND\n    }\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = (data) => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return popperConfig\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(ClassName.SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(Event.HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      $(dropdownMenu).removeClass(ClassName.SHOW)\n      $(parent)\n        .removeClass(ClassName.SHOW)\n        .trigger($.Event(Event.HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName)\n      ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(Selector.MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const parent   = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(ClassName.SHOW)\n\n    if (!isActive || isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        const toggle = parent.querySelector(Selector.DATA_TOGGLE)\n        $(toggle).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(Selector.VISIBLE_ITEMS))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n    e.stopPropagation()\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\n\nexport default Dropdown\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "RegExp", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "CLICK", "CLICK_DATA_API", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "ClassName", "DISABLED", "DROPUP", "DROPRIGHT", "DROPLEFT", "MENURIGHT", "MENULEFT", "POSITION_STATIC", "Selector", "DATA_TOGGLE", "FORM_CHILD", "MENU", "NAVBAR_NAV", "VISIBLE_ITEMS", "AttachmentMap", "TOP", "TOPEND", "BOTTOM", "BOTTOMEND", "RIGHT", "RIGHTEND", "LEFT", "LEFTEND", "<PERSON><PERSON><PERSON>", "offset", "flip", "boundary", "reference", "display", "DefaultType", "Dropdown", "element", "config", "_element", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "toggle", "disabled", "hasClass", "parent", "_getParentFromElement", "isActive", "_clearMenus", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "<PERSON><PERSON>", "TypeError", "referenceElement", "<PERSON><PERSON>", "isElement", "j<PERSON>y", "addClass", "_getPopperConfig", "document", "documentElement", "closest", "length", "body", "children", "on", "noop", "focus", "setAttribute", "toggleClass", "show", "hide", "hideEvent", "dispose", "removeData", "off", "destroy", "update", "scheduleUpdate", "event", "preventDefault", "stopPropagation", "constructor", "data", "typeCheckConfig", "querySelector", "_getPlacement", "$parentDropdown", "parentNode", "placement", "_getOffset", "offsets", "popperConfig", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "_jQueryInterface", "each", "which", "type", "toggles", "slice", "call", "querySelectorAll", "i", "len", "context", "clickEvent", "dropdownMenu", "test", "target", "tagName", "contains", "removeClass", "selector", "getSelectorFromElement", "_dataApiKeydownHandler", "items", "index", "indexOf", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAWA;;;;;;EAMA,IAAMA,IAAI,GAAuB,UAAjC;EACA,IAAMC,OAAO,GAAoB,OAAjC;EACA,IAAMC,QAAQ,GAAmB,aAAjC;EACA,IAAMC,SAAS,SAAsBD,QAArC;EACA,IAAME,YAAY,GAAe,WAAjC;EACA,IAAMC,kBAAkB,GAASC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAAjC;EACA,IAAMQ,cAAc,GAAa,EAAjC;;EACA,IAAMC,aAAa,GAAc,EAAjC;;EACA,IAAMC,WAAW,GAAgB,CAAjC;;EACA,IAAMC,gBAAgB,GAAW,EAAjC;;EACA,IAAMC,kBAAkB,GAAS,EAAjC;;EACA,IAAMC,wBAAwB,GAAG,CAAjC;;EACA,IAAMC,cAAc,GAAa,IAAIC,MAAJ,CAAcJ,gBAAd,SAAkCC,kBAAlC,SAAwDJ,cAAxD,CAAjC;EAEA,IAAMQ,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAsBd,SADd;EAEZe,EAAAA,MAAM,aAAsBf,SAFhB;EAGZgB,EAAAA,IAAI,WAAsBhB,SAHd;EAIZiB,EAAAA,KAAK,YAAsBjB,SAJf;EAKZkB,EAAAA,KAAK,YAAsBlB,SALf;EAMZmB,EAAAA,cAAc,YAAanB,SAAb,GAAyBC,YAN3B;EAOZmB,EAAAA,gBAAgB,cAAapB,SAAb,GAAyBC,YAP7B;EAQZoB,EAAAA,cAAc,YAAarB,SAAb,GAAyBC;EAR3B,CAAd;EAWA,IAAMqB,SAAS,GAAG;EAChBC,EAAAA,QAAQ,EAAU,UADF;EAEhBP,EAAAA,IAAI,EAAc,MAFF;EAGhBQ,EAAAA,MAAM,EAAY,QAHF;EAIhBC,EAAAA,SAAS,EAAS,WAJF;EAKhBC,EAAAA,QAAQ,EAAU,UALF;EAMhBC,EAAAA,SAAS,EAAS,qBANF;EAOhBC,EAAAA,QAAQ,EAAU,oBAPF;EAQhBC,EAAAA,eAAe,EAAG;EARF,CAAlB;EAWA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,WAAW,EAAK,0BADD;EAEfC,EAAAA,UAAU,EAAM,gBAFD;EAGfC,EAAAA,IAAI,EAAY,gBAHD;EAIfC,EAAAA,UAAU,EAAM,aAJD;EAKfC,EAAAA,aAAa,EAAG;EALD,CAAjB;EAQA,IAAMC,aAAa,GAAG;EACpBC,EAAAA,GAAG,EAAS,WADQ;EAEpBC,EAAAA,MAAM,EAAM,SAFQ;EAGpBC,EAAAA,MAAM,EAAM,cAHQ;EAIpBC,EAAAA,SAAS,EAAG,YAJQ;EAKpBC,EAAAA,KAAK,EAAO,aALQ;EAMpBC,EAAAA,QAAQ,EAAI,WANQ;EAOpBC,EAAAA,IAAI,EAAQ,YAPQ;EAQpBC,EAAAA,OAAO,EAAK;EARQ,CAAtB;EAWA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAM,CADE;EAEdC,EAAAA,IAAI,EAAQ,IAFE;EAGdC,EAAAA,QAAQ,EAAI,cAHE;EAIdC,EAAAA,SAAS,EAAG,QAJE;EAKdC,EAAAA,OAAO,EAAK;EALE,CAAhB;EAQA,IAAMC,WAAW,GAAG;EAClBL,EAAAA,MAAM,EAAM,0BADM;EAElBC,EAAAA,IAAI,EAAQ,SAFM;EAGlBC,EAAAA,QAAQ,EAAI,kBAHM;EAIlBC,EAAAA,SAAS,EAAG,kBAJM;EAKlBC,EAAAA,OAAO,EAAK;EAGd;;;;;;EARoB,CAApB;;MAcME;;;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAiBF,OAAjB;EACA,SAAKG,OAAL,GAAiB,IAAjB;EACA,SAAKC,OAAL,GAAiB,KAAKC,UAAL,CAAgBJ,MAAhB,CAAjB;EACA,SAAKK,KAAL,GAAiB,KAAKC,eAAL,EAAjB;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKC,kBAAL;EACD;;;;;EAgBD;WAEAC,SAAA,kBAAS;EACP,QAAI,KAAKT,QAAL,CAAcU,QAAd,IAA0B9D,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B5C,SAAS,CAACC,QAApC,CAA9B,EAA6E;EAC3E;EACD;;EAED,QAAM4C,MAAM,GAAKf,QAAQ,CAACgB,qBAAT,CAA+B,KAAKb,QAApC,CAAjB;;EACA,QAAMc,QAAQ,GAAGlE,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAcO,QAAd,CAAuB5C,SAAS,CAACN,IAAjC,CAAjB;;EAEAoC,IAAAA,QAAQ,CAACkB,WAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,QAAME,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAMiB,SAAS,GAAGrE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACG,IAAd,EAAoBuD,aAApB,CAAlB;EAEApE,IAAAA,CAAC,CAACgE,MAAD,CAAD,CAAUM,OAAV,CAAkBD,SAAlB;;EAEA,QAAIA,SAAS,CAACE,kBAAV,EAAJ,EAAoC;EAClC;EACD,KAvBM;;;EA0BP,QAAI,CAAC,KAAKb,SAAV,EAAqB;EACnB;;;;EAIA,UAAI,OAAOc,MAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAIC,SAAJ,CAAc,mEAAd,CAAN;EACD;;EAED,UAAIC,gBAAgB,GAAG,KAAKtB,QAA5B;;EAEA,UAAI,KAAKE,OAAL,CAAaR,SAAb,KAA2B,QAA/B,EAAyC;EACvC4B,QAAAA,gBAAgB,GAAGV,MAAnB;EACD,OAFD,MAEO,IAAIW,IAAI,CAACC,SAAL,CAAe,KAAKtB,OAAL,CAAaR,SAA5B,CAAJ,EAA4C;EACjD4B,QAAAA,gBAAgB,GAAG,KAAKpB,OAAL,CAAaR,SAAhC,CADiD;;EAIjD,YAAI,OAAO,KAAKQ,OAAL,CAAaR,SAAb,CAAuB+B,MAA9B,KAAyC,WAA7C,EAA0D;EACxDH,UAAAA,gBAAgB,GAAG,KAAKpB,OAAL,CAAaR,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OApBkB;EAuBnB;EACA;;;EACA,UAAI,KAAKQ,OAAL,CAAaT,QAAb,KAA0B,cAA9B,EAA8C;EAC5C7C,QAAAA,CAAC,CAACgE,MAAD,CAAD,CAAUc,QAAV,CAAmB3D,SAAS,CAACO,eAA7B;EACD;;EACD,WAAK2B,OAAL,GAAe,IAAImB,MAAJ,CAAWE,gBAAX,EAA6B,KAAKlB,KAAlC,EAAyC,KAAKuB,gBAAL,EAAzC,CAAf;EACD,KAvDM;EA0DP;EACA;EACA;;;EACA,QAAI,kBAAkBC,QAAQ,CAACC,eAA3B,IACAjF,CAAC,CAACgE,MAAD,CAAD,CAAUkB,OAAV,CAAkBvD,QAAQ,CAACI,UAA3B,EAAuCoD,MAAvC,KAAkD,CADtD,EACyD;EACvDnF,MAAAA,CAAC,CAACgF,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDtF,CAAC,CAACuF,IAApD;EACD;;EAED,SAAKnC,QAAL,CAAcoC,KAAd;;EACA,SAAKpC,QAAL,CAAcqC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEAzF,IAAAA,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAckC,WAAd,CAA0BvE,SAAS,CAACN,IAApC;EACAb,IAAAA,CAAC,CAACgE,MAAD,CAAD,CACG0B,WADH,CACevE,SAAS,CAACN,IADzB,EAEGyD,OAFH,CAEWtE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACI,KAAd,EAAqBsD,aAArB,CAFX;EAGD;;WAEDuB,OAAA,gBAAO;EACL,QAAI,KAAKvC,QAAL,CAAcU,QAAd,IAA0B9D,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B5C,SAAS,CAACC,QAApC,CAA1B,IAA2EpB,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAcO,QAAd,CAAuB5C,SAAS,CAACN,IAAjC,CAA/E,EAAuH;EACrH;EACD;;EAED,QAAMuD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAMiB,SAAS,GAAGrE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACG,IAAd,EAAoBuD,aAApB,CAAlB;;EACA,QAAMJ,MAAM,GAAGf,QAAQ,CAACgB,qBAAT,CAA+B,KAAKb,QAApC,CAAf;;EAEApD,IAAAA,CAAC,CAACgE,MAAD,CAAD,CAAUM,OAAV,CAAkBD,SAAlB;;EAEA,QAAIA,SAAS,CAACE,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAEDvE,IAAAA,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAckC,WAAd,CAA0BvE,SAAS,CAACN,IAApC;EACAb,IAAAA,CAAC,CAACgE,MAAD,CAAD,CACG0B,WADH,CACevE,SAAS,CAACN,IADzB,EAEGyD,OAFH,CAEWtE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACI,KAAd,EAAqBsD,aAArB,CAFX;EAGD;;WAEDwB,OAAA,gBAAO;EACL,QAAI,KAAKxC,QAAL,CAAcU,QAAd,IAA0B9D,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B5C,SAAS,CAACC,QAApC,CAA1B,IAA2E,CAACpB,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAcO,QAAd,CAAuB5C,SAAS,CAACN,IAAjC,CAAhF,EAAwH;EACtH;EACD;;EAED,QAAMuD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAMyC,SAAS,GAAG7F,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACC,IAAd,EAAoByD,aAApB,CAAlB;;EACA,QAAMJ,MAAM,GAAGf,QAAQ,CAACgB,qBAAT,CAA+B,KAAKb,QAApC,CAAf;;EAEApD,IAAAA,CAAC,CAACgE,MAAD,CAAD,CAAUM,OAAV,CAAkBuB,SAAlB;;EAEA,QAAIA,SAAS,CAACtB,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAEDvE,IAAAA,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAckC,WAAd,CAA0BvE,SAAS,CAACN,IAApC;EACAb,IAAAA,CAAC,CAACgE,MAAD,CAAD,CACG0B,WADH,CACevE,SAAS,CAACN,IADzB,EAEGyD,OAFH,CAEWtE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACE,MAAd,EAAsBwD,aAAtB,CAFX;EAGD;;WAED0B,UAAA,mBAAU;EACR9F,IAAAA,CAAC,CAAC+F,UAAF,CAAa,KAAK3C,QAAlB,EAA4BxD,QAA5B;EACAI,IAAAA,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiB4C,GAAjB,CAAqBnG,SAArB;EACA,SAAKuD,QAAL,GAAgB,IAAhB;EACA,SAAKI,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKH,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa4C,OAAb;;EACA,WAAK5C,OAAL,GAAe,IAAf;EACD;EACF;;WAED6C,SAAA,kBAAS;EACP,SAAKxC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKN,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa8C,cAAb;EACD;EACF;;;WAIDvC,qBAAA,8BAAqB;EAAA;;EACnB5D,IAAAA,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiBkC,EAAjB,CAAoB5E,KAAK,CAACK,KAA1B,EAAiC,UAACqF,KAAD,EAAW;EAC1CA,MAAAA,KAAK,CAACC,cAAN;EACAD,MAAAA,KAAK,CAACE,eAAN;;EACA,MAAA,KAAI,CAACzC,MAAL;EACD,KAJD;EAKD;;WAEDN,aAAA,oBAAWJ,MAAX,EAAmB;EACjBA,IAAAA,MAAM,qBACD,KAAKoD,WAAL,CAAiB7D,OADhB,EAED1C,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiBoD,IAAjB,EAFC,EAGDrD,MAHC,CAAN;EAMAwB,IAAAA,IAAI,CAAC8B,eAAL,CACE/G,IADF,EAEEyD,MAFF,EAGE,KAAKoD,WAAL,CAAiBvD,WAHnB;EAMA,WAAOG,MAAP;EACD;;WAEDM,kBAAA,2BAAkB;EAChB,QAAI,CAAC,KAAKD,KAAV,EAAiB;EACf,UAAMQ,MAAM,GAAGf,QAAQ,CAACgB,qBAAT,CAA+B,KAAKb,QAApC,CAAf;;EAEA,UAAIY,MAAJ,EAAY;EACV,aAAKR,KAAL,GAAaQ,MAAM,CAAC0C,aAAP,CAAqB/E,QAAQ,CAACG,IAA9B,CAAb;EACD;EACF;;EACD,WAAO,KAAK0B,KAAZ;EACD;;WAEDmD,gBAAA,yBAAgB;EACd,QAAMC,eAAe,GAAG5G,CAAC,CAAC,KAAKoD,QAAL,CAAcyD,UAAf,CAAzB;EACA,QAAIC,SAAS,GAAG7E,aAAa,CAACG,MAA9B,CAFc;;EAKd,QAAIwE,eAAe,CAAC7C,QAAhB,CAAyB5C,SAAS,CAACE,MAAnC,CAAJ,EAAgD;EAC9CyF,MAAAA,SAAS,GAAG7E,aAAa,CAACC,GAA1B;;EACA,UAAIlC,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAcO,QAAd,CAAuB5C,SAAS,CAACK,SAAjC,CAAJ,EAAiD;EAC/CsF,QAAAA,SAAS,GAAG7E,aAAa,CAACE,MAA1B;EACD;EACF,KALD,MAKO,IAAIyE,eAAe,CAAC7C,QAAhB,CAAyB5C,SAAS,CAACG,SAAnC,CAAJ,EAAmD;EACxDwF,MAAAA,SAAS,GAAG7E,aAAa,CAACK,KAA1B;EACD,KAFM,MAEA,IAAIsE,eAAe,CAAC7C,QAAhB,CAAyB5C,SAAS,CAACI,QAAnC,CAAJ,EAAkD;EACvDuF,MAAAA,SAAS,GAAG7E,aAAa,CAACO,IAA1B;EACD,KAFM,MAEA,IAAIxC,CAAC,CAAC,KAAKwD,KAAN,CAAD,CAAcO,QAAd,CAAuB5C,SAAS,CAACK,SAAjC,CAAJ,EAAiD;EACtDsF,MAAAA,SAAS,GAAG7E,aAAa,CAACI,SAA1B;EACD;;EACD,WAAOyE,SAAP;EACD;;WAEDnD,gBAAA,yBAAgB;EACd,WAAO3D,CAAC,CAAC,KAAKoD,QAAN,CAAD,CAAiB8B,OAAjB,CAAyB,SAAzB,EAAoCC,MAApC,GAA6C,CAApD;EACD;;WAED4B,aAAA,sBAAa;EAAA;;EACX,QAAMpE,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKW,OAAL,CAAaX,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAAC1C,EAAP,GAAY,UAACuG,IAAD,EAAU;EACpBA,QAAAA,IAAI,CAACQ,OAAL,qBACKR,IAAI,CAACQ,OADV,EAEK,MAAI,CAAC1D,OAAL,CAAaX,MAAb,CAAoB6D,IAAI,CAACQ,OAAzB,EAAkC,MAAI,CAAC5D,QAAvC,KAAoD,EAFzD;EAKA,eAAOoD,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL7D,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKW,OAAL,CAAaX,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDoC,mBAAA,4BAAmB;EACjB,QAAMkC,YAAY,GAAG;EACnBH,MAAAA,SAAS,EAAE,KAAKH,aAAL,EADQ;EAEnBO,MAAAA,SAAS,EAAE;EACTvE,QAAAA,MAAM,EAAE,KAAKoE,UAAL,EADC;EAETnE,QAAAA,IAAI,EAAE;EACJuE,UAAAA,OAAO,EAAE,KAAK7D,OAAL,CAAaV;EADlB,SAFG;EAKTwE,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAK/D,OAAL,CAAaT;EADjB;EALR,OAFQ;;EAAA,KAArB;;EAcA,QAAI,KAAKS,OAAL,CAAaP,OAAb,KAAyB,QAA7B,EAAuC;EACrCkE,MAAAA,YAAY,CAACC,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,WAAOF,YAAP;EACD;;;aAIMM,mBAAP,0BAAwBpE,MAAxB,EAAgC;EAC9B,WAAO,KAAKqE,IAAL,CAAU,YAAY;EAC3B,UAAIhB,IAAI,GAAGxG,CAAC,CAAC,IAAD,CAAD,CAAQwG,IAAR,CAAa5G,QAAb,CAAX;;EACA,UAAM0D,OAAO,GAAG,OAAOH,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACqD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIvD,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP;EACAtD,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQwG,IAAR,CAAa5G,QAAb,EAAuB4G,IAAvB;EACD;;EAED,UAAI,OAAOrD,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOqD,IAAI,CAACrD,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIsB,SAAJ,wBAAkCtB,MAAlC,QAAN;EACD;;EACDqD,QAAAA,IAAI,CAACrD,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;aAEMgB,cAAP,qBAAmBiC,KAAnB,EAA0B;EACxB,QAAIA,KAAK,KAAKA,KAAK,CAACqB,KAAN,KAAgBlH,wBAAhB,IACZ6F,KAAK,CAACsB,IAAN,KAAe,OAAf,IAA0BtB,KAAK,CAACqB,KAAN,KAAgBrH,WADnC,CAAT,EAC0D;EACxD;EACD;;EAED,QAAMuH,OAAO,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAc7C,QAAQ,CAAC8C,gBAAT,CAA0BnG,QAAQ,CAACC,WAAnC,CAAd,CAAhB;;EAEA,SAAK,IAAImG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGL,OAAO,CAACxC,MAA9B,EAAsC4C,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,UAAM/D,MAAM,GAAGf,QAAQ,CAACgB,qBAAT,CAA+B0D,OAAO,CAACI,CAAD,CAAtC,CAAf;;EACA,UAAME,OAAO,GAAGjI,CAAC,CAAC2H,OAAO,CAACI,CAAD,CAAR,CAAD,CAAcvB,IAAd,CAAmB5G,QAAnB,CAAhB;EACA,UAAMwE,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEuD,OAAO,CAACI,CAAD;EADF,OAAtB;;EAIA,UAAI3B,KAAK,IAAIA,KAAK,CAACsB,IAAN,KAAe,OAA5B,EAAqC;EACnCtD,QAAAA,aAAa,CAAC8D,UAAd,GAA2B9B,KAA3B;EACD;;EAED,UAAI,CAAC6B,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAACzE,KAA7B;;EACA,UAAI,CAACxD,CAAC,CAACgE,MAAD,CAAD,CAAUD,QAAV,CAAmB5C,SAAS,CAACN,IAA7B,CAAL,EAAyC;EACvC;EACD;;EAED,UAAIuF,KAAK,KAAKA,KAAK,CAACsB,IAAN,KAAe,OAAf,IACV,kBAAkBU,IAAlB,CAAuBhC,KAAK,CAACiC,MAAN,CAAaC,OAApC,CADU,IACsClC,KAAK,CAACsB,IAAN,KAAe,OAAf,IAA0BtB,KAAK,CAACqB,KAAN,KAAgBrH,WADrF,CAAL,IAEAJ,CAAC,CAACuI,QAAF,CAAWvE,MAAX,EAAmBoC,KAAK,CAACiC,MAAzB,CAFJ,EAEsC;EACpC;EACD;;EAED,UAAMxC,SAAS,GAAG7F,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACC,IAAd,EAAoByD,aAApB,CAAlB;EACApE,MAAAA,CAAC,CAACgE,MAAD,CAAD,CAAUM,OAAV,CAAkBuB,SAAlB;;EACA,UAAIA,SAAS,CAACtB,kBAAV,EAAJ,EAAoC;EAClC;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkBS,QAAQ,CAACC,eAA/B,EAAgD;EAC9CjF,QAAAA,CAAC,CAACgF,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BW,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmDhG,CAAC,CAACuF,IAArD;EACD;;EAEDoC,MAAAA,OAAO,CAACI,CAAD,CAAP,CAAWtC,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;EAEAzF,MAAAA,CAAC,CAACmI,YAAD,CAAD,CAAgBK,WAAhB,CAA4BrH,SAAS,CAACN,IAAtC;EACAb,MAAAA,CAAC,CAACgE,MAAD,CAAD,CACGwE,WADH,CACerH,SAAS,CAACN,IADzB,EAEGyD,OAFH,CAEWtE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACE,MAAd,EAAsBwD,aAAtB,CAFX;EAGD;EACF;;aAEMH,wBAAP,+BAA6Bf,OAA7B,EAAsC;EACpC,QAAIc,MAAJ;EACA,QAAMyE,QAAQ,GAAG9D,IAAI,CAAC+D,sBAAL,CAA4BxF,OAA5B,CAAjB;;EAEA,QAAIuF,QAAJ,EAAc;EACZzE,MAAAA,MAAM,GAAGgB,QAAQ,CAAC0B,aAAT,CAAuB+B,QAAvB,CAAT;EACD;;EAED,WAAOzE,MAAM,IAAId,OAAO,CAAC2D,UAAzB;EACD;;;aAGM8B,yBAAP,gCAA8BvC,KAA9B,EAAqC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBgC,IAAlB,CAAuBhC,KAAK,CAACiC,MAAN,CAAaC,OAApC,IACAlC,KAAK,CAACqB,KAAN,KAAgBtH,aAAhB,IAAiCiG,KAAK,CAACqB,KAAN,KAAgBvH,cAAhB,KAClCkG,KAAK,CAACqB,KAAN,KAAgBnH,kBAAhB,IAAsC8F,KAAK,CAACqB,KAAN,KAAgBpH,gBAAtD,IACCL,CAAC,CAACoG,KAAK,CAACiC,MAAP,CAAD,CAAgBnD,OAAhB,CAAwBvD,QAAQ,CAACG,IAAjC,EAAuCqD,MAFN,CADjC,GAGiD,CAAC3E,cAAc,CAAC4H,IAAf,CAAoBhC,KAAK,CAACqB,KAA1B,CAHtD,EAGwF;EACtF;EACD;;EAEDrB,IAAAA,KAAK,CAACC,cAAN;EACAD,IAAAA,KAAK,CAACE,eAAN;;EAEA,QAAI,KAAKxC,QAAL,IAAiB9D,CAAC,CAAC,IAAD,CAAD,CAAQ+D,QAAR,CAAiB5C,SAAS,CAACC,QAA3B,CAArB,EAA2D;EACzD;EACD;;EAED,QAAM4C,MAAM,GAAKf,QAAQ,CAACgB,qBAAT,CAA+B,IAA/B,CAAjB;;EACA,QAAMC,QAAQ,GAAGlE,CAAC,CAACgE,MAAD,CAAD,CAAUD,QAAV,CAAmB5C,SAAS,CAACN,IAA7B,CAAjB;;EAEA,QAAI,CAACqD,QAAD,IAAaA,QAAQ,KAAKkC,KAAK,CAACqB,KAAN,KAAgBvH,cAAhB,IAAkCkG,KAAK,CAACqB,KAAN,KAAgBtH,aAAvD,CAAzB,EAAgG;EAC9F,UAAIiG,KAAK,CAACqB,KAAN,KAAgBvH,cAApB,EAAoC;EAClC,YAAM2D,MAAM,GAAGG,MAAM,CAAC0C,aAAP,CAAqB/E,QAAQ,CAACC,WAA9B,CAAf;EACA5B,QAAAA,CAAC,CAAC6D,MAAD,CAAD,CAAUS,OAAV,CAAkB,OAAlB;EACD;;EAEDtE,MAAAA,CAAC,CAAC,IAAD,CAAD,CAAQsE,OAAR,CAAgB,OAAhB;EACA;EACD;;EAED,QAAMsE,KAAK,GAAG,GAAGhB,KAAH,CAASC,IAAT,CAAc7D,MAAM,CAAC8D,gBAAP,CAAwBnG,QAAQ,CAACK,aAAjC,CAAd,CAAd;;EAEA,QAAI4G,KAAK,CAACzD,MAAN,KAAiB,CAArB,EAAwB;EACtB;EACD;;EAED,QAAI0D,KAAK,GAAGD,KAAK,CAACE,OAAN,CAAc1C,KAAK,CAACiC,MAApB,CAAZ;;EAEA,QAAIjC,KAAK,CAACqB,KAAN,KAAgBpH,gBAAhB,IAAoCwI,KAAK,GAAG,CAAhD,EAAmD;EAAE;EACnDA,MAAAA,KAAK;EACN;;EAED,QAAIzC,KAAK,CAACqB,KAAN,KAAgBnH,kBAAhB,IAAsCuI,KAAK,GAAGD,KAAK,CAACzD,MAAN,GAAe,CAAjE,EAAoE;EAAE;EACpE0D,MAAAA,KAAK;EACN;;EAED,QAAIA,KAAK,GAAG,CAAZ,EAAe;EACbA,MAAAA,KAAK,GAAG,CAAR;EACD;;EAEDD,IAAAA,KAAK,CAACC,KAAD,CAAL,CAAarD,KAAb;EACD;;;;0BAjZoB;EACnB,aAAO7F,OAAP;EACD;;;0BAEoB;EACnB,aAAO+C,OAAP;EACD;;;0BAEwB;EACvB,aAAOM,WAAP;EACD;;;;;EA0YH;;;;;;;EAMAhD,CAAC,CAACgF,QAAD,CAAD,CACGM,EADH,CACM5E,KAAK,CAACO,gBADZ,EAC8BU,QAAQ,CAACC,WADvC,EACoDqB,QAAQ,CAAC0F,sBAD7D,EAEGrD,EAFH,CAEM5E,KAAK,CAACO,gBAFZ,EAE8BU,QAAQ,CAACG,IAFvC,EAE6CmB,QAAQ,CAAC0F,sBAFtD,EAGGrD,EAHH,CAGS5E,KAAK,CAACM,cAHf,SAGiCN,KAAK,CAACQ,cAHvC,EAGyD+B,QAAQ,CAACkB,WAHlE,EAIGmB,EAJH,CAIM5E,KAAK,CAACM,cAJZ,EAI4BW,QAAQ,CAACC,WAJrC,EAIkD,UAAUwE,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN;EACAD,EAAAA,KAAK,CAACE,eAAN;;EACArD,EAAAA,QAAQ,CAACsE,gBAAT,CAA0BM,IAA1B,CAA+B7H,CAAC,CAAC,IAAD,CAAhC,EAAwC,QAAxC;EACD,CARH,EASGsF,EATH,CASM5E,KAAK,CAACM,cATZ,EAS4BW,QAAQ,CAACE,UATrC,EASiD,UAACkH,CAAD,EAAO;EACpDA,EAAAA,CAAC,CAACzC,eAAF;EACD,CAXH;EAaA;;;;;;EAMAtG,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAauD,QAAQ,CAACsE,gBAAtB;EACAvH,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWsJ,WAAX,GAAyB/F,QAAzB;;EACAjD,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWuJ,UAAX,GAAwB,YAAM;EAC5BjJ,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOkD,QAAQ,CAACsE,gBAAhB;EACD,CAHD;;;;;;;;"}