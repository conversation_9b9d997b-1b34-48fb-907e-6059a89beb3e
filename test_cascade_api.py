#!/usr/bin/env python3
"""
测试分级联动选择API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import AdministrativeArea, User
from app.models_financial import AccountingSubject
from sqlalchemy import text

def test_cascade_api():
    """测试分级联动选择API"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试分级联动选择API ===\n")
        
        # 1. 测试科目类型API
        print("1. 测试科目类型...")
        types = db.session.query(AccountingSubject.subject_type).filter(
            AccountingSubject.is_active == True
        ).distinct().all()
        
        type_order = ['资产', '负债', '净资产', '收入', '费用']
        available_types = [t[0] for t in types]
        ordered_types = [t for t in type_order if t in available_types]
        
        print(f"可用科目类型: {ordered_types}")
        
        # 2. 测试一级科目API
        print("\n2. 测试一级科目...")
        for subject_type in ordered_types[:2]:  # 只测试前两个类型
            subjects = AccountingSubject.query.filter(
                AccountingSubject.subject_type == subject_type,
                AccountingSubject.level == 1,
                AccountingSubject.is_active == True
            ).order_by(AccountingSubject.code).limit(3).all()
            
            print(f"  {subject_type}类一级科目:")
            for subject in subjects:
                print(f"    {subject.code} - {subject.name}")
        
        # 3. 测试下级科目API
        print("\n3. 测试下级科目...")
        parent_subjects = AccountingSubject.query.filter(
            AccountingSubject.level == 1,
            AccountingSubject.is_active == True
        ).limit(3).all()
        
        for parent in parent_subjects:
            children = AccountingSubject.query.filter(
                AccountingSubject.parent_id == parent.id,
                AccountingSubject.is_active == True
            ).order_by(AccountingSubject.code).all()
            
            if children:
                print(f"  {parent.code} - {parent.name} 的下级科目:")
                for child in children:
                    print(f"    {child.code} - {child.name}")
            else:
                print(f"  {parent.code} - {parent.name} 没有下级科目")
        
        # 4. 测试凭证号生成
        print("\n4. 测试凭证号生成...")
        from app.routes.financial.vouchers import get_school_pinyin_initials, generate_voucher_number
        
        # 获取一个测试区域
        test_area = AdministrativeArea.query.first()
        if test_area:
            initials = get_school_pinyin_initials(test_area.name)
            voucher_number = generate_voucher_number(test_area)
            print(f"学校名称: {test_area.name}")
            print(f"拼音首字母: {initials}")
            print(f"生成的凭证号: {voucher_number}")
        else:
            print("没有找到测试区域")
        
        print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_cascade_api()
