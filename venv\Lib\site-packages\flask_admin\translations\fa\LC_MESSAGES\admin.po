msgid ""
msgstr ""
"Project-Id-Version: flask-admin\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-02-07 00:17-0600\n"
"PO-Revision-Date: 2017-02-07 01:19-0500\n"
"Last-Translator: mr<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Persian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.1.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: crowdin.com\n"
"X-Crowdin-Project: flask-admin\n"
"X-Crowdin-Language: fa\n"
"X-Crowdin-File: admin.pot\n"
"Language: fa_IR\n"

#: ../flask_admin/base.py:440
msgid "Home"
msgstr "خانه"

#: ../flask_admin/contrib/rediscli.py:127
msgid "Cli: Invalid command."
msgstr "Cli: دستور نامعتبر است."

#: ../flask_admin/contrib/fileadmin/__init__.py:352
msgid "File to upload"
msgstr "فایلی که باید آپلود شود"

#: ../flask_admin/contrib/fileadmin/__init__.py:360
msgid "File required."
msgstr "فایل لازم است"

#: ../flask_admin/contrib/fileadmin/__init__.py:365
msgid "Invalid file type."
msgstr "نوع فایل غیرمجاز است"

#: ../flask_admin/contrib/fileadmin/__init__.py:376
msgid "Content"
msgstr "محتوا"

#: ../flask_admin/contrib/fileadmin/__init__.py:390
msgid "Invalid name"
msgstr "نام اشتباه"

#: ../flask_admin/contrib/fileadmin/__init__.py:398
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:106
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:112
#: ../flask_admin/tests/sqla/test_translation.py:17
msgid "Name"
msgstr "نام"

#: ../flask_admin/contrib/fileadmin/__init__.py:757
#, python-format
msgid "File \"%(name)s\" already exists."
msgstr "پرونده \"%(name)s\" وجود دارد."

#: ../flask_admin/contrib/fileadmin/__init__.py:802
#: ../flask_admin/contrib/fileadmin/__init__.py:885
#: ../flask_admin/contrib/fileadmin/__init__.py:947
#: ../flask_admin/contrib/fileadmin/__init__.py:1000
#: ../flask_admin/contrib/fileadmin/__init__.py:1047
#: ../flask_admin/contrib/fileadmin/__init__.py:1099
#: ../flask_admin/model/base.py:2168
msgid "Permission denied."
msgstr "خطای دسترسی"

#: ../flask_admin/contrib/fileadmin/__init__.py:881
msgid "File uploading is disabled."
msgstr "آپلود فایل غیر فعال است"

#: ../flask_admin/contrib/fileadmin/__init__.py:892
#, python-format
msgid "Successfully saved file: %(name)s"
msgstr "فایل با موفقیت ذخیره شده: %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:896
#, python-format
msgid "Failed to save file: %(error)s"
msgstr "ذخیره ی فایل  بامشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:904
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:148
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:150
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:148
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:150
msgid "Upload File"
msgstr "آپلود فایل"

#: ../flask_admin/contrib/fileadmin/__init__.py:943
msgid "Directory creation is disabled."
msgstr "ساخت پوشه غیر فعال است"

#: ../flask_admin/contrib/fileadmin/__init__.py:956
#, python-format
msgid "Successfully created directory: %(directory)s"
msgstr "پوشه با موفقیت ایجاد شده: %(directory)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:960
#, python-format
msgid "Failed to create directory: %(error)s"
msgstr "ساخت پوشه ی  با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:970
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:159
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:161
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:159
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:161
msgid "Create Directory"
msgstr "ایجاد پوشه"

#: ../flask_admin/contrib/fileadmin/__init__.py:996
msgid "Deletion is disabled."
msgstr "حذف غیر فعال شده است."

#: ../flask_admin/contrib/fileadmin/__init__.py:1005
msgid "Directory deletion is disabled."
msgstr "حذف دایرکتوری غیر فعال شده است."

#: ../flask_admin/contrib/fileadmin/__init__.py:1011
#, python-format
msgid "Directory \"%(path)s\" was successfully deleted."
msgstr "پوشه %(path)s با موفقیت حذف شد."

#: ../flask_admin/contrib/fileadmin/__init__.py:1013
#, python-format
msgid "Failed to delete directory: %(error)s"
msgstr "حذف پوشه ی  با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1019
#: ../flask_admin/contrib/fileadmin/__init__.py:1176
#, python-format
msgid "File \"%(name)s\" was successfully deleted."
msgstr "فایل \"%(name)s\" با موفقیت حذف شد"

#: ../flask_admin/contrib/fileadmin/__init__.py:1021
#: ../flask_admin/contrib/fileadmin/__init__.py:1178
#, python-format
msgid "Failed to delete file: %(name)s"
msgstr "حذف فایل%(name)s  با مشکل روبرو شد"

#: ../flask_admin/contrib/fileadmin/__init__.py:1043
msgid "Renaming is disabled."
msgstr "تغییر نام غیر فعال است"

#: ../flask_admin/contrib/fileadmin/__init__.py:1051
msgid "Path does not exist."
msgstr "مسیر وجود ندارد"

#: ../flask_admin/contrib/fileadmin/__init__.py:1061
#, python-format
msgid "Successfully renamed \"%(src)s\" to \"%(dst)s\""
msgstr "با موفقیت از\"%(src)s\"  به \"%(dst)s\" تغییر نام داده شد"

#: ../flask_admin/contrib/fileadmin/__init__.py:1064
#, python-format
msgid "Failed to rename: %(error)s"
msgstr "تغییر نام  با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1078
#, python-format
msgid "Rename %(name)s"
msgstr "تغییر نام %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1115
#, python-format
msgid "Error saving changes to %(name)s."
msgstr "مشکل در ذخیره ی تغییرات %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1119
#, python-format
msgid "Changes to %(name)s saved successfully."
msgstr "تغییرات%(name)s  با موفقیت صورت گرفت"

#: ../flask_admin/contrib/fileadmin/__init__.py:1128
#, python-format
msgid "Error reading %(name)s."
msgstr "مشکل در خواندن %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1131
#: ../flask_admin/contrib/fileadmin/__init__.py:1140
#, python-format
msgid "Unexpected error while reading from %(name)s"
msgstr "خطای غیر منتظره در حال خواندن %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1137
#, python-format
msgid "Cannot edit %(name)s."
msgstr "عدم توانایی در ویرایش %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1155
#, python-format
msgid "Editing %(path)s"
msgstr "ویرایش %(path)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1163
#: ../flask_admin/contrib/mongoengine/view.py:658
#: ../flask_admin/contrib/peewee/view.py:487
#: ../flask_admin/contrib/pymongo/view.py:384
#: ../flask_admin/contrib/sqla/view.py:1149
msgid "Delete"
msgstr "حذف"

#: ../flask_admin/contrib/fileadmin/__init__.py:1164
msgid "Are you sure you want to delete these files?"
msgstr "آیا از حذف این فایل ها اطمینان دارید؟"

#: ../flask_admin/contrib/fileadmin/__init__.py:1167
msgid "File deletion is disabled."
msgstr "امکان حذف فایل وجود ندارد"

#: ../flask_admin/contrib/fileadmin/__init__.py:1180
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:17
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:22
msgid "Edit"
msgstr "ویرایش"

#: ../flask_admin/contrib/fileadmin/s3.py:153
msgid "Cannot operate on non empty directories"
msgstr ""

#: ../flask_admin/contrib/mongoengine/filters.py:39
#: ../flask_admin/contrib/peewee/filters.py:35
#: ../flask_admin/contrib/pymongo/filters.py:38
#: ../flask_admin/contrib/sqla/filters.py:41
msgid "equals"
msgstr "برابر با"

#: ../flask_admin/contrib/mongoengine/filters.py:48
#: ../flask_admin/contrib/peewee/filters.py:43
#: ../flask_admin/contrib/pymongo/filters.py:47
#: ../flask_admin/contrib/sqla/filters.py:49
msgid "not equal"
msgstr "برابر نیست با"

#: ../flask_admin/contrib/mongoengine/filters.py:58
#: ../flask_admin/contrib/peewee/filters.py:52
#: ../flask_admin/contrib/pymongo/filters.py:57
#: ../flask_admin/contrib/sqla/filters.py:58
msgid "contains"
msgstr "محتوی"

#: ../flask_admin/contrib/mongoengine/filters.py:68
#: ../flask_admin/contrib/peewee/filters.py:61
#: ../flask_admin/contrib/pymongo/filters.py:67
#: ../flask_admin/contrib/sqla/filters.py:67
msgid "not contains"
msgstr "محتوی نیست"

#: ../flask_admin/contrib/mongoengine/filters.py:77
#: ../flask_admin/contrib/peewee/filters.py:69
#: ../flask_admin/contrib/pymongo/filters.py:80
#: ../flask_admin/contrib/sqla/filters.py:75
msgid "greater than"
msgstr "بزرگتر از"

#: ../flask_admin/contrib/mongoengine/filters.py:86
#: ../flask_admin/contrib/peewee/filters.py:77
#: ../flask_admin/contrib/pymongo/filters.py:93
#: ../flask_admin/contrib/sqla/filters.py:83
msgid "smaller than"
msgstr "کوچکتر از"

#: ../flask_admin/contrib/mongoengine/filters.py:98
#: ../flask_admin/contrib/peewee/filters.py:88
#: ../flask_admin/contrib/sqla/filters.py:94
msgid "empty"
msgstr "خالی باشد"

#: ../flask_admin/contrib/mongoengine/filters.py:113
#: ../flask_admin/contrib/peewee/filters.py:102
#: ../flask_admin/contrib/sqla/filters.py:108
msgid "in list"
msgstr "در این لیست باشد"

#: ../flask_admin/contrib/mongoengine/filters.py:122
#: ../flask_admin/contrib/peewee/filters.py:111
#: ../flask_admin/contrib/sqla/filters.py:118
msgid "not in list"
msgstr "در این لیست نباشد"

#: ../flask_admin/contrib/mongoengine/filters.py:222
#: ../flask_admin/contrib/peewee/filters.py:207
#: ../flask_admin/contrib/peewee/filters.py:244
#: ../flask_admin/contrib/peewee/filters.py:281
#: ../flask_admin/contrib/sqla/filters.py:213
#: ../flask_admin/contrib/sqla/filters.py:250
#: ../flask_admin/contrib/sqla/filters.py:287
msgid "not between"
msgstr "بین این دو نباشد"

#: ../flask_admin/contrib/mongoengine/filters.py:247
msgid "ObjectId equals"
msgstr ""

#: ../flask_admin/contrib/mongoengine/view.py:551
#, python-format
msgid "Failed to get model. %(error)s"
msgstr "خواندن مدل با مشکل روبرو شد. %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:570
#: ../flask_admin/contrib/peewee/view.py:435
#: ../flask_admin/contrib/pymongo/view.py:316
#: ../flask_admin/contrib/sqla/view.py:1078
#, python-format
msgid "Failed to create record. %(error)s"
msgstr "ساخت مدل با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:596
#: ../flask_admin/contrib/peewee/view.py:454
#: ../flask_admin/contrib/pymongo/view.py:341
#: ../flask_admin/contrib/sqla/view.py:1104 ../flask_admin/model/base.py:2305
#: ../flask_admin/model/base.py:2313 ../flask_admin/model/base.py:2315
#, python-format
msgid "Failed to update record. %(error)s"
msgstr "بروزرسانی مدل با مشکل روبرو شد%(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:619
#: ../flask_admin/contrib/peewee/view.py:469
#: ../flask_admin/contrib/pymongo/view.py:366
#: ../flask_admin/contrib/sqla/view.py:1129
#, python-format
msgid "Failed to delete record. %(error)s"
msgstr "حذف مدل با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:659
#: ../flask_admin/contrib/peewee/view.py:488
#: ../flask_admin/contrib/pymongo/view.py:385
#: ../flask_admin/contrib/sqla/view.py:1150
msgid "Are you sure you want to delete selected records?"
msgstr "آیا از حذف مدل ها اطمینان دارید"

#: ../flask_admin/contrib/mongoengine/view.py:668
#: ../flask_admin/contrib/peewee/view.py:505
#: ../flask_admin/contrib/pymongo/view.py:395
#: ../flask_admin/contrib/sqla/view.py:1166 ../flask_admin/model/base.py:2118
#, python-format
msgid "Record was successfully deleted."
msgid_plural "%(count)s records were successfully deleted."
msgstr[0] "مدل با موفقیت حذف شد"
msgstr[1] "%(count)s رکورد با موفقیت حذف شد."

#: ../flask_admin/contrib/mongoengine/view.py:674
#: ../flask_admin/contrib/peewee/view.py:511
#: ../flask_admin/contrib/pymongo/view.py:400
#: ../flask_admin/contrib/sqla/view.py:1174
#, python-format
msgid "Failed to delete records. %(error)s"
msgstr "حذف مدل ها با مشکل روبرو شد %(error)s"

#: ../flask_admin/contrib/sqla/fields.py:126
#: ../flask_admin/contrib/sqla/fields.py:176
#: ../flask_admin/contrib/sqla/fields.py:181 ../flask_admin/model/fields.py:173
#: ../flask_admin/model/fields.py:222
msgid "Not a valid choice"
msgstr "انتخاب مناسبی نیست"

#: ../flask_admin/contrib/sqla/fields.py:186
msgid "Key"
msgstr ""

#: ../flask_admin/contrib/sqla/fields.py:187
msgid "Value"
msgstr ""

#: ../flask_admin/contrib/sqla/validators.py:42
msgid "Already exists."
msgstr "قبلا وجود داشته است"

#: ../flask_admin/contrib/sqla/validators.py:60
#, python-format
msgid "At least %(num)d item is required"
msgid_plural "At least %(num)d items are required"
msgstr[0] ""
msgstr[1] ""

#: ../flask_admin/contrib/sqla/view.py:1057
#, python-format
msgid "Integrity error. %(message)s"
msgstr "خطای یکپارچگی داده. %(message)s"

#: ../flask_admin/form/fields.py:98
msgid "Invalid time format"
msgstr "قالب زمان اشتباه است"

#: ../flask_admin/form/fields.py:144
msgid "Invalid Choice: could not coerce"
msgstr "انتخاب اشتباه"

#: ../flask_admin/form/fields.py:208
msgid "Invalid JSON"
msgstr "JSON نامعتبر است"

#: ../flask_admin/form/upload.py:207
msgid "Invalid file extension"
msgstr "پسوند فایل غیرمجاز است"

#: ../flask_admin/form/upload.py:214 ../flask_admin/form/upload.py:281
#, python-format
msgid "File \"%s\" already exists."
msgstr "فایل \"%s\" قبلا وجود داشته است"

#: ../flask_admin/model/base.py:1649
msgid "There are no items in the table."
msgstr "هیچ موردی در این جدول وجود ندارد."

#: ../flask_admin/model/base.py:1673
#, python-format
msgid "Invalid Filter Value: %(value)s"
msgstr "مقدار فیلتر معتبر نیست: %(value)s"

#: ../flask_admin/model/base.py:1984
msgid "Record was successfully created."
msgstr "مدل با موفقیت ساخته شد"

#: ../flask_admin/model/base.py:2028 ../flask_admin/model/base.py:2080
#: ../flask_admin/model/base.py:2113 ../flask_admin/model/base.py:2297
msgid "Record does not exist."
msgstr "رکورد وجود ندارد."

#: ../flask_admin/model/base.py:2037 ../flask_admin/model/base.py:2301
msgid "Record was successfully saved."
msgstr "مدل با موفقیت ذخیره شد"

#: ../flask_admin/model/base.py:2222
msgid "Tablib dependency not installed."
msgstr ""

#: ../flask_admin/model/base.py:2249
#, python-format
msgid "Export type \"%(type)s not supported."
msgstr ""

#: ../flask_admin/model/filters.py:103 ../flask_admin/model/widgets.py:111
msgid "Yes"
msgstr "بله"

#: ../flask_admin/model/filters.py:104 ../flask_admin/model/widgets.py:110
msgid "No"
msgstr "خیر"

#: ../flask_admin/model/filters.py:172 ../flask_admin/model/filters.py:212
#: ../flask_admin/model/filters.py:257
msgid "between"
msgstr "بین این دو باشد"

#: ../flask_admin/model/template.py:81 ../flask_admin/model/template.py:88
#: ../flask_admin/templates/bootstrap2/admin/model/modals/details.html:37
#: ../flask_admin/templates/bootstrap3/admin/model/modals/details.html:8
msgid "View Record"
msgstr "مشاهده سابقه"

#: ../flask_admin/model/template.py:95 ../flask_admin/model/template.py:102
#: ../flask_admin/model/template.py:109
#: ../flask_admin/templates/bootstrap2/admin/model/modals/edit.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/modals/edit.html:11
msgid "Edit Record"
msgstr "ویرایش رکورد"

#: ../flask_admin/model/widgets.py:61
msgid "Please select model"
msgstr ""

#: ../flask_admin/templates/bootstrap2/admin/actions.html:4
#: ../flask_admin/templates/bootstrap3/admin/actions.html:4
msgid "With selected"
msgstr "با انتخاب شده ها"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:200
#: ../flask_admin/templates/bootstrap3/admin/lib.html:190
msgid "Save"
msgstr "ذخیره"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:205
#: ../flask_admin/templates/bootstrap3/admin/lib.html:195
msgid "Cancel"
msgstr "لغو"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:256
#: ../flask_admin/templates/bootstrap3/admin/lib.html:247
msgid "Save and Add Another"
msgstr "ذخیره و اضافه یک ردیف جدید"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:259
#: ../flask_admin/templates/bootstrap3/admin/lib.html:250
msgid "Save and Continue Editing"
msgstr "ذخیره و ادامه ویرایش"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:9
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:9
msgid "Root"
msgstr "ریشه"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:40
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:49
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:90
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:99
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:40
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:49
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:89
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:98
#, python-format
msgid "Sort by %(name)s"
msgstr "مرتب سازی بر اساس %(name)s"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:74
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:77
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:74
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:77
msgid "Rename File"
msgstr "تغییر نام پرونده"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:88
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:88
#, python-format
msgid "Are you sure you want to delete \\'%(name)s\\' recursively?"
msgstr "آیا از حذف محتویات \\'%(name)s\\' اطمینان دارید؟"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:97
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:97
#, python-format
msgid "Are you sure you want to delete \\'%(name)s\\'?"
msgstr "آیا از حذف  \\'%(name)s\\'اطمینان دارید؟"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:125
msgid "Size"
msgstr ""

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:185
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:185
msgid "Please select at least one file."
msgstr "حداقل یک فایل انتخاب کنید"

#: ../flask_admin/templates/bootstrap2/admin/model/create.html:14
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:8
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:14
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/create.html:14
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:8
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:14
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:17
msgid "List"
msgstr "لیست"

#: ../flask_admin/templates/bootstrap2/admin/model/create.html:17
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:12
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:18
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap3/admin/model/create.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:12
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:18
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:25
msgid "Create"
msgstr "ایجاد"

#: ../flask_admin/templates/bootstrap2/admin/model/details.html:21
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:26
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:21
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:26
msgid "Details"
msgstr "جزییات"

#: ../flask_admin/templates/bootstrap2/admin/model/details.html:29
#: ../flask_admin/templates/bootstrap2/admin/model/modals/details.html:8
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:28
#: ../flask_admin/templates/bootstrap3/admin/model/modals/details.html:15
msgid "Filter"
msgstr "فیلتر"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:13
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:14
msgid "Delete?"
msgstr "حذف؟"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:30
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:33
msgid "New"
msgstr "تازه"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:40
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:43
msgid "Add"
msgstr "اضافه"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:3
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:3
msgid "Add Filter"
msgstr "اضافه کردن فیلتر"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:18
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:30
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:18
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:30
msgid "Export"
msgstr "خروجی گرفتن"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:38
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:38
msgid "Apply"
msgstr "اعمال"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:40
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:40
msgid "Reset Filters"
msgstr "برگرداندن فیلتر ها به حالت اول"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:59
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:66
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:59
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:64
msgid "Search"
msgstr "جستجو"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:74
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:77
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:78
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:79
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:72
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:75
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:76
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:77
msgid "items"
msgstr ""

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap2/admin/model/modals/create.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap3/admin/model/modals/create.html:10
msgid "Create New Record"
msgstr "ایجاد رکورد جدید"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:77
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:76
msgid "Select all records"
msgstr "انتخاب همه رکوردها"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:120
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:119
msgid "Select record"
msgstr "انتخاب رکورد"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:185
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:186
msgid "Please select at least one record."
msgstr "حداقل یک پوشه انتخاب کنید"

#: ../flask_admin/templates/bootstrap2/admin/model/row_actions.html:34
#: ../flask_admin/templates/bootstrap3/admin/model/row_actions.html:34
msgid "Are you sure you want to delete this record?"
msgstr "آیا از حذف این موارد اطمینان دارید؟"

