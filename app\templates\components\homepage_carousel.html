<!-- 首页轮播图组件 -->
<div class="homepage-carousel-container mb-4">
    <div id="homepageCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
        <!-- 轮播指示器 -->
        <div class="carousel-indicators" id="carouselIndicators">
            <!-- 动态生成 -->
        </div>
        
        <!-- 轮播内容 -->
        <div class="carousel-inner" id="carouselInner">
            <!-- 动态生成 -->
        </div>
        
        <!-- 轮播控制按钮 -->
        <button class="carousel-control-prev" type="button" data-bs-target="#homepageCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">上一张</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#homepageCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">下一张</span>
        </button>
        
        <!-- 加载状态 -->
        <div class="carousel-loading text-center py-5" id="carouselLoading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载轮播图...</p>
        </div>
        
        <!-- 空状态 -->
        <div class="carousel-empty text-center py-5 d-none" id="carouselEmpty">
            <i class="fas fa-images fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无轮播图</h5>
            <p class="text-muted">管理员可以在后台添加轮播图片</p>
        </div>
    </div>
</div>

<style nonce="{{ csp_nonce }}">
.homepage-carousel-container {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.homepage-carousel-container .carousel {
    border-radius: 15px;
    overflow: hidden;
}

.homepage-carousel-container .carousel-item {
    height: 400px;
    position: relative;
}

.homepage-carousel-container .carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.homepage-carousel-container .carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 40px 20px 20px;
    text-align: left;
    border-radius: 0 0 15px 15px;
}

.homepage-carousel-container .carousel-caption h5 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.homepage-carousel-container .carousel-caption p {
    font-size: 1rem;
    margin-bottom: 0;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.homepage-carousel-container .carousel-indicators {
    bottom: 15px;
    margin-bottom: 0;
}

.homepage-carousel-container .carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 4px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.homepage-carousel-container .carousel-indicators .active {
    background-color: #fff;
    transform: scale(1.2);
}

.homepage-carousel-container .carousel-control-prev,
.homepage-carousel-container .carousel-control-next {
    width: 50px;
    height: 50px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.homepage-carousel-container .carousel-control-prev {
    left: 15px;
}

.homepage-carousel-container .carousel-control-next {
    right: 15px;
}

.homepage-carousel-container .carousel-control-prev:hover,
.homepage-carousel-container .carousel-control-next:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-50%) scale(1.1);
}

.homepage-carousel-container .carousel-control-prev-icon,
.homepage-carousel-container .carousel-control-next-icon {
    width: 20px;
    height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .homepage-carousel-container .carousel-item {
        height: 250px;
    }
    
    .homepage-carousel-container .carousel-caption {
        padding: 20px 15px 15px;
    }
    
    .homepage-carousel-container .carousel-caption h5 {
        font-size: 1.2rem;
    }
    
    .homepage-carousel-container .carousel-caption p {
        font-size: 0.9rem;
    }
    
    .homepage-carousel-container .carousel-control-prev,
    .homepage-carousel-container .carousel-control-next {
        width: 40px;
        height: 40px;
    }
    
    .homepage-carousel-container .carousel-control-prev {
        left: 10px;
    }
    
    .homepage-carousel-container .carousel-control-next {
        right: 10px;
    }
}

/* 加载和空状态样式 */
.carousel-loading,
.carousel-empty {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin: 20px;
}

/* 点击效果 */
.homepage-carousel-container .carousel-item {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.homepage-carousel-container .carousel-item:hover {
    transform: scale(1.02);
}

/* 淡入淡出动画 */
.homepage-carousel-container .carousel-item {
    transition: opacity 0.6s ease-in-out;
}

.homepage-carousel-container .carousel-item.active {
    opacity: 1;
}

.homepage-carousel-container .carousel-item:not(.active) {
    opacity: 0;
}
</style>

<script nonce="{{ csp_nonce }}">
// 轮播图管理类
class HomepageCarousel {
    constructor() {
        this.container = document.getElementById('homepageCarousel');
        this.indicators = document.getElementById('carouselIndicators');
        this.inner = document.getElementById('carouselInner');
        this.loading = document.getElementById('carouselLoading');
        this.empty = document.getElementById('carouselEmpty');
        this.carousel = null;
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadCarouselData();
        } catch (error) {
            console.error('轮播图初始化失败:', error);
            this.showEmpty();
        }
    }
    
    async loadCarouselData() {
        try {
            const response = await fetch('/api/carousel/list');
            const data = await response.json();
            
            if (data.success && data.data && data.data.length > 0) {
                this.renderCarousel(data.data);
                this.hideLoading();
                await new Promise(resolve => setTimeout(resolve, 0));
                this.initBootstrapCarousel();
            } else {
                this.showEmpty();
            }
        } catch (error) {
            console.error('加载轮播图数据失败:', error);
            this.showEmpty();
        }
    }
    
    renderCarousel(items) {
        // 清空现有内容
        this.indicators.innerHTML = '';
        this.inner.innerHTML = '';
        
        items.forEach((item, index) => {
            // 创建指示器
            const indicator = document.createElement('button');
            indicator.type = 'button';
            indicator.setAttribute('data-bs-target', '#homepageCarousel');
            indicator.setAttribute('data-bs-slide-to', index);
            indicator.setAttribute('aria-label', `轮播图 ${index + 1}`);
            if (index === 0) {
                indicator.classList.add('active');
                indicator.setAttribute('aria-current', 'true');
            }
            this.indicators.appendChild(indicator);
            
            // 创建轮播项
            const carouselItem = document.createElement('div');
            carouselItem.className = `carousel-item${index === 0 ? ' active' : ''}`;
            
            const img = document.createElement('img');
            img.src = item.image_path;
            img.alt = item.title;
            img.className = 'd-block w-100';
            img.loading = 'lazy';
            
            carouselItem.appendChild(img);
            
            // 添加标题和描述
            if (item.title || item.description) {
                const caption = document.createElement('div');
                caption.className = 'carousel-caption d-none d-md-block';
                
                if (item.title) {
                    const title = document.createElement('h5');
                    title.textContent = item.title;
                    caption.appendChild(title);
                }
                
                if (item.description) {
                    const desc = document.createElement('p');
                    desc.textContent = item.description;
                    caption.appendChild(desc);
                }
                
                carouselItem.appendChild(caption);
            }
            
            // 添加点击事件
            if (item.link_url) {
                carouselItem.style.cursor = 'pointer';
                carouselItem.addEventListener('click', () => {
                    if (item.link_url.startsWith('http')) {
                        window.open(item.link_url, '_blank');
                    } else {
                        window.location.href = item.link_url;
                    }
                });
            }
            
            this.inner.appendChild(carouselItem);
        });
    }
    
    initBootstrapCarousel() {
        // 初始化Bootstrap轮播
        if (this.carousel) {
            this.carousel.dispose();
        }

        // 增加检查，确保指示器元素在DOM中可被查找到
        // const indicatorsExist = setInterval(() => {
        //     const firstIndicator = this.indicators.querySelector('[data-bs-slide-to="0"]');
        //     if (firstIndicator) {
        //         clearInterval(indicatorsExist); // 指示器已找到，清除定时器

                this.carousel = new bootstrap.Carousel(this.container, {
                    interval: 5000,
                    wrap: true,
                    touch: true
                });

                // 添加键盘支持
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        this.carousel.prev();
                    } else if (e.key === 'ArrowRight') {
                        this.carousel.next();
                    }
                });

        //     }
        // }, 50); // 每隔50毫秒检查一次

        // 手动处理指示器同步，避免TypeError
        this.container.addEventListener('slid.bs.carousel', (event) => {
            const activeIndex = event.to;
            const indicators = this.indicators.querySelectorAll('button');

            indicators.forEach(indicator => {
                indicator.classList.remove('active');
                indicator.removeAttribute('aria-current');
            });

            const currentIndicator = this.indicators.querySelector(`[data-bs-slide-to="${activeIndex}"]`);
            if (currentIndicator) {
                currentIndicator.classList.add('active');
                currentIndicator.setAttribute('aria-current', 'true');
            }
        });

    }
    
    hideLoading() {
        this.loading.style.display = 'none';
    }
    
    showEmpty() {
        this.loading.style.display = 'none';
        this.empty.classList.remove('d-none');
        this.indicators.style.display = 'none';
        this.container.querySelector('.carousel-control-prev').style.display = 'none';
        this.container.querySelector('.carousel-control-next').style.display = 'none';
    }
}

// 页面加载完成后初始化轮播图
document.addEventListener('DOMContentLoaded', function() {
    new HomepageCarousel();
});
</script>
