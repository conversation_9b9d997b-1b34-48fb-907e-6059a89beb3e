# Catalan translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0dev\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-10-05 13:42+0200\n"
"PO-Revision-Date: 2014-01-16 09:58+0100\n"
"Last-Translator: Òscar Vilaplana <<EMAIL>>\n"
"Language-Team: ca <<EMAIL>>\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nom de camp no vàlid '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "El camp ha de ser igual a %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "El camp ha de contenir almenys %(min)d caràcter."
msgstr[1] "El camp ha de contenir almenys %(min)d caràcters."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "El camp no pot contenir més d'%(max)d caràcter."
msgstr[1] "El camp no pot contenir més de %(max)d caràcters."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "El camp ha de contenir entre %(min)d i %(min)d caràcters."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "El nombre ha de ser major o igual a %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "El nombre ha de ser com a màxim %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "El nombre ha d'estar entre %(min)s i %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Aquest camp és obligatori."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Valor no vàlid."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Adreça d'e-mail no vàlida."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Adreça IP no vàlida."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Adreça MAC no vàlida."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "URL no vàlida."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "UUID no vàlid."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valor no vàlid, ha de ser un d'entre: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valor no vàlid, no pot ser cap d'aquests: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited"
msgstr "Aquest camp és obligatori."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value"
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Token CSRF no vàlid"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Falta el token CSRF"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "Ha fallat la comprovació de CSRF"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Token CSRF caducat"

#: src/wtforms/fields/choices.py:135
msgid "Invalid Choice: could not coerce."
msgstr "Opció no vàlida"

#: src/wtforms/fields/choices.py:139 src/wtforms/fields/choices.py:192
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:148
msgid "Not a valid choice."
msgstr "Opció no acceptada"

#: src/wtforms/fields/choices.py:185
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Opció o opcions no vàlides: alguna de les entrades no s'ha pogut processar"

#: src/wtforms/fields/choices.py:204
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' no és una opció acceptada per a aquest camp"
msgstr[1] "'%(value)s' no és una opció acceptada per a aquest camp"

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Valor de data i hora no vàlid"

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Valor de data no vàlid"

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Valor de data no vàlid"

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Valor enter no vàlid"

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Valor decimal no vàlid"

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Valor en coma flotant no vàlid"
