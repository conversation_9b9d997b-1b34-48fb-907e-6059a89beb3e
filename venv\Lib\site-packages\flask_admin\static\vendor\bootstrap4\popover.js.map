{"version": 3, "file": "popover.js", "sources": ["../src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'popover'\nconst VERSION             = '4.3.1'\nconst DATA_KEY            = 'bs.popover'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\nconst CLASS_PREFIX        = 'bs-popover'\nconst BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement : 'right',\n  trigger   : 'click',\n  content   : '',\n  template  : '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content : '(string|element|function)'\n}\n\nconst ClassName = {\n  FADE : 'fade',\n  SHOW : 'show'\n}\n\nconst Selector = {\n  TITLE   : '.popover-header',\n  CONTENT : '.popover-body'\n}\n\nconst Event = {\n  HIDE       : `hide${EVENT_KEY}`,\n  HIDDEN     : `hidden${EVENT_KEY}`,\n  SHOW       : `show${EVENT_KEY}`,\n  SHOWN      : `shown${EVENT_KEY}`,\n  INSERTED   : `inserted${EVENT_KEY}`,\n  CLICK      : `click${EVENT_KEY}`,\n  FOCUSIN    : `focusin${EVENT_KEY}`,\n  FOCUSOUT   : `focusout${EVENT_KEY}`,\n  MOUSEENTER : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE : `mouseleave${EVENT_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n    this.setElementContent($tip.find(Selector.CONTENT), content)\n\n    $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n  }\n\n  // Private\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "RegExp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "placement", "trigger", "content", "template", "DefaultType", "ClassName", "FADE", "SHOW", "Selector", "TITLE", "CONTENT", "Event", "HIDE", "HIDDEN", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "Popover", "isWithContent", "getTitle", "_getContent", "addAttachmentClass", "attachment", "getTipElement", "addClass", "tip", "config", "<PERSON><PERSON><PERSON><PERSON>", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "call", "element", "removeClass", "getAttribute", "_cleanTipClass", "tabClass", "attr", "match", "length", "join", "_jQueryInterface", "each", "data", "_config", "test", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAkB,SAA5B;EACA,IAAMC,OAAO,GAAe,OAA5B;EACA,IAAMC,QAAQ,GAAc,YAA5B;EACA,IAAMC,SAAS,SAAiBD,QAAhC;EACA,IAAME,kBAAkB,GAAIC,CAAC,CAACC,EAAF,CAAKN,IAAL,CAA5B;EACA,IAAMO,YAAY,GAAU,YAA5B;EACA,IAAMC,kBAAkB,GAAI,IAAIC,MAAJ,aAAqBF,YAArB,WAAyC,GAAzC,CAA5B;;EAEA,IAAMG,OAAO,qBACRC,OAAO,CAACD,OADA;EAEXE,EAAAA,SAAS,EAAG,OAFD;EAGXC,EAAAA,OAAO,EAAK,OAHD;EAIXC,EAAAA,OAAO,EAAK,EAJD;EAKXC,EAAAA,QAAQ,EAAI,yCACA,2BADA,GAEA,kCAFA,GAGA;EARD,EAAb;;EAWA,IAAMC,WAAW,qBACZL,OAAO,CAACK,WADI;EAEfF,EAAAA,OAAO,EAAG;EAFK,EAAjB;;EAKA,IAAMG,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAG,MADS;EAEhBC,EAAAA,IAAI,EAAG;EAFS,CAAlB;EAKA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,KAAK,EAAK,iBADK;EAEfC,EAAAA,OAAO,EAAG;EAFK,CAAjB;EAKA,IAAMC,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAgBrB,SADR;EAEZsB,EAAAA,MAAM,aAAgBtB,SAFV;EAGZgB,EAAAA,IAAI,WAAgBhB,SAHR;EAIZuB,EAAAA,KAAK,YAAgBvB,SAJT;EAKZwB,EAAAA,QAAQ,eAAgBxB,SALZ;EAMZyB,EAAAA,KAAK,YAAgBzB,SANT;EAOZ0B,EAAAA,OAAO,cAAgB1B,SAPX;EAQZ2B,EAAAA,QAAQ,eAAgB3B,SARZ;EASZ4B,EAAAA,UAAU,iBAAgB5B,SATd;EAUZ6B,EAAAA,UAAU,iBAAgB7B;EAG5B;;;;;;EAbc,CAAd;;MAmBM8B;;;;;;;;;;;EA+BJ;WAEAC,gBAAA,yBAAgB;EACd,WAAO,KAAKC,QAAL,MAAmB,KAAKC,WAAL,EAA1B;EACD;;WAEDC,qBAAA,4BAAmBC,UAAnB,EAA+B;EAC7BjC,IAAAA,CAAC,CAAC,KAAKkC,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAoCjC,YAApC,SAAoD+B,UAApD;EACD;;WAEDC,gBAAA,yBAAgB;EACd,SAAKE,GAAL,GAAW,KAAKA,GAAL,IAAYpC,CAAC,CAAC,KAAKqC,MAAL,CAAY3B,QAAb,CAAD,CAAwB,CAAxB,CAAvB;EACA,WAAO,KAAK0B,GAAZ;EACD;;WAEDE,aAAA,sBAAa;EACX,QAAMC,IAAI,GAAGvC,CAAC,CAAC,KAAKkC,aAAL,EAAD,CAAd,CADW;;EAIX,SAAKM,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAU1B,QAAQ,CAACC,KAAnB,CAAvB,EAAkD,KAAKc,QAAL,EAAlD;;EACA,QAAIrB,OAAO,GAAG,KAAKsB,WAAL,EAAd;;EACA,QAAI,OAAOtB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACiC,IAAR,CAAa,KAAKC,OAAlB,CAAV;EACD;;EACD,SAAKH,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAU1B,QAAQ,CAACE,OAAnB,CAAvB,EAAoDR,OAApD;EAEA8B,IAAAA,IAAI,CAACK,WAAL,CAAoBhC,SAAS,CAACC,IAA9B,SAAsCD,SAAS,CAACE,IAAhD;EACD;;;WAIDiB,cAAA,uBAAc;EACZ,WAAO,KAAKY,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAKR,MAAL,CAAY5B,OADd;EAED;;WAEDqC,iBAAA,0BAAiB;EACf,QAAMP,IAAI,GAAGvC,CAAC,CAAC,KAAKkC,aAAL,EAAD,CAAd;EACA,QAAMa,QAAQ,GAAGR,IAAI,CAACS,IAAL,CAAU,OAAV,EAAmBC,KAAnB,CAAyB9C,kBAAzB,CAAjB;;EACA,QAAI4C,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACG,MAAT,GAAkB,CAA3C,EAA8C;EAC5CX,MAAAA,IAAI,CAACK,WAAL,CAAiBG,QAAQ,CAACI,IAAT,CAAc,EAAd,CAAjB;EACD;EACF;;;YAIMC,mBAAP,0BAAwBf,MAAxB,EAAgC;EAC9B,WAAO,KAAKgB,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGtD,CAAC,CAAC,IAAD,CAAD,CAAQsD,IAAR,CAAazD,QAAb,CAAX;;EACA,UAAM0D,OAAO,GAAG,OAAOlB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACiB,IAAD,IAAS,eAAeE,IAAf,CAAoBnB,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACiB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI1B,OAAJ,CAAY,IAAZ,EAAkB2B,OAAlB,CAAP;EACAvD,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQsD,IAAR,CAAazD,QAAb,EAAuByD,IAAvB;EACD;;EAED,UAAI,OAAOjB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOiB,IAAI,CAACjB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIoB,SAAJ,wBAAkCpB,MAAlC,QAAN;EACD;;EACDiB,QAAAA,IAAI,CAACjB,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;;;EAjGD;0BAEqB;EACnB,aAAOzC,OAAP;EACD;;;0BAEoB;EACnB,aAAOS,OAAP;EACD;;;0BAEiB;EAChB,aAAOV,IAAP;EACD;;;0BAEqB;EACpB,aAAOE,QAAP;EACD;;;0BAEkB;EACjB,aAAOqB,KAAP;EACD;;;0BAEsB;EACrB,aAAOpB,SAAP;EACD;;;0BAEwB;EACvB,aAAOa,WAAP;EACD;;;;IA7BmBL;EAqGtB;;;;;;;EAMAN,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAaiC,OAAO,CAACwB,gBAArB;EACApD,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAW+D,WAAX,GAAyB9B,OAAzB;;EACA5B,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAWgE,UAAX,GAAwB,YAAM;EAC5B3D,EAAAA,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAO6B,OAAO,CAACwB,gBAAf;EACD,CAHD;;;;;;;;"}