{% extends 'admin/master.html' %}
{% import 'admin/lib.html' as lib with context %}

{% block body %}
  {% block navlinks %}
  <ul class="nav nav-tabs">
    <li>
        <a href="{{ return_url }}">{{ _gettext('List') }}</a>
    </li>
    {%- if admin_view.can_create -%}
    <li>
        <a href="{{ get_url('.create_view', url=return_url) }}">{{ _gettext('Create') }}</a>
    </li>
    {%- endif -%}
    {%- if admin_view.can_edit -%}
    <li>
        <a href="{{ get_url('.edit_view', id=request.args.get('id'), url=return_url) }}">{{ _gettext('Edit') }}</a>
    </li>
    {%- endif -%}
    <li class="active">
        <a href="javascript:void(0)">{{ _gettext('Details') }}</a>
    </li>
  </ul>
  {% endblock %}

  {% block details_search %}
    <div class="row-fluid">
      <div class="input-prepend fa_filter_container">
        <span class="add-on">{{ _gettext('Filter') }}</span>
        <input id="fa_filter" id="prependedInput" type="text">
      </div>
    </div>
  {% endblock %}

  {% block details_table %}
    <table class="table table-hover table-bordered searchable">
    {% for c, name in details_columns %}
      <tr>
        <td>
          <b>{{ name }}</b>
        </td>
        <td>
        {{ get_value(model, c) }}
        </td>
      </tr>
    {% endfor %}
    </table>
  {% endblock %}
{% endblock %}

{% block tail %}
  {{ super() }}
  <script src="{{ admin_static.url(filename='admin/js/details_filter.js', v='1.0.0') }}"></script>
{% endblock %}
