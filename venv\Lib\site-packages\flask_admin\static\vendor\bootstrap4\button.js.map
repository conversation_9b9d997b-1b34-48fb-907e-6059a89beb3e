{"version": 3, "file": "button.js", "sources": ["../src/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.3.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'button'\nconst VERSION             = '4.3.1'\nconst DATA_KEY            = 'bs.button'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst ClassName = {\n  ACTIVE : 'active',\n  BUTTON : 'btn',\n  FOCUS  : 'focus'\n}\n\nconst Selector = {\n  DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n  DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n  INPUT              : 'input:not([type=\"hidden\"])',\n  ACTIVE             : '.active',\n  BUTTON             : '.btn'\n}\n\nconst Event = {\n  CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n  FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(\n      Selector.DATA_TOGGLE\n    )[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(Selector.INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked &&\n            this._element.classList.contains(ClassName.ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(Selector.ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(ClassName.ACTIVE)\n            }\n          }\n        }\n\n        if (triggerChangeEvent) {\n          if (input.hasAttribute('disabled') ||\n            rootElement.hasAttribute('disabled') ||\n            input.classList.contains('disabled') ||\n            rootElement.classList.contains('disabled')) {\n            return\n          }\n          input.checked = !this._element.classList.contains(ClassName.ACTIVE)\n          $(input).trigger('change')\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (addAriaPressed) {\n      this._element.setAttribute('aria-pressed',\n        !this._element.classList.contains(ClassName.ACTIVE))\n    }\n\n    if (triggerChangeEvent) {\n      $(this._element).toggleClass(ClassName.ACTIVE)\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    event.preventDefault()\n\n    let button = event.target\n\n    if (!$(button).hasClass(ClassName.BUTTON)) {\n      button = $(button).closest(Selector.BUTTON)\n    }\n\n    Button._jQueryInterface.call($(button), 'toggle')\n  })\n  .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    const button = $(event.target).closest(Selector.BUTTON)[0]\n    $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ClassName", "ACTIVE", "BUTTON", "FOCUS", "Selector", "DATA_TOGGLE_CARROT", "DATA_TOGGLE", "INPUT", "Event", "CLICK_DATA_API", "FOCUS_BLUR_DATA_API", "<PERSON><PERSON>", "element", "_element", "toggle", "triggerChangeEvent", "addAriaPressed", "rootElement", "closest", "input", "querySelector", "type", "checked", "classList", "contains", "activeElement", "removeClass", "hasAttribute", "trigger", "focus", "setAttribute", "toggleClass", "dispose", "removeData", "_jQueryInterface", "config", "each", "data", "document", "on", "event", "preventDefault", "button", "target", "hasClass", "call", "test", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;EASA;;;;;;EAMA,IAAMA,IAAI,GAAkB,QAA5B;EACA,IAAMC,OAAO,GAAe,OAA5B;EACA,IAAMC,QAAQ,GAAc,WAA5B;EACA,IAAMC,SAAS,SAAiBD,QAAhC;EACA,IAAME,YAAY,GAAU,WAA5B;EACA,IAAMC,kBAAkB,GAAIC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA5B;EAEA,IAAMQ,SAAS,GAAG;EAChBC,EAAAA,MAAM,EAAG,QADO;EAEhBC,EAAAA,MAAM,EAAG,KAFO;EAGhBC,EAAAA,KAAK,EAAI;EAHO,CAAlB;EAMA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,kBAAkB,EAAG,yBADN;EAEfC,EAAAA,WAAW,EAAU,yBAFN;EAGfC,EAAAA,KAAK,EAAgB,4BAHN;EAIfN,EAAAA,MAAM,EAAe,SAJN;EAKfC,EAAAA,MAAM,EAAe;EALN,CAAjB;EAQA,IAAMM,KAAK,GAAG;EACZC,EAAAA,cAAc,YAAgBd,SAAhB,GAA4BC,YAD9B;EAEZc,EAAAA,mBAAmB,EAAG,UAAQf,SAAR,GAAoBC,YAApB,mBACSD,SADT,GACqBC,YADrB;EAIxB;;;;;;EANc,CAAd;;MAYMe;;;EACJ,kBAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,SAAA,kBAAS;EACP,QAAIC,kBAAkB,GAAG,IAAzB;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAMC,WAAW,GAAGnB,CAAC,CAAC,KAAKe,QAAN,CAAD,CAAiBK,OAAjB,CAClBd,QAAQ,CAACE,WADS,EAElB,CAFkB,CAApB;;EAIA,QAAIW,WAAJ,EAAiB;EACf,UAAME,KAAK,GAAG,KAAKN,QAAL,CAAcO,aAAd,CAA4BhB,QAAQ,CAACG,KAArC,CAAd;;EAEA,UAAIY,KAAJ,EAAW;EACT,YAAIA,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;EAC1B,cAAIF,KAAK,CAACG,OAAN,IACF,KAAKT,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiCxB,SAAS,CAACC,MAA3C,CADF,EACsD;EACpDc,YAAAA,kBAAkB,GAAG,KAArB;EACD,WAHD,MAGO;EACL,gBAAMU,aAAa,GAAGR,WAAW,CAACG,aAAZ,CAA0BhB,QAAQ,CAACH,MAAnC,CAAtB;;EAEA,gBAAIwB,aAAJ,EAAmB;EACjB3B,cAAAA,CAAC,CAAC2B,aAAD,CAAD,CAAiBC,WAAjB,CAA6B1B,SAAS,CAACC,MAAvC;EACD;EACF;EACF;;EAED,YAAIc,kBAAJ,EAAwB;EACtB,cAAII,KAAK,CAACQ,YAAN,CAAmB,UAAnB,KACFV,WAAW,CAACU,YAAZ,CAAyB,UAAzB,CADE,IAEFR,KAAK,CAACI,SAAN,CAAgBC,QAAhB,CAAyB,UAAzB,CAFE,IAGFP,WAAW,CAACM,SAAZ,CAAsBC,QAAtB,CAA+B,UAA/B,CAHF,EAG8C;EAC5C;EACD;;EACDL,UAAAA,KAAK,CAACG,OAAN,GAAgB,CAAC,KAAKT,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiCxB,SAAS,CAACC,MAA3C,CAAjB;EACAH,UAAAA,CAAC,CAACqB,KAAD,CAAD,CAASS,OAAT,CAAiB,QAAjB;EACD;;EAEDT,QAAAA,KAAK,CAACU,KAAN;EACAb,QAAAA,cAAc,GAAG,KAAjB;EACD;EACF;;EAED,QAAIA,cAAJ,EAAoB;EAClB,WAAKH,QAAL,CAAciB,YAAd,CAA2B,cAA3B,EACE,CAAC,KAAKjB,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiCxB,SAAS,CAACC,MAA3C,CADH;EAED;;EAED,QAAIc,kBAAJ,EAAwB;EACtBjB,MAAAA,CAAC,CAAC,KAAKe,QAAN,CAAD,CAAiBkB,WAAjB,CAA6B/B,SAAS,CAACC,MAAvC;EACD;EACF;;WAED+B,UAAA,mBAAU;EACRlC,IAAAA,CAAC,CAACmC,UAAF,CAAa,KAAKpB,QAAlB,EAA4BnB,QAA5B;EACA,SAAKmB,QAAL,GAAgB,IAAhB;EACD;;;WAIMqB,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGvC,CAAC,CAAC,IAAD,CAAD,CAAQuC,IAAR,CAAa3C,QAAb,CAAX;;EAEA,UAAI,CAAC2C,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI1B,MAAJ,CAAW,IAAX,CAAP;EACAb,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQuC,IAAR,CAAa3C,QAAb,EAAuB2C,IAAvB;EACD;;EAED,UAAIF,MAAM,KAAK,QAAf,EAAyB;EACvBE,QAAAA,IAAI,CAACF,MAAD,CAAJ;EACD;EACF,KAXM,CAAP;EAYD;;;;0BA5EoB;EACnB,aAAO1C,OAAP;EACD;;;;;EA6EH;;;;;;;EAMAK,CAAC,CAACwC,QAAD,CAAD,CACGC,EADH,CACM/B,KAAK,CAACC,cADZ,EAC4BL,QAAQ,CAACC,kBADrC,EACyD,UAACmC,KAAD,EAAW;EAChEA,EAAAA,KAAK,CAACC,cAAN;EAEA,MAAIC,MAAM,GAAGF,KAAK,CAACG,MAAnB;;EAEA,MAAI,CAAC7C,CAAC,CAAC4C,MAAD,CAAD,CAAUE,QAAV,CAAmB5C,SAAS,CAACE,MAA7B,CAAL,EAA2C;EACzCwC,IAAAA,MAAM,GAAG5C,CAAC,CAAC4C,MAAD,CAAD,CAAUxB,OAAV,CAAkBd,QAAQ,CAACF,MAA3B,CAAT;EACD;;EAEDS,EAAAA,MAAM,CAACuB,gBAAP,CAAwBW,IAAxB,CAA6B/C,CAAC,CAAC4C,MAAD,CAA9B,EAAwC,QAAxC;EACD,CAXH,EAYGH,EAZH,CAYM/B,KAAK,CAACE,mBAZZ,EAYiCN,QAAQ,CAACC,kBAZ1C,EAY8D,UAACmC,KAAD,EAAW;EACrE,MAAME,MAAM,GAAG5C,CAAC,CAAC0C,KAAK,CAACG,MAAP,CAAD,CAAgBzB,OAAhB,CAAwBd,QAAQ,CAACF,MAAjC,EAAyC,CAAzC,CAAf;EACAJ,EAAAA,CAAC,CAAC4C,MAAD,CAAD,CAAUX,WAAV,CAAsB/B,SAAS,CAACG,KAAhC,EAAuC,eAAe2C,IAAf,CAAoBN,KAAK,CAACnB,IAA1B,CAAvC;EACD,CAfH;EAiBA;;;;;;EAMAvB,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAamB,MAAM,CAACuB,gBAApB;EACApC,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWuD,WAAX,GAAyBpC,MAAzB;;EACAb,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWwD,UAAX,GAAwB,YAAM;EAC5BlD,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOc,MAAM,CAACuB,gBAAd;EACD,CAHD;;;;;;;;"}