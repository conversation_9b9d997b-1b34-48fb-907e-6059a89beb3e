#!/usr/bin/env python3
"""
JavaScript CSP问题检查工具
检查JS文件中的CSP违规代码，如eval()、Function构造器等
"""

import os
import re
from pathlib import Path

class JSCSPChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.js_dirs = [
            self.project_root / "app" / "static" / "js",
        ]
        self.issues = []
        
        # CSP违规模式
        self.violation_patterns = [
            # eval() 调用
            (r'\beval\s*\(', 'eval() 调用', 'high'),
            # Function 构造器
            (r'\bnew\s+Function\s*\(', 'Function构造器', 'high'),
            # setTimeout/setInterval 字符串参数
            (r'\bsetTimeout\s*\(\s*["\']', 'setTimeout字符串参数', 'medium'),
            (r'\bsetInterval\s*\(\s*["\']', 'setInterval字符串参数', 'medium'),
            # document.write
            (r'\bdocument\.write\s*\(', 'document.write调用', 'medium'),
            # innerHTML 赋值（可能的XSS）
            (r'\.innerHTML\s*=\s*[^;]+[+]', 'innerHTML动态赋值', 'low'),
            # 内联事件处理器
            (r'\.onclick\s*=', '内联onclick赋值', 'low'),
            (r'\.onload\s*=', '内联onload赋值', 'low'),
        ]
    
    def find_js_files(self):
        """查找所有JavaScript文件"""
        js_files = []
        for js_dir in self.js_dirs:
            if js_dir.exists():
                for file_path in js_dir.rglob("*.js"):
                    js_files.append(file_path)
        return js_files
    
    def check_file(self, file_path):
        """检查单个JS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_issues = []
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern, description, severity in self.violation_patterns:
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    for match in matches:
                        file_issues.append({
                            'file': file_path,
                            'line': line_num,
                            'column': match.start() + 1,
                            'pattern': pattern,
                            'description': description,
                            'severity': severity,
                            'code': line.strip(),
                            'match': match.group(0)
                        })
            
            return file_issues
            
        except Exception as e:
            print(f"❌ 检查文件 {file_path} 时出错: {str(e)}")
            return []
    
    def generate_fixes(self, issue):
        """为问题生成修复建议"""
        fixes = []
        
        if 'eval()' in issue['description']:
            fixes.append("使用JSON.parse()替代eval()解析JSON")
            fixes.append("使用Function构造器替代eval()执行代码")
            fixes.append("重构代码避免动态执行")
        
        elif 'Function构造器' in issue['description']:
            fixes.append("使用事件监听器替代动态函数创建")
            fixes.append("预定义函数并通过名称调用")
            fixes.append("使用配置对象替代动态代码")
        
        elif 'setTimeout字符串' in issue['description']:
            fixes.append("使用函数引用替代字符串参数")
            fixes.append("setTimeout(() => { ... }, delay)")
        
        elif 'innerHTML动态' in issue['description']:
            fixes.append("使用textContent替代innerHTML")
            fixes.append("使用DOM API创建元素")
            fixes.append("使用模板引擎进行安全渲染")
        
        return fixes
    
    def run(self):
        """运行检查"""
        print("🔍 JavaScript CSP违规检查工具")
        print("=" * 50)
        
        # 查找JS文件
        js_files = self.find_js_files()
        print(f"📁 找到 {len(js_files)} 个JavaScript文件")
        
        # 检查每个文件
        all_issues = []
        for file_path in js_files:
            file_issues = self.check_file(file_path)
            if file_issues:
                all_issues.extend(file_issues)
                rel_path = file_path.relative_to(self.project_root)
                print(f"⚠️  {rel_path}: {len(file_issues)} 个问题")
        
        # 按严重程度分类
        high_issues = [i for i in all_issues if i['severity'] == 'high']
        medium_issues = [i for i in all_issues if i['severity'] == 'medium']
        low_issues = [i for i in all_issues if i['severity'] == 'low']
        
        # 输出结果
        print("\n" + "=" * 50)
        print("📊 检查结果")
        print(f"🔴 高危问题: {len(high_issues)} 个")
        print(f"🟡 中危问题: {len(medium_issues)} 个")
        print(f"🟢 低危问题: {len(low_issues)} 个")
        print(f"📈 总计: {len(all_issues)} 个问题")
        
        # 详细报告
        if high_issues:
            print(f"\n🔴 高危问题详情:")
            for issue in high_issues:
                rel_path = issue['file'].relative_to(self.project_root)
                print(f"  📄 {rel_path}:{issue['line']}:{issue['column']}")
                print(f"     {issue['description']}: {issue['match']}")
                print(f"     代码: {issue['code']}")
                
                fixes = self.generate_fixes(issue)
                if fixes:
                    print(f"     💡 修复建议:")
                    for fix in fixes[:2]:  # 只显示前2个建议
                        print(f"       - {fix}")
                print()
        
        if medium_issues:
            print(f"\n🟡 中危问题详情:")
            for issue in medium_issues[:5]:  # 只显示前5个
                rel_path = issue['file'].relative_to(self.project_root)
                print(f"  📄 {rel_path}:{issue['line']}:{issue['column']}")
                print(f"     {issue['description']}: {issue['match']}")
                print(f"     代码: {issue['code']}")
                print()
        
        # 生成修复脚本
        if all_issues:
            self.generate_fix_script(all_issues)
        
        return len(all_issues)
    
    def generate_fix_script(self, issues):
        """生成修复脚本"""
        print(f"\n🔧 修复建议:")
        print(f"1. 立即修复所有eval()调用 - 这是最严重的CSP违规")
        print(f"2. 替换Function构造器为安全的替代方案")
        print(f"3. 更新CSP策略移除'unsafe-eval'")
        print(f"4. 使用nonce或hash为合法脚本添加白名单")
        
        # 按文件分组问题
        files_with_issues = {}
        for issue in issues:
            file_path = issue['file']
            if file_path not in files_with_issues:
                files_with_issues[file_path] = []
            files_with_issues[file_path].append(issue)
        
        print(f"\n📋 需要修复的文件:")
        for file_path, file_issues in files_with_issues.items():
            rel_path = file_path.relative_to(self.project_root)
            high_count = len([i for i in file_issues if i['severity'] == 'high'])
            print(f"  - {rel_path} ({len(file_issues)} 个问题, {high_count} 个高危)")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='JavaScript CSP违规检查工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    
    args = parser.parse_args()
    
    checker = JSCSPChecker(args.project_root)
    issue_count = checker.run()
    
    if issue_count > 0:
        print(f"\n⚠️  发现 {issue_count} 个CSP相关问题，建议立即修复！")
        exit(1)
    else:
        print(f"\n✅ 未发现CSP违规问题！")
        exit(0)

if __name__ == "__main__":
    main()
