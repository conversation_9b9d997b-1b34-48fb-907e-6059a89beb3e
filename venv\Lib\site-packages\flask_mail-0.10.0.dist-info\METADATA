Metadata-Version: 2.1
Name: Flask-Mail
Version: 0.10.0
Summary: Flask extension for sending email
Author: <PERSON>-email: Pallets Ecosystem <<EMAIL>>
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Classifier: Development Status :: 4 - Beta
Classifier: Framework :: Flask
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Typing :: Typed
Requires-Dist: flask
Requires-Dist: blinker
Project-URL: Changes, https://flask-mail.readthedocs.io/en/latest/changes/
Project-URL: Chat, https://discord.gg/pallets
Project-URL: Documentation, https://flask-mail.readthedocs.io
Project-URL: Source, https://github.com/pallets-eco/flask-mail/

# Flask-Mail

Flask-Mail is an extension for [Flask] that makes it easy to send emails from
your application. It simplifies the process of integrating email functionality,
allowing you to focus on building great features for your application.

[flask]: https://flask.palletsprojects.com


## Pallets Community Ecosystem

> [!IMPORTANT]\
> This project is part of the Pallets Community Ecosystem. Pallets is the open
> source organization that maintains Flask; Pallets-Eco enables community
> maintenance of related projects. If you are interested in helping maintain
> this project, please reach out on [the Pallets Discord server][discord].

[discord]: https://discord.gg/pallets


## A Simple Example

```python
from flask import Flask
from flask_mail import Mail, Message

app = Flask(__name__)
app.config['MAIL_SERVER'] = 'your_mail_server'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USE_SSL'] = False
app.config['MAIL_USERNAME'] = 'your_username'
app.config['MAIL_PASSWORD'] = 'your_password'
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'

mail = Mail(app)

@app.route('/')
def send_email():
  msg = Message(
    'Hello',
    recipients=['<EMAIL>'],
    body='This is a test email sent from Flask-Mail!'
  )
  mail.send(msg)
  return 'Email sent succesfully!'
```

