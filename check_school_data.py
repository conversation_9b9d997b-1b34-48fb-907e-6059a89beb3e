#!/usr/bin/env python3
"""
查询城南小学的数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import AdministrativeArea, Recipe, WeeklyMenu, WeeklyMenuRecipe, User
from datetime import date, datetime
from sqlalchemy import text

def check_school_data():
    app = create_app()
    
    with app.app_context():
        print("=== 城南小学数据查询 ===\n")
        
        # 1. 查找城南小学
        print("1. 查找城南小学:")
        schools = AdministrativeArea.query.filter(
            AdministrativeArea.name.like('%城南小学%')
        ).all()
        
        if not schools:
            print("   未找到城南小学，查找所有学校:")
            all_schools = AdministrativeArea.query.filter(
                AdministrativeArea.area_type == '学校'
            ).all()
            for school in all_schools:
                print(f"   - ID: {school.id}, 名称: {school.name}")
            return
        
        for school in schools:
            print(f"   找到学校: ID={school.id}, 名称={school.name}")
            area_id = school.id
            
            # 2. 查看该学校的用户
            print(f"\n2. 学校 {school.name} 的用户:")
            users = User.query.filter_by(area_id=area_id).all()
            print(f"   用户数量: {len(users)}")
            for user in users:
                print(f"   - ID: {user.id}, 用户名: {user.username}, 真实姓名: {user.real_name}")
            
            # 3. 查看今日菜单计划 - 日菜单功能已移除
            today = date.today()
            print(f"\n3. 今日菜单计划 ({today}): 日菜单功能已移除，请查看周菜单")
            
            # 4. 查看周菜单
            print(f"\n4. 周菜单数据:")
            
            # 查找包含今天的周菜单
            weekly_menus = WeeklyMenu.query.filter(
                WeeklyMenu.area_id == area_id,
                WeeklyMenu.week_start <= today,
                WeeklyMenu.week_end >= today
            ).all()
            
            print(f"   包含今天的周菜单数量: {len(weekly_menus)}")
            
            for wm in weekly_menus:
                print(f"   - 周菜单ID: {wm.id}, 周期: {wm.week_start} ~ {wm.week_end}, 状态: {wm.status}")
                
                # 查看今天的菜谱
                day_of_week = today.weekday() + 1
                weekly_recipes = WeeklyMenuRecipe.query.filter_by(
                    weekly_menu_id=wm.id,
                    day_of_week=day_of_week
                ).all()
                
                print(f"     今天({day_of_week})的菜谱数量: {len(weekly_recipes)}")
                for wr in weekly_recipes:
                    print(f"     - 餐次: {wr.meal_type}, 菜谱: {wr.recipe_name}")
            
            # 5. 查看所有周菜单（不限日期）
            print(f"\n5. 所有周菜单:")
            all_weekly_menus = WeeklyMenu.query.filter_by(area_id=area_id).all()
            print(f"   总周菜单数量: {len(all_weekly_menus)}")
            
            for wm in all_weekly_menus:
                print(f"   - ID: {wm.id}, 周期: {wm.week_start} ~ {wm.week_end}, 状态: {wm.status}")
                
                # 统计该周菜单的菜谱数量
                recipe_count = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=wm.id).count()
                print(f"     菜谱总数: {recipe_count}")
            
            # 6. 查看所有日菜单计划（不限日期） - 日菜单功能已移除
            print(f"\n6. 所有日菜单计划: 日菜单功能已移除")

if __name__ == '__main__':
    check_school_data()
