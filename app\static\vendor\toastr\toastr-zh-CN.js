/**
 * Toastr 中文本地化
 */
(function($) {
    'use strict';
    
    if (typeof toastr !== 'undefined') {
        // 设置默认选项
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            // 中文本地化
            "closeHtml": '<button type="button">&times;</button>',
            "titleClass": "toast-title",
            "messageClass": "toast-message",
            "target": "body",
            "closeClass": "toast-close-button",
            "containerId": "toast-container",
            "iconClasses": {
                "error": "toast-error",
                "info": "toast-info",
                "success": "toast-success",
                "warning": "toast-warning"
            },
            "toastClass": "toast"
        };
        
        // 扩展 toastr 方法，添加中文提示
        var originalSuccess = toastr.success;
        var originalInfo = toastr.info;
        var originalWarning = toastr.warning;
        var originalError = toastr.error;
        
        // 重写 success 方法
        toastr.success = function(message, title, options) {
            if (!title) title = "成功";
            return originalSuccess.call(this, message, title, options);
        };
        
        // 重写 info 方法
        toastr.info = function(message, title, options) {
            if (!title) title = "信息";
            return originalInfo.call(this, message, title, options);
        };
        
        // 重写 warning 方法
        toastr.warning = function(message, title, options) {
            if (!title) title = "警告";
            return originalWarning.call(this, message, title, options);
        };
        
        // 重写 error 方法
        toastr.error = function(message, title, options) {
            if (!title) title = "错误";
            return originalError.call(this, message, title, options);
        };
        
        // 添加自定义方法
        toastr.confirm = function(message, title, okCallback, cancelCallback, options) {
            if (!title) title = "确认";
            
            var defaultOptions = {
                "closeButton": true,
                "timeOut": 0,
                "extendedTimeOut": 0,
                "tapToDismiss": false,
                "positionClass": "toast-top-center"
            };
            
            options = $.extend(defaultOptions, options || {});
            
            var $toast = originalInfo.call(this, message, title, options);
            
            if ($toast) {
                var $toastContent = $toast.find('.toast-message');
                var $buttonContainer = $('<div class="toast-buttons"></div>');
                var $okButton = $('<button class="btn btn-primary btn-sm mr-2">确定</button>');
                var $cancelButton = $('<button class="btn btn-secondary btn-sm">取消</button>');
                
                $buttonContainer.append($okButton).append($cancelButton);
                $toastContent.after($buttonContainer);
                
                $okButton.on('click', function() {
                    if (typeof okCallback === 'function') {
                        okCallback();
                    }
                    toastr.clear($toast);
                });
                
                $cancelButton.on('click', function() {
                    if (typeof cancelCallback === 'function') {
                        cancelCallback();
                    }
                    toastr.clear($toast);
                });
            }
            
            return $toast;
        };
    }
})(jQuery);
