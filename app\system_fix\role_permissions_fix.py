"""
修复角色权限编辑页面的问题
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Role
from app.utils.decorators import admin_required
from app.utils.permissions import get_permission_list, parse_permissions_json, format_permissions_json
import json

role_permissions_fix_bp = Blueprint('role_permissions_fix', __name__)

@role_permissions_fix_bp.route('/fix-role-permissions/<int:role_id>')
@login_required
@admin_required
def fix_role_permissions(role_id):
    """修复角色权限"""
    try:
        # 获取角色
        role = Role.query.get_or_404(role_id)
        
        # 获取所有权限
        all_permissions = get_permission_list()
        
        # 解析当前角色的权限
        current_permissions = parse_permissions_json(role.permissions)
        
        # 确保权限格式正确
        if not isinstance(current_permissions, dict):
            current_permissions = {}
        
        # 确保采购管理模块的权限存在
        if 'purchase' not in current_permissions:
            current_permissions['purchase'] = []
        
        # 确保采购管理模块的基本权限
        basic_permissions = ['view', 'create', 'edit']
        for perm in basic_permissions:
            if perm not in current_permissions['purchase']:
                current_permissions['purchase'].append(perm)
        
        # 更新角色权限
        permissions_json = format_permissions_json(current_permissions)
        
        # 使用原始SQL更新角色权限，避免精度问题
        from sqlalchemy import text
        sql = text("""
        UPDATE roles
        SET permissions = :permissions
        WHERE id = :id
        """)
        
        db.session.execute(
            sql,
            {
                'permissions': permissions_json,
                'id': role.id
            }
        )
        db.session.commit()
        
        flash(f'角色 {role.name} 的权限已修复', 'success')
        return redirect(url_for('system.view_role', id=role.id))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修复角色权限时出错: {str(e)}")
        flash(f'修复角色权限时出错: {str(e)}', 'danger')
        return redirect(url_for('system.roles'))

@role_permissions_fix_bp.route('/fix-all-role-permissions')
@login_required
@admin_required
def fix_all_role_permissions():
    """修复所有角色权限"""
    try:
        # 获取所有角色
        roles = Role.query.all()
        
        fixed_count = 0
        error_count = 0
        
        for role in roles:
            try:
                # 解析当前角色的权限
                current_permissions = parse_permissions_json(role.permissions)
                
                # 确保权限格式正确
                if not isinstance(current_permissions, dict):
                    current_permissions = {}
                
                # 确保采购管理模块的权限存在
                if 'purchase' not in current_permissions:
                    current_permissions['purchase'] = []
                
                # 确保采购管理模块的基本权限
                basic_permissions = ['view', 'create', 'edit']
                for perm in basic_permissions:
                    if perm not in current_permissions['purchase']:
                        current_permissions['purchase'].append(perm)
                
                # 更新角色权限
                permissions_json = format_permissions_json(current_permissions)
                
                # 使用原始SQL更新角色权限，避免精度问题
                from sqlalchemy import text
                sql = text("""
                UPDATE roles
                SET permissions = :permissions
                WHERE id = :id
                """)
                
                db.session.execute(
                    sql,
                    {
                        'permissions': permissions_json,
                        'id': role.id
                    }
                )
                db.session.commit()
                
                fixed_count += 1
            except Exception as e:
                error_count += 1
                current_app.logger.error(f"修复角色 {role.name} 权限时出错: {str(e)}")
        
        if fixed_count > 0:
            flash(f'成功修复 {fixed_count} 个角色的权限', 'success')
        
        if error_count > 0:
            flash(f'有 {error_count} 个角色权限修复失败，详情请查看日志', 'warning')
        
        return redirect(url_for('system.roles'))
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修复所有角色权限时出错: {str(e)}")
        flash(f'修复所有角色权限时出错: {str(e)}', 'danger')
        return redirect(url_for('system.roles'))
