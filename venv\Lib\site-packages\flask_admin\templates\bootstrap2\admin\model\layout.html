{% macro filter_options(btn_class='dropdown-toggle') %}
    <a class="{{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)">
        {{ _gettext('Add Filter') }}<b class="caret"></b>
    </a>
    <ul class="dropdown-menu field-filters">
        {% for k in filter_groups %}
        <li>
            <a href="javascript:void(0)" class="filter" onclick="return false;">{{ k }}</a>
        </li>
        {% endfor %}
    </ul>
{% endmacro %}

{% macro export_options(btn_class='dropdown-toggle') %}
    {% if admin_view.export_types|length > 1 %}
    <li class="dropdown">
        <a class="{{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)">
            {{ _gettext('Export') }}<b class="caret"></b>
        </a>
        <ul class="dropdown-menu field-filters">
            {% for export_type in admin_view.export_types %}
            <li>
                <a href="{{ get_url('.export', export_type=export_type, **request.args) }}" title="{{ _gettext('Export') }}">{{ _gettext('Export') + ' ' + export_type|upper }}</a>
            </li>
            {% endfor %}
        </ul>
    </li>
    {% else %}
    <li>
        <a href="{{ get_url('.export', export_type=admin_view.export_types[0], **request.args) }}" title="{{ _gettext('Export') }}">{{ _gettext('Export') }}</a>
    </li>
    {% endif %}
{% endmacro %}

{% macro filter_form() %}
    <form id="filter_form" method="GET" action="{{ return_url }}">
        {% for arg_name, arg_value in extra_args.items() %}
        <input type="hidden" name="{{ arg_name }}" value="{{ arg_value }}">
        {% endfor %}
        {% if sort_column is not none %}
        <input type="hidden" name="sort" value="{{ sort_column }}">
        {% endif %}
        {% if sort_desc %}
        <input type="hidden" name="desc" value="{{ sort_desc }}">
        {% endif %}
        {% if search %}
        <input type="hidden" name="search" value="{{ search }}">
        {% endif %}
        {% if page_size != default_page_size %}
        <input type="hidden" name="page_size" value="{{ page_size }}">
        {% endif %}
        <div class="pull-right">
            <button type="submit" class="btn btn-primary" style="display: none">{{ _gettext('Apply') }}</button>
            {% if active_filters %}
            <a href="{{ clear_search_url }}" class="btn">{{ _gettext('Reset Filters') }}</a>
            {% endif %}
        </div>

        <table class="filters"></table>
    </form>
    <div class="clearfix"></div>
{% endmacro %}

{% macro search_form(input_class=None) %}
<form method="GET" action="{{ return_url }}" class="search-form">
    {% for flt_name, flt_value in filter_args.items() %}
    <input type="hidden" name="{{ flt_name }}" value="{{ flt_value }}">
    {% endfor %}
    {% for arg_name, arg_value in extra_args.items() %}
    <input type="hidden" name="{{ arg_name }}" value="{{ arg_value }}">
    {% endfor %}
    {% if page_size != default_page_size %}
    <input type="hidden" name="page_size" value="{{ page_size }}">
    {% endif %}
    {% if sort_column is not none %}
    <input type="hidden" name="sort" value="{{ sort_column }}">
    {% endif %}
    {% if sort_desc %}
    <input type="hidden" name="desc" value="{{ sort_desc }}">
    {% endif %}
    {%- set full_search_placeholder = _gettext('Search') %}
    {%- if search_placeholder %}{% set full_search_placeholder = [full_search_placeholder, search_placeholder] | join(": ") %}{% endif %}
    {% if search %}
    <div class="input-append">
            <input type="search" name="search" class="input-xlarge{% if input_class %} {{ input_class }}{% endif %}" value="{{ search }}" placeholder="{{ full_search_placeholder }}">
            <a href="{{ clear_search_url }}" class="clear add-on">
                <i class="fa fa-times icon-remove"></i>
            </a>
    </div>
    {% else %}
    <input type="search" name="search" class="input-xlarge{% if input_class %} {{ input_class }}{% endif %}" value="" placeholder="{{ full_search_placeholder }}">
    {% endif %}
</form>
{% endmacro %}

{% macro page_size_form(generator, btn_class='dropdown-toggle') %}
    <a class="{{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)">
        {{ page_size }} {{ _gettext('items') }}<b class="caret"></b>
    </a>
    <ul class="dropdown-menu">
        <li><a href="{{ generator(20) }}">20 {{ _gettext('items') }}</a></li>
        <li><a href="{{ generator(50) }}">50 {{ _gettext('items') }}</a></li>
        <li><a href="{{ generator(100) }}">100 {{ _gettext('items') }}</a></li>
    </ul>
{% endmacro %}
