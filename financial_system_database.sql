-- =============================================
-- 学校食堂财务系统数据库设计
-- 以入库单为核心的财务核算体系
-- 创建日期: 2024年
-- =============================================

USE [StudentsCMSSP]
GO

PRINT '开始创建财务系统数据库表...'
PRINT '========================================'

-- =============================================
-- 第一部分：修改现有表，添加财务相关字段
-- =============================================

PRINT '第一部分：修改现有表结构...'

-- 1. 修改采购订单表：添加预算控制字段
PRINT '1. 修改采购订单表...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('purchase_orders') AND name = 'budget_amount')
BEGIN
    ALTER TABLE purchase_orders ADD budget_amount DECIMAL(12, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加预算金额字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('purchase_orders') AND name = 'committed_amount')
BEGIN
    ALTER TABLE purchase_orders ADD committed_amount DECIMAL(12, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加承诺金额字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('purchase_orders') AND name = 'is_budget_approved')
BEGIN
    ALTER TABLE purchase_orders ADD is_budget_approved BIT DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加预算审批字段'
END

-- 2. 修改入库单表：添加财务确认字段
PRINT '2. 修改入库单表...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_ins ADD total_cost DECIMAL(12, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加入库总成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
BEGIN
    ALTER TABLE stock_ins ADD is_financial_confirmed BIT DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加财务确认标志字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_at')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
    PRINT '  ✓ 添加财务确认时间字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
    PRINT '  ✓ 添加财务确认人字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
BEGIN
    ALTER TABLE stock_ins ADD voucher_id INT NULL;
    PRINT '  ✓ 添加关联凭证字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
BEGIN
    ALTER TABLE stock_ins ADD payable_id INT NULL;
    PRINT '  ✓ 添加关联应付账款字段'
END

-- 3. 修改入库明细表：添加成本字段
PRINT '3. 修改入库明细表...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'unit_cost')
BEGIN
    ALTER TABLE stock_in_items ADD unit_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加单位成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_in_items ADD total_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加总成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'freight_cost')
BEGIN
    ALTER TABLE stock_in_items ADD freight_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加运费分摊字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_in_items') AND name = 'other_cost')
BEGIN
    ALTER TABLE stock_in_items ADD other_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加其他费用分摊字段'
END

-- 4. 修改出库明细表：添加成本字段
PRINT '4. 修改出库明细表...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'unit_cost')
BEGIN
    ALTER TABLE stock_out_items ADD unit_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加出库单位成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_out_items ADD total_cost DECIMAL(10, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加出库总成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_out_items') AND name = 'cost_method')
BEGIN
    ALTER TABLE stock_out_items ADD cost_method NVARCHAR(20) DEFAULT '加权平均' NOT NULL;
    PRINT '  ✓ 添加成本计算方法字段'
END

-- 5. 修改库存表：添加成本管理字段
PRINT '5. 修改库存表...'
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inventories') AND name = 'weighted_avg_cost')
BEGIN
    ALTER TABLE inventories ADD weighted_avg_cost DECIMAL(10, 4) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加加权平均成本字段'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inventories') AND name = 'total_cost')
BEGIN
    ALTER TABLE inventories ADD total_cost DECIMAL(12, 2) DEFAULT 0 NOT NULL;
    PRINT '  ✓ 添加库存总成本字段'
END

PRINT '现有表结构修改完成！'
PRINT ''

-- =============================================
-- 第二部分：创建财务系统核心表
-- =============================================

PRINT '第二部分：创建财务系统核心表...'

-- 1. 会计科目表
PRINT '1. 创建会计科目表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[accounting_subjects]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[accounting_subjects](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [code] [nvarchar](20) NOT NULL,
        [name] [nvarchar](100) NOT NULL,
        [parent_id] [int] NULL,
        [level] [int] NOT NULL DEFAULT 1,
        [subject_type] [nvarchar](20) NOT NULL,
        [balance_direction] [nvarchar](10) NOT NULL,
        [area_id] [int] NOT NULL,
        [is_system] [bit] NOT NULL DEFAULT 0,
        [is_active] [bit] NOT NULL DEFAULT 1,
        [description] [nvarchar](500) NULL,
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_accounting_subjects] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_accounting_subjects_parent] FOREIGN KEY([parent_id]) REFERENCES [dbo].[accounting_subjects] ([id]),
        CONSTRAINT [FK_accounting_subjects_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_accounting_subjects_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_accounting_subjects_area_code] UNIQUE([area_id], [code])
    )
    PRINT '  ✓ 会计科目表创建成功'
END
ELSE
BEGIN
    PRINT '  - 会计科目表已存在'
END

-- 2. 财务凭证表
PRINT '2. 创建财务凭证表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[financial_vouchers]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[financial_vouchers](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [voucher_number] [nvarchar](20) NOT NULL,
        [voucher_date] [date] NOT NULL,
        [area_id] [int] NOT NULL,
        [voucher_type] [nvarchar](50) NOT NULL,
        [summary] [nvarchar](200) NULL,
        [total_amount] [decimal](12, 2) NOT NULL DEFAULT 0,
        [status] [nvarchar](20) NOT NULL DEFAULT '草稿',
        [source_type] [nvarchar](50) NULL,
        [source_id] [int] NULL,
        [attachment_count] [int] NOT NULL DEFAULT 0,
        [created_by] [int] NOT NULL,
        [reviewed_by] [int] NULL,
        [reviewed_at] [datetime2](1) NULL,
        [posted_by] [int] NULL,
        [posted_at] [datetime2](1) NULL,
        [notes] [nvarchar](max) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_financial_vouchers] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_financial_vouchers_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_financial_vouchers_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_financial_vouchers_reviewer] FOREIGN KEY([reviewed_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_financial_vouchers_poster] FOREIGN KEY([posted_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_financial_vouchers_area_number] UNIQUE([area_id], [voucher_number])
    )
    PRINT '  ✓ 财务凭证表创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务凭证表已存在'
END

-- 3. 凭证明细表
PRINT '3. 创建凭证明细表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[voucher_details]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[voucher_details](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [voucher_id] [int] NOT NULL,
        [line_number] [int] NOT NULL,
        [subject_id] [int] NOT NULL,
        [summary] [nvarchar](200) NOT NULL,
        [debit_amount] [decimal](12, 2) NOT NULL DEFAULT 0,
        [credit_amount] [decimal](12, 2) NOT NULL DEFAULT 0,
        [auxiliary_info] [nvarchar](200) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_voucher_details] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_voucher_details_voucher] FOREIGN KEY([voucher_id]) REFERENCES [dbo].[financial_vouchers] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_voucher_details_subject] FOREIGN KEY([subject_id]) REFERENCES [dbo].[accounting_subjects] ([id])
    )
    PRINT '  ✓ 凭证明细表创建成功'
END
ELSE
BEGIN
    PRINT '  - 凭证明细表已存在'
END

-- 4. 应付账款表（基于入库单）
PRINT '4. 创建应付账款表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[account_payables]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[account_payables](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [payable_number] [nvarchar](50) NOT NULL,
        [area_id] [int] NOT NULL,
        [supplier_id] [int] NOT NULL,
        [stock_in_id] [int] NOT NULL,
        [purchase_order_id] [int] NULL,
        [original_amount] [decimal](12, 2) NOT NULL,
        [paid_amount] [decimal](12, 2) NOT NULL DEFAULT 0,
        [balance_amount] [decimal](12, 2) NOT NULL,
        [due_date] [date] NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '未付款',
        [payment_terms] [nvarchar](200) NULL,
        [invoice_number] [nvarchar](50) NULL,
        [invoice_date] [date] NULL,
        [invoice_amount] [decimal](12, 2) NULL,
        [created_by] [int] NOT NULL,
        [notes] [nvarchar](max) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_account_payables] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_account_payables_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_account_payables_supplier] FOREIGN KEY([supplier_id]) REFERENCES [dbo].[suppliers] ([id]),
        CONSTRAINT [FK_account_payables_stock_in] FOREIGN KEY([stock_in_id]) REFERENCES [dbo].[stock_ins] ([id]),
        CONSTRAINT [FK_account_payables_purchase_order] FOREIGN KEY([purchase_order_id]) REFERENCES [dbo].[purchase_orders] ([id]),
        CONSTRAINT [FK_account_payables_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_account_payables_area_number] UNIQUE([area_id], [payable_number])
    )
    PRINT '  ✓ 应付账款表创建成功'
END
ELSE
BEGIN
    PRINT '  - 应付账款表已存在'
END

-- 5. 付款记录表
PRINT '5. 创建付款记录表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[payment_records]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[payment_records](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [payment_number] [nvarchar](50) NOT NULL,
        [area_id] [int] NOT NULL,
        [payment_date] [date] NOT NULL,
        [amount] [decimal](12, 2) NOT NULL,
        [payment_method] [nvarchar](30) NOT NULL,
        [payable_id] [int] NOT NULL,
        [supplier_id] [int] NOT NULL,
        [bank_account] [nvarchar](50) NULL,
        [reference_number] [nvarchar](50) NULL,
        [voucher_id] [int] NULL,
        [summary] [nvarchar](200) NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '已确认',
        [created_by] [int] NOT NULL,
        [reviewed_by] [int] NULL,
        [reviewed_at] [datetime2](1) NULL,
        [notes] [nvarchar](max) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_payment_records] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_payment_records_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_payment_records_payable] FOREIGN KEY([payable_id]) REFERENCES [dbo].[account_payables] ([id]),
        CONSTRAINT [FK_payment_records_supplier] FOREIGN KEY([supplier_id]) REFERENCES [dbo].[suppliers] ([id]),
        CONSTRAINT [FK_payment_records_voucher] FOREIGN KEY([voucher_id]) REFERENCES [dbo].[financial_vouchers] ([id]),
        CONSTRAINT [FK_payment_records_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_payment_records_reviewer] FOREIGN KEY([reviewed_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_payment_records_area_number] UNIQUE([area_id], [payment_number])
    )
    PRINT '  ✓ 付款记录表创建成功'
END
ELSE
BEGIN
    PRINT '  - 付款记录表已存在'
END

-- 6. 库存成本核算表
PRINT '6. 创建库存成本核算表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[inventory_cost_records]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[inventory_cost_records](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [ingredient_id] [int] NOT NULL,
        [warehouse_id] [int] NOT NULL,
        [calculation_date] [date] NOT NULL,
        [beginning_quantity] [decimal](10, 3) NOT NULL DEFAULT 0,
        [beginning_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [in_quantity] [decimal](10, 3) NOT NULL DEFAULT 0,
        [in_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [out_quantity] [decimal](10, 3) NOT NULL DEFAULT 0,
        [out_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [ending_quantity] [decimal](10, 3) NOT NULL DEFAULT 0,
        [ending_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [weighted_avg_cost] [decimal](10, 4) NOT NULL DEFAULT 0,
        [cost_method] [nvarchar](20) NOT NULL DEFAULT '加权平均',
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_inventory_cost_records] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_inventory_cost_records_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_inventory_cost_records_ingredient] FOREIGN KEY([ingredient_id]) REFERENCES [dbo].[ingredients] ([id]),
        CONSTRAINT [FK_inventory_cost_records_warehouse] FOREIGN KEY([warehouse_id]) REFERENCES [dbo].[warehouses] ([id]),
        CONSTRAINT [FK_inventory_cost_records_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_inventory_cost_records_unique] UNIQUE([area_id], [ingredient_id], [warehouse_id], [calculation_date])
    )
    PRINT '  ✓ 库存成本核算表创建成功'
END
ELSE
BEGIN
    PRINT '  - 库存成本核算表已存在'
END

-- 7. 消耗成本核算表
PRINT '7. 创建消耗成本核算表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[consumption_cost_records]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[consumption_cost_records](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [consumption_plan_id] [int] NOT NULL,
        [stock_out_id] [int] NOT NULL,
        [consumption_date] [date] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [total_material_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [total_labor_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [total_overhead_cost] [decimal](12, 2) NOT NULL DEFAULT 0,
        [total_cost] [decimal](12, 2) NOT NULL,
        [serving_count] [int] NULL,
        [cost_per_serving] [decimal](10, 4) NULL,
        [voucher_id] [int] NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '已计算',
        [calculated_by] [int] NOT NULL,
        [reviewed_by] [int] NULL,
        [reviewed_at] [datetime2](1) NULL,
        [notes] [nvarchar](max) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_consumption_cost_records] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_consumption_cost_records_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_consumption_cost_records_consumption_plan] FOREIGN KEY([consumption_plan_id]) REFERENCES [dbo].[consumption_plans] ([id]),
        CONSTRAINT [FK_consumption_cost_records_stock_out] FOREIGN KEY([stock_out_id]) REFERENCES [dbo].[stock_outs] ([id]),
        CONSTRAINT [FK_consumption_cost_records_voucher] FOREIGN KEY([voucher_id]) REFERENCES [dbo].[financial_vouchers] ([id]),
        CONSTRAINT [FK_consumption_cost_records_calculator] FOREIGN KEY([calculated_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_consumption_cost_records_reviewer] FOREIGN KEY([reviewed_by]) REFERENCES [dbo].[users] ([id])
    )
    PRINT '  ✓ 消耗成本核算表创建成功'
END
ELSE
BEGIN
    PRINT '  - 消耗成本核算表已存在'
END

-- 8. 收入记录表
PRINT '8. 创建收入记录表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[income_records]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[income_records](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [income_number] [nvarchar](50) NOT NULL,
        [area_id] [int] NOT NULL,
        [income_date] [date] NOT NULL,
        [income_type] [nvarchar](30) NOT NULL,
        [amount] [decimal](12, 2) NOT NULL,
        [payment_method] [nvarchar](30) NOT NULL,
        [customer_type] [nvarchar](30) NULL,
        [meal_type] [nvarchar](20) NULL,
        [serving_count] [int] NULL,
        [voucher_id] [int] NULL,
        [summary] [nvarchar](200) NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '已确认',
        [created_by] [int] NOT NULL,
        [reviewed_by] [int] NULL,
        [reviewed_at] [datetime2](1) NULL,
        [notes] [nvarchar](max) NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_income_records] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_income_records_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_income_records_voucher] FOREIGN KEY([voucher_id]) REFERENCES [dbo].[financial_vouchers] ([id]),
        CONSTRAINT [FK_income_records_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_income_records_reviewer] FOREIGN KEY([reviewed_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_income_records_area_number] UNIQUE([area_id], [income_number])
    )
    PRINT '  ✓ 收入记录表创建成功'
END
ELSE
BEGIN
    PRINT '  - 收入记录表已存在'
END

-- 9. 财务报表配置表
PRINT '9. 创建财务报表配置表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[financial_report_configs]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[financial_report_configs](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [report_type] [nvarchar](30) NOT NULL,
        [report_name] [nvarchar](100) NOT NULL,
        [config_json] [nvarchar](max) NOT NULL,
        [is_active] [bit] NOT NULL DEFAULT 1,
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NULL,
        CONSTRAINT [PK_financial_report_configs] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_financial_report_configs_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_financial_report_configs_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id])
    )
    PRINT '  ✓ 财务报表配置表创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务报表配置表已存在'
END

-- 10. 财务期间表
PRINT '10. 创建财务期间表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[financial_periods]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[financial_periods](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [period_year] [int] NOT NULL,
        [period_month] [int] NOT NULL,
        [start_date] [date] NOT NULL,
        [end_date] [date] NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '开放',
        [closing_voucher_id] [int] NULL,
        [closed_by] [int] NULL,
        [closed_at] [datetime2](1) NULL,
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_financial_periods] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_financial_periods_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_financial_periods_closing_voucher] FOREIGN KEY([closing_voucher_id]) REFERENCES [dbo].[financial_vouchers] ([id]),
        CONSTRAINT [FK_financial_periods_closer] FOREIGN KEY([closed_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_financial_periods_creator] FOREIGN KEY([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [UQ_financial_periods_area_period] UNIQUE([area_id], [period_year], [period_month])
    )
    PRINT '  ✓ 财务期间表创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务期间表已存在'
END

-- 11. 财务附件表
PRINT '11. 创建财务附件表...'
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[financial_attachments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[financial_attachments](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [reference_type] [nvarchar](30) NOT NULL,
        [reference_id] [int] NOT NULL,
        [file_name] [nvarchar](255) NOT NULL,
        [file_path] [nvarchar](500) NOT NULL,
        [file_size] [int] NOT NULL,
        [file_type] [nvarchar](50) NOT NULL,
        [attachment_type] [nvarchar](30) NOT NULL,
        [description] [nvarchar](200) NULL,
        [uploaded_by] [int] NOT NULL,
        [uploaded_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_financial_attachments] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_financial_attachments_area] FOREIGN KEY([area_id]) REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_financial_attachments_uploader] FOREIGN KEY([uploaded_by]) REFERENCES [dbo].[users] ([id])
    )
    PRINT '  ✓ 财务附件表创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务附件表已存在'
END

PRINT '财务系统核心表创建完成！'
PRINT ''

-- =============================================
-- 第三部分：创建索引优化
-- =============================================

PRINT '第三部分：创建索引优化...'

-- 1. 会计科目表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_accounting_subjects_area_type')
BEGIN
    CREATE NONCLUSTERED INDEX IX_accounting_subjects_area_type
    ON accounting_subjects (area_id, subject_type, is_active)
    INCLUDE (id, code, name, balance_direction)
    PRINT '  ✓ 会计科目表索引创建成功'
END

-- 2. 财务凭证表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_financial_vouchers_area_date')
BEGIN
    CREATE NONCLUSTERED INDEX IX_financial_vouchers_area_date
    ON financial_vouchers (area_id, voucher_date, status)
    INCLUDE (id, voucher_number, voucher_type, total_amount)
    PRINT '  ✓ 财务凭证表索引创建成功'
END

-- 3. 应付账款表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_account_payables_area_status')
BEGIN
    CREATE NONCLUSTERED INDEX IX_account_payables_area_status
    ON account_payables (area_id, status, due_date)
    INCLUDE (id, supplier_id, balance_amount)
    PRINT '  ✓ 应付账款表索引创建成功'
END

-- 4. 库存成本核算表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_inventory_cost_records_area_date')
BEGIN
    CREATE NONCLUSTERED INDEX IX_inventory_cost_records_area_date
    ON inventory_cost_records (area_id, calculation_date, ingredient_id)
    INCLUDE (warehouse_id, ending_quantity, ending_cost, weighted_avg_cost)
    PRINT '  ✓ 库存成本核算表索引创建成功'
END

-- 5. 收入记录表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_income_records_area_date')
BEGIN
    CREATE NONCLUSTERED INDEX IX_income_records_area_date
    ON income_records (area_id, income_date, income_type)
    INCLUDE (amount, payment_method, serving_count)
    PRINT '  ✓ 收入记录表索引创建成功'
END

PRINT '索引优化完成！'
PRINT ''

-- =============================================
-- 第四部分：添加外键约束到现有表
-- =============================================

PRINT '第四部分：添加外键约束...'

-- 1. 入库单表添加外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_financial_confirmed_by
    FOREIGN KEY (financial_confirmed_by) REFERENCES users(id)
    PRINT '  ✓ 入库单财务确认人外键约束添加成功'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_voucher')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_voucher
    FOREIGN KEY (voucher_id) REFERENCES financial_vouchers(id)
    PRINT '  ✓ 入库单凭证外键约束添加成功'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_payable')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_payable
    FOREIGN KEY (payable_id) REFERENCES account_payables(id)
    PRINT '  ✓ 入库单应付账款外键约束添加成功'
END

PRINT '外键约束添加完成！'
PRINT ''

-- =============================================
-- 第五部分：插入基础数据
-- =============================================

PRINT '第五部分：插入基础数据...'

-- 插入系统默认会计科目（仅为示例，实际使用时需要根据具体需求调整）
PRINT '插入系统默认会计科目...'

-- 注意：这里只是示例，实际部署时需要根据每个学校的area_id来插入
-- 可以通过以下查询获取所有学校的area_id：
-- SELECT id, name FROM administrative_areas WHERE level = 3 AND status = 1

PRINT '基础数据插入完成！'
PRINT ''

-- =============================================
-- 第六部分：创建触发器（可选）
-- =============================================

PRINT '第六部分：创建触发器...'

-- 1. 应付账款余额更新触发器
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_payment_records_update_payable_balance')
BEGIN
    EXEC('
    CREATE TRIGGER tr_payment_records_update_payable_balance
    ON payment_records
    AFTER INSERT, UPDATE, DELETE
    AS
    BEGIN
        SET NOCOUNT ON;

        -- 更新相关应付账款的已付金额和余额
        UPDATE ap
        SET paid_amount = ISNULL((
            SELECT SUM(amount)
            FROM payment_records pr
            WHERE pr.payable_id = ap.id AND pr.status = ''已确认''
        ), 0),
        balance_amount = ap.original_amount - ISNULL((
            SELECT SUM(amount)
            FROM payment_records pr
            WHERE pr.payable_id = ap.id AND pr.status = ''已确认''
        ), 0),
        status = CASE
            WHEN ap.original_amount <= ISNULL((
                SELECT SUM(amount)
                FROM payment_records pr
                WHERE pr.payable_id = ap.id AND pr.status = ''已确认''
            ), 0) THEN ''已付款''
            WHEN ISNULL((
                SELECT SUM(amount)
                FROM payment_records pr
                WHERE pr.payable_id = ap.id AND pr.status = ''已确认''
            ), 0) > 0 THEN ''部分付款''
            ELSE ''未付款''
        END
        FROM account_payables ap
        WHERE ap.id IN (
            SELECT DISTINCT payable_id FROM inserted
            UNION
            SELECT DISTINCT payable_id FROM deleted
        );
    END
    ')
    PRINT '  ✓ 应付账款余额更新触发器创建成功'
END

PRINT '触发器创建完成！'
PRINT ''

-- =============================================
-- 完成信息
-- =============================================

PRINT '========================================'
PRINT '财务系统数据库创建完成！'
PRINT ''
PRINT '已创建的表：'
PRINT '1. 修改了现有表的财务字段'
PRINT '2. accounting_subjects - 会计科目表'
PRINT '3. financial_vouchers - 财务凭证表'
PRINT '4. voucher_details - 凭证明细表'
PRINT '5. account_payables - 应付账款表'
PRINT '6. payment_records - 付款记录表'
PRINT '7. inventory_cost_records - 库存成本核算表'
PRINT '8. consumption_cost_records - 消耗成本核算表'
PRINT '9. income_records - 收入记录表'
PRINT '10. financial_report_configs - 财务报表配置表'
PRINT '11. financial_periods - 财务期间表'
PRINT '12. financial_attachments - 财务附件表'
PRINT ''
PRINT '已创建的索引和约束：'
PRINT '- 各表的性能优化索引'
PRINT '- 外键约束'
PRINT '- 唯一约束'
PRINT '- 触发器'
PRINT ''
PRINT '下一步：'
PRINT '1. 根据实际需要为每个学校插入会计科目'
PRINT '2. 配置财务权限'
PRINT '3. 开发财务模块的业务逻辑'
PRINT '========================================'
