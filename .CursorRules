根据 README.md 中的解决方案，我来详细说明如何处理时间的问题：

1. **模型定义层面**：
```python
from sqlalchemy.dialects.mssql import DATETIME2
from datetime import datetime

class StandardModel(db.Model):
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 使用 DATETIME2 类型，precision=1 表示精确到 0.1 秒
    created_at = db.Column(DATETIME2(precision=1), 
                          default=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
    updated_at = db.Column(DATETIME2(precision=1), 
                          default=lambda: datetime.now().replace(microsecond=0),
                          onupdate=lambda: datetime.now().replace(microsecond=0),
                          nullable=False)
```

2. **创建记录时**：
```python
from sqlalchemy import text

def create_record():
    try:
        # 注意：SQL 语句中不包含 created_at 和 updated_at 字段
        sql = text("""
            INSERT INTO table_name 
            (name, location, manager_id, status, notes)  
            OUTPUT inserted.id
            VALUES 
            (:name, :location, :manager_id, :status, :notes)
        """)
        
        params = {
            'name': form.name.data,
            'location': form.location.data,
            'manager_id': form.manager_id.data,
            'status': form.status.data,
            'notes': form.notes.data
        }
        
        result = db.session.execute(sql, params)
        record_id = result.fetchone()[0]
        db.session.commit()
        return record_id
        
    except Exception as e:
        db.session.rollback()
        raise
```

3. **更新记录时**：
```python
def update_record(record_id):
    try:
        # 注意：SQL 语句中不包含 updated_at 字段
        sql = text("""
            UPDATE table_name
            SET name = :name,
                location = :location,
                manager_id = :manager_id,
                status = :status,
                notes = :notes
            WHERE id = :id
        """)
        
        params = {
            'name': form.name.data,
            'location': form.location.data,
            'manager_id': form.manager_id.data,
            'status': form.status.data,
            'notes': form.notes.data,
            'id': record_id
        }
        
        db.session.execute(sql, params)
        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        raise
```

4. **关键点说明**：

- **不使用 ORM**：避免使用 SQLAlchemy ORM 的方式处理时间字段
- **不手动设置时间**：让数据库使用默认值处理 created_at 和 updated_at
- **精度控制**：使用 DATETIME2(precision=1) 控制时间精度到 0.1 秒
- **去除微秒**：使用 datetime.now().replace(microsecond=0) 去除微秒部分
- **参数化查询**：使用命名参数（:name）而不是问号占位符
- **异常处理**：使用 try-except 进行异常处理和回滚

5. **前端显示时间**：
```python
@app.template_filter('format_datetime')
def format_datetime_filter(value, format='%Y-%m-%d %H:%M'):
    """格式化日期时间，默认只精确到分钟"""
    if value is None:
        return ''
    if isinstance(value, datetime):
        return value.strftime(format)
    return value
```

6. **使用示例**：
```python
@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = ModelForm()
    if form.validate_on_submit():
        try:
            # 使用原始 SQL 创建记录
            sql = text("""
                INSERT INTO warehouses 
                (name, area_id, location, manager_id, status, notes)
                OUTPUT inserted.id
                VALUES 
                (:name, :area_id, :location, :manager_id, :status, :notes)
            """)
            
            result = db.session.execute(sql, {
                'name': form.name.data,
                'area_id': form.area_id.data,
                'location': form.location.data,
                'manager_id': form.manager_id.data,
                'status': form.status.data,
                'notes': form.notes.data
            })
            
            record_id = result.fetchone()[0]
            db.session.commit()
            
            flash('创建成功', 'success')
            return redirect(url_for('view', id=record_id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(str(e))
            flash('操作失败', 'danger')
            
    return render_template('form.html', form=form)
```

这种方案的优点是：
1. 避免了 SQLAlchemy 的时间戳处理问题
2. 保持了数据库时间戳的一致性
3. 减少了代码复杂度
4. 提高了代码的可维护性
5. 保证了数据的准确性

需要注意的是，这个方案是针对 MSSQL Server 的 DATETIME2 类型特别优化的，如果使用其他数据库，可能需要调整相应的实现细节。
