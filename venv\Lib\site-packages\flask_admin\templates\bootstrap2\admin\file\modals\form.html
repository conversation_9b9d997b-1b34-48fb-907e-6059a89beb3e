{% import 'admin/static.html' as admin_static with context %}
{% import 'admin/lib.html' as lib with context %}

{% block body %}
  {# content added to modal-content #}
  {% block fa_form %}
    {{ lib.render_form(form, dir_url, action=request.url, is_modal=True) }}
  {% endblock %}
{% endblock %}

{% block tail %}
  <script src="{{ admin_static.url(filename='admin/js/bs2_modal.js', v='1.0.0') }}"></script>

  <script>
  // fill the header of modal dynamically
  $('.modal-header h3').html('{% block header %}{{ header_text }}{% endblock %}');
  </script>
{% endblock %}
