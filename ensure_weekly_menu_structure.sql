-- 确保周菜单表结构完整（不删除现有表）
-- 只添加缺失的表、字段和索引
USE [StudentsCMSSP]
GO

PRINT '开始检查和完善周菜单表结构...'
PRINT '========================================'
PRINT '注意：此脚本不会删除任何现有表或数据'
PRINT ''

-- 1. 确保 weekly_menus 表存在且结构正确
PRINT '1. 检查 weekly_menus 表...'
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
BEGIN
    PRINT '创建 weekly_menus 表...'
    
    CREATE TABLE [dbo].[weekly_menus](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [week_start] [date] NOT NULL,
        [week_end] [date] NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '计划中',
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menus] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menus_area_id] FOREIGN KEY([area_id]) 
            REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_weekly_menus_created_by] FOREIGN KEY([created_by]) 
            REFERENCES [dbo].[users] ([id])
    )
    
    PRINT '✓ weekly_menus 表创建成功'
END
ELSE
BEGIN
    PRINT '✓ weekly_menus 表已存在'
    
    -- 检查必要字段是否存在
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'area_id')
    BEGIN
        ALTER TABLE weekly_menus ADD area_id INT NOT NULL DEFAULT 1
        PRINT '✓ 添加 area_id 字段'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'status')
    BEGIN
        ALTER TABLE weekly_menus ADD status NVARCHAR(20) NOT NULL DEFAULT '计划中'
        PRINT '✓ 添加 status 字段'
    END
END

-- 2. 确保 weekly_menu_recipes 表存在且结构正确
PRINT ''
PRINT '2. 检查 weekly_menu_recipes 表...'
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menu_recipes')
BEGIN
    PRINT '创建 weekly_menu_recipes 表...'
    
    CREATE TABLE [dbo].[weekly_menu_recipes](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [weekly_menu_id] [int] NOT NULL,
        [day_of_week] [int] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [recipe_id] [int] NULL,
        [recipe_name] [nvarchar](100) NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menu_recipes] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menu_recipes_weekly_menu_id] FOREIGN KEY([weekly_menu_id]) 
            REFERENCES [dbo].[weekly_menus] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_weekly_menu_recipes_recipe_id] FOREIGN KEY([recipe_id]) 
            REFERENCES [dbo].[recipes] ([id])
    )
    
    PRINT '✓ weekly_menu_recipes 表创建成功'
END
ELSE
BEGIN
    PRINT '✓ weekly_menu_recipes 表已存在'
END

-- 3. 检查 consumption_plans 表的兼容性
PRINT ''
PRINT '3. 检查 consumption_plans 表兼容性...'
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    -- 检查是否有 area_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
    BEGIN
        PRINT '为 consumption_plans 添加 area_id 字段...'
        ALTER TABLE consumption_plans ADD area_id INT NOT NULL DEFAULT 1
        
        -- 添加外键约束（如果不存在）
        IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE parent_object_id = OBJECT_ID('consumption_plans') AND name = 'FK_consumption_plans_area_id')
        BEGIN
            ALTER TABLE consumption_plans ADD CONSTRAINT FK_consumption_plans_area_id 
            FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
        END
        
        PRINT '✓ 添加 area_id 字段和外键约束'
    END
    ELSE
    BEGIN
        PRINT '✓ consumption_plans.area_id 字段已存在'
    END
    
    -- 检查其他必要字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'consumption_date')
    BEGIN
        ALTER TABLE consumption_plans ADD consumption_date DATE NOT NULL DEFAULT GETDATE()
        PRINT '✓ 添加 consumption_date 字段'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'meal_type')
    BEGIN
        ALTER TABLE consumption_plans ADD meal_type NVARCHAR(20) NOT NULL DEFAULT '午餐'
        PRINT '✓ 添加 meal_type 字段'
    END
END
ELSE
BEGIN
    PRINT '⚠️ consumption_plans 表不存在，建议创建'
END

-- 4. 创建性能优化索引（如果不存在）
PRINT ''
PRINT '4. 创建性能优化索引...'

-- weekly_menus 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_area_id] ON [dbo].[weekly_menus] ([area_id])
    PRINT '✓ 创建 weekly_menus.area_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_week_start')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_week_start] ON [dbo].[weekly_menus] ([week_start])
    PRINT '✓ 创建 weekly_menus.week_start 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_optimized')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_area_week_optimized]
    ON [dbo].[weekly_menus] ([area_id], [week_start])
    INCLUDE ([id], [week_end], [status], [created_by], [created_at], [updated_at])
    PRINT '✓ 创建 weekly_menus 复合优化索引'
END

-- weekly_menu_recipes 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_weekly_menu_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_weekly_menu_id] ON [dbo].[weekly_menu_recipes] ([weekly_menu_id])
    PRINT '✓ 创建 weekly_menu_recipes.weekly_menu_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_recipe_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_recipe_id] ON [dbo].[weekly_menu_recipes] ([recipe_id])
    PRINT '✓ 创建 weekly_menu_recipes.recipe_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_menu_day_meal')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_menu_day_meal]
    ON [dbo].[weekly_menu_recipes] ([weekly_menu_id], [day_of_week], [meal_type])
    INCLUDE ([recipe_id], [recipe_name])
    PRINT '✓ 创建 weekly_menu_recipes 复合优化索引'
END

-- consumption_plans 表索引（如果表存在）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'IX_consumption_plans_area_date_meal')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_consumption_plans_area_date_meal]
        ON [dbo].[consumption_plans] ([area_id], [consumption_date], [meal_type])
        INCLUDE ([status], [diners_count])
        PRINT '✓ 创建 consumption_plans 复合优化索引'
    END
END

-- 5. 更新统计信息
PRINT ''
PRINT '5. 更新统计信息...'
UPDATE STATISTICS weekly_menus
UPDATE STATISTICS weekly_menu_recipes
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
    UPDATE STATISTICS consumption_plans
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '========================================'
PRINT '🎉 周菜单表结构检查和完善完成！'
PRINT '========================================'
PRINT ''
PRINT '完成的操作：'
PRINT '• 确保 weekly_menus 表存在且结构正确'
PRINT '• 确保 weekly_menu_recipes 表存在且结构正确'
PRINT '• 检查 consumption_plans 表的兼容性'
PRINT '• 创建了所有必要的性能优化索引'
PRINT '• 更新了统计信息'
PRINT ''
PRINT '注意：没有删除任何现有表或数据'
PRINT '现在可以正常使用周菜单系统了！'
PRINT '========================================'
