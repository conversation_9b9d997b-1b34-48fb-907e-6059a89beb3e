msgid ""
msgstr ""
"Project-Id-Version: flask-admin\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-02-07 00:17-0600\n"
"PO-Revision-Date: 2017-02-13 09:18-0500\n"
"Last-Translator: mr<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Belarusian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.1.1\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: crowdin.com\n"
"X-Crowdin-Project: flask-admin\n"
"X-Crowdin-Language: be\n"
"X-Crowdin-File: admin.pot\n"
"Language: be_BY\n"

#: ../flask_admin/base.py:440
msgid "Home"
msgstr "Галоўная"

#: ../flask_admin/contrib/rediscli.py:127
msgid "Cli: Invalid command."
msgstr "Cli: хібны загад."

#: ../flask_admin/contrib/fileadmin/__init__.py:352
msgid "File to upload"
msgstr "Файл для ладавання"

#: ../flask_admin/contrib/fileadmin/__init__.py:360
msgid "File required."
msgstr "Патрабуецца файл."

#: ../flask_admin/contrib/fileadmin/__init__.py:365
msgid "Invalid file type."
msgstr "Хібны тып файлу."

#: ../flask_admin/contrib/fileadmin/__init__.py:376
msgid "Content"
msgstr "Змесціва"

#: ../flask_admin/contrib/fileadmin/__init__.py:390
msgid "Invalid name"
msgstr "Хібная назва"

#: ../flask_admin/contrib/fileadmin/__init__.py:398
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:106
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:112
#: ../flask_admin/tests/sqla/test_translation.py:17
msgid "Name"
msgstr "Назва"

#: ../flask_admin/contrib/fileadmin/__init__.py:757
#, python-format
msgid "File \"%(name)s\" already exists."
msgstr "Файл \"%(name)s\" ужо йснуе."

#: ../flask_admin/contrib/fileadmin/__init__.py:802
#: ../flask_admin/contrib/fileadmin/__init__.py:885
#: ../flask_admin/contrib/fileadmin/__init__.py:947
#: ../flask_admin/contrib/fileadmin/__init__.py:1000
#: ../flask_admin/contrib/fileadmin/__init__.py:1047
#: ../flask_admin/contrib/fileadmin/__init__.py:1099
#: ../flask_admin/model/base.py:2168
msgid "Permission denied."
msgstr "Бракуе дазволу."

#: ../flask_admin/contrib/fileadmin/__init__.py:881
msgid "File uploading is disabled."
msgstr "Ладаванне файлаў адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:892
#, python-format
msgid "Successfully saved file: %(name)s"
msgstr "Файл паспяхова захаваны: %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:896
#, python-format
msgid "Failed to save file: %(error)s"
msgstr "Памылка захавання файлу: %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:904
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:148
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:150
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:148
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:150
msgid "Upload File"
msgstr "Заладаваць файл"

#: ../flask_admin/contrib/fileadmin/__init__.py:943
msgid "Directory creation is disabled."
msgstr "Стварэнне тэчак адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:956
#, python-format
msgid "Successfully created directory: %(directory)s"
msgstr "Тэчка паспяхова створаная: %(directory)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:960
#, python-format
msgid "Failed to create directory: %(error)s"
msgstr "Памылка стварэння тэчкі: %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:970
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:159
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:161
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:159
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:161
msgid "Create Directory"
msgstr "Стварыць тэчку"

#: ../flask_admin/contrib/fileadmin/__init__.py:996
msgid "Deletion is disabled."
msgstr "Выдаленне адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:1005
msgid "Directory deletion is disabled."
msgstr "Выдаленне тэчак адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:1011
#, python-format
msgid "Directory \"%(path)s\" was successfully deleted."
msgstr "Тэчка \"%(path)s\" паспяхова выдаленая."

#: ../flask_admin/contrib/fileadmin/__init__.py:1013
#, python-format
msgid "Failed to delete directory: %(error)s"
msgstr "Памылка выдалення тэчкі: %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1019
#: ../flask_admin/contrib/fileadmin/__init__.py:1176
#, python-format
msgid "File \"%(name)s\" was successfully deleted."
msgstr "Файл \"%(name)s\" паспяхова выдалены."

#: ../flask_admin/contrib/fileadmin/__init__.py:1021
#: ../flask_admin/contrib/fileadmin/__init__.py:1178
#, python-format
msgid "Failed to delete file: %(name)s"
msgstr "Памылка выдалення файлу: %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1043
msgid "Renaming is disabled."
msgstr "Перайменаванне адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:1051
msgid "Path does not exist."
msgstr "Шлях не йснуе."

#: ../flask_admin/contrib/fileadmin/__init__.py:1061
#, python-format
msgid "Successfully renamed \"%(src)s\" to \"%(dst)s\""
msgstr "Паспяхова змененае імя з \"%(src)s\" у \"%(dst)s\""

#: ../flask_admin/contrib/fileadmin/__init__.py:1064
#, python-format
msgid "Failed to rename: %(error)s"
msgstr "Памылка перайменавання: %(error)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1078
#, python-format
msgid "Rename %(name)s"
msgstr "Перайменаваць %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1115
#, python-format
msgid "Error saving changes to %(name)s."
msgstr "Памылка захавання зменаў у %(name)s."

#: ../flask_admin/contrib/fileadmin/__init__.py:1119
#, python-format
msgid "Changes to %(name)s saved successfully."
msgstr "Змены ў %(name)s паспяхова захаваныя."

#: ../flask_admin/contrib/fileadmin/__init__.py:1128
#, python-format
msgid "Error reading %(name)s."
msgstr "Памылка чытання %(name)s."

#: ../flask_admin/contrib/fileadmin/__init__.py:1131
#: ../flask_admin/contrib/fileadmin/__init__.py:1140
#, python-format
msgid "Unexpected error while reading from %(name)s"
msgstr "Непрадугледжаная памылка падчас чытання з %(name)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1137
#, python-format
msgid "Cannot edit %(name)s."
msgstr "Немагчыма рэдагаваць %(name)s."

#: ../flask_admin/contrib/fileadmin/__init__.py:1155
#, python-format
msgid "Editing %(path)s"
msgstr "Рэдагаванне %(path)s"

#: ../flask_admin/contrib/fileadmin/__init__.py:1163
#: ../flask_admin/contrib/mongoengine/view.py:658
#: ../flask_admin/contrib/peewee/view.py:487
#: ../flask_admin/contrib/pymongo/view.py:384
#: ../flask_admin/contrib/sqla/view.py:1149
msgid "Delete"
msgstr "Выдаліць"

#: ../flask_admin/contrib/fileadmin/__init__.py:1164
msgid "Are you sure you want to delete these files?"
msgstr "Вы дакладна хочаце выдаліць гэтыя файлы?"

#: ../flask_admin/contrib/fileadmin/__init__.py:1167
msgid "File deletion is disabled."
msgstr "Выдаленне файлаў адключанае."

#: ../flask_admin/contrib/fileadmin/__init__.py:1180
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:17
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:22
msgid "Edit"
msgstr "Рэдагаваць"

#: ../flask_admin/contrib/fileadmin/s3.py:153
msgid "Cannot operate on non empty directories"
msgstr "Дзеянне з непустымі тэчкамі немагчымае"

#: ../flask_admin/contrib/mongoengine/filters.py:39
#: ../flask_admin/contrib/peewee/filters.py:35
#: ../flask_admin/contrib/pymongo/filters.py:38
#: ../flask_admin/contrib/sqla/filters.py:41
msgid "equals"
msgstr "роўны"

#: ../flask_admin/contrib/mongoengine/filters.py:48
#: ../flask_admin/contrib/peewee/filters.py:43
#: ../flask_admin/contrib/pymongo/filters.py:47
#: ../flask_admin/contrib/sqla/filters.py:49
msgid "not equal"
msgstr "няроўны"

#: ../flask_admin/contrib/mongoengine/filters.py:58
#: ../flask_admin/contrib/peewee/filters.py:52
#: ../flask_admin/contrib/pymongo/filters.py:57
#: ../flask_admin/contrib/sqla/filters.py:58
msgid "contains"
msgstr "утрымлівае"

#: ../flask_admin/contrib/mongoengine/filters.py:68
#: ../flask_admin/contrib/peewee/filters.py:61
#: ../flask_admin/contrib/pymongo/filters.py:67
#: ../flask_admin/contrib/sqla/filters.py:67
msgid "not contains"
msgstr "ня ўтрымлівае"

#: ../flask_admin/contrib/mongoengine/filters.py:77
#: ../flask_admin/contrib/peewee/filters.py:69
#: ../flask_admin/contrib/pymongo/filters.py:80
#: ../flask_admin/contrib/sqla/filters.py:75
msgid "greater than"
msgstr "большы за"

#: ../flask_admin/contrib/mongoengine/filters.py:86
#: ../flask_admin/contrib/peewee/filters.py:77
#: ../flask_admin/contrib/pymongo/filters.py:93
#: ../flask_admin/contrib/sqla/filters.py:83
msgid "smaller than"
msgstr "меншы за"

#: ../flask_admin/contrib/mongoengine/filters.py:98
#: ../flask_admin/contrib/peewee/filters.py:88
#: ../flask_admin/contrib/sqla/filters.py:94
msgid "empty"
msgstr "пусты"

#: ../flask_admin/contrib/mongoengine/filters.py:113
#: ../flask_admin/contrib/peewee/filters.py:102
#: ../flask_admin/contrib/sqla/filters.py:108
msgid "in list"
msgstr "у спісе"

#: ../flask_admin/contrib/mongoengine/filters.py:122
#: ../flask_admin/contrib/peewee/filters.py:111
#: ../flask_admin/contrib/sqla/filters.py:118
msgid "not in list"
msgstr "ня ў спісе"

#: ../flask_admin/contrib/mongoengine/filters.py:222
#: ../flask_admin/contrib/peewee/filters.py:207
#: ../flask_admin/contrib/peewee/filters.py:244
#: ../flask_admin/contrib/peewee/filters.py:281
#: ../flask_admin/contrib/sqla/filters.py:213
#: ../flask_admin/contrib/sqla/filters.py:250
#: ../flask_admin/contrib/sqla/filters.py:287
msgid "not between"
msgstr "не паміж"

#: ../flask_admin/contrib/mongoengine/filters.py:247
msgid "ObjectId equals"
msgstr "ObjectId роўны"

#: ../flask_admin/contrib/mongoengine/view.py:551
#, python-format
msgid "Failed to get model. %(error)s"
msgstr "Памылка атрымання мадэлі. %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:570
#: ../flask_admin/contrib/peewee/view.py:435
#: ../flask_admin/contrib/pymongo/view.py:316
#: ../flask_admin/contrib/sqla/view.py:1078
#, python-format
msgid "Failed to create record. %(error)s"
msgstr "Памылка стварэння запісу. %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:596
#: ../flask_admin/contrib/peewee/view.py:454
#: ../flask_admin/contrib/pymongo/view.py:341
#: ../flask_admin/contrib/sqla/view.py:1104 ../flask_admin/model/base.py:2305
#: ../flask_admin/model/base.py:2313 ../flask_admin/model/base.py:2315
#, python-format
msgid "Failed to update record. %(error)s"
msgstr "Памылка аднаўлення запісу. %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:619
#: ../flask_admin/contrib/peewee/view.py:469
#: ../flask_admin/contrib/pymongo/view.py:366
#: ../flask_admin/contrib/sqla/view.py:1129
#, python-format
msgid "Failed to delete record. %(error)s"
msgstr "Памылка выдалення запісу. %(error)s"

#: ../flask_admin/contrib/mongoengine/view.py:659
#: ../flask_admin/contrib/peewee/view.py:488
#: ../flask_admin/contrib/pymongo/view.py:385
#: ../flask_admin/contrib/sqla/view.py:1150
msgid "Are you sure you want to delete selected records?"
msgstr "Вы дакладна хочаце выдаліць пазначаныя запісы?"

#: ../flask_admin/contrib/mongoengine/view.py:668
#: ../flask_admin/contrib/peewee/view.py:505
#: ../flask_admin/contrib/pymongo/view.py:395
#: ../flask_admin/contrib/sqla/view.py:1166 ../flask_admin/model/base.py:2118
#, python-format
msgid "Record was successfully deleted."
msgid_plural "%(count)s records were successfully deleted."
msgstr[0] "Запіс быў паспяхова выдалены."
msgstr[1] "%(count)s запісы былі паспяхова выдаленыя."
msgstr[2] "%(count)s запісаў было паспяхова выдалена."

#: ../flask_admin/contrib/mongoengine/view.py:674
#: ../flask_admin/contrib/peewee/view.py:511
#: ../flask_admin/contrib/pymongo/view.py:400
#: ../flask_admin/contrib/sqla/view.py:1174
#, python-format
msgid "Failed to delete records. %(error)s"
msgstr "Памылка выдалення запісаў. %(error)s"

#: ../flask_admin/contrib/sqla/fields.py:126
#: ../flask_admin/contrib/sqla/fields.py:176
#: ../flask_admin/contrib/sqla/fields.py:181 ../flask_admin/model/fields.py:173
#: ../flask_admin/model/fields.py:222
msgid "Not a valid choice"
msgstr "Няслушны выбар"

#: ../flask_admin/contrib/sqla/fields.py:186
msgid "Key"
msgstr "Ключ"

#: ../flask_admin/contrib/sqla/fields.py:187
msgid "Value"
msgstr "Значэнне"

#: ../flask_admin/contrib/sqla/validators.py:42
msgid "Already exists."
msgstr "Ужо йснуе."

#: ../flask_admin/contrib/sqla/validators.py:60
#, python-format
msgid "At least %(num)d item is required"
msgid_plural "At least %(num)d items are required"
msgstr[0] "Патрабуецца як найменш %(num)d аб'ект"
msgstr[1] "Патрабуецца як найменш %(num)d аб'екты"
msgstr[2] "Патрабуецца як найменш %(num)d аб'ектаў"

#: ../flask_admin/contrib/sqla/view.py:1057
#, python-format
msgid "Integrity error. %(message)s"
msgstr "Памылка цэласнасці. %(message)s"

#: ../flask_admin/form/fields.py:98
msgid "Invalid time format"
msgstr "Хібны фармат часу"

#: ../flask_admin/form/fields.py:144
msgid "Invalid Choice: could not coerce"
msgstr "Хібны выбар: немагчыма пераўтварыць тып"

#: ../flask_admin/form/fields.py:208
msgid "Invalid JSON"
msgstr "Хібны JSON"

#: ../flask_admin/form/upload.py:207
msgid "Invalid file extension"
msgstr "Хібнае пашырэнне файла"

#: ../flask_admin/form/upload.py:214 ../flask_admin/form/upload.py:281
#, python-format
msgid "File \"%s\" already exists."
msgstr "Файл \"%s\" ужо йснуе."

#: ../flask_admin/model/base.py:1649
msgid "There are no items in the table."
msgstr "Няма элементаў ў табліцы."

#: ../flask_admin/model/base.py:1673
#, python-format
msgid "Invalid Filter Value: %(value)s"
msgstr "Хібнае значэнне цэдалю: %(value)s"

#: ../flask_admin/model/base.py:1984
msgid "Record was successfully created."
msgstr "Запіс быў паспяхова створаны."

#: ../flask_admin/model/base.py:2028 ../flask_admin/model/base.py:2080
#: ../flask_admin/model/base.py:2113 ../flask_admin/model/base.py:2297
msgid "Record does not exist."
msgstr "Запіс не йснуе."

#: ../flask_admin/model/base.py:2037 ../flask_admin/model/base.py:2301
msgid "Record was successfully saved."
msgstr "Запіс быў паспяхова захаваны."

#: ../flask_admin/model/base.py:2222
msgid "Tablib dependency not installed."
msgstr "Неабходны пакунак tablib не ўсталяваны."

#: ../flask_admin/model/base.py:2249
#, python-format
msgid "Export type \"%(type)s not supported."
msgstr "Тып экспарта \"%(type)s\" не падтрымліваецца."

#: ../flask_admin/model/filters.py:103 ../flask_admin/model/widgets.py:111
msgid "Yes"
msgstr "Так"

#: ../flask_admin/model/filters.py:104 ../flask_admin/model/widgets.py:110
msgid "No"
msgstr "Не"

#: ../flask_admin/model/filters.py:172 ../flask_admin/model/filters.py:212
#: ../flask_admin/model/filters.py:257
msgid "between"
msgstr "паміж"

#: ../flask_admin/model/template.py:81 ../flask_admin/model/template.py:88
#: ../flask_admin/templates/bootstrap2/admin/model/modals/details.html:37
#: ../flask_admin/templates/bootstrap3/admin/model/modals/details.html:8
msgid "View Record"
msgstr "Глядзець запіс"

#: ../flask_admin/model/template.py:95 ../flask_admin/model/template.py:102
#: ../flask_admin/model/template.py:109
#: ../flask_admin/templates/bootstrap2/admin/model/modals/edit.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/modals/edit.html:11
msgid "Edit Record"
msgstr "Рэдагаваць запіс"

#: ../flask_admin/model/widgets.py:61
msgid "Please select model"
msgstr "Выберыце, калі ласка, мадэль"

#: ../flask_admin/templates/bootstrap2/admin/actions.html:4
#: ../flask_admin/templates/bootstrap3/admin/actions.html:4
msgid "With selected"
msgstr "З выбранымі"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:200
#: ../flask_admin/templates/bootstrap3/admin/lib.html:190
msgid "Save"
msgstr "Захаваць"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:205
#: ../flask_admin/templates/bootstrap3/admin/lib.html:195
msgid "Cancel"
msgstr "Скасаваць"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:256
#: ../flask_admin/templates/bootstrap3/admin/lib.html:247
msgid "Save and Add Another"
msgstr "Захаваць і дадаць іншы"

#: ../flask_admin/templates/bootstrap2/admin/lib.html:259
#: ../flask_admin/templates/bootstrap3/admin/lib.html:250
msgid "Save and Continue Editing"
msgstr "Захаваць і працягнуць рэдагаванне"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:9
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:9
msgid "Root"
msgstr "Корань"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:40
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:49
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:90
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:99
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:40
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:49
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:89
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:98
#, python-format
msgid "Sort by %(name)s"
msgstr "Сартаваць па %(name)s"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:74
#: ../flask_admin/templates/bootstrap2/admin/file/list.html:77
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:74
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:77
msgid "Rename File"
msgstr "Перайменаваць файл"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:88
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:88
#, python-format
msgid "Are you sure you want to delete \\'%(name)s\\' recursively?"
msgstr "Вы дакладна жадаеце выдаліць \\'%(name)s\\' рэкурсіўна?"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:97
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:97
#, python-format
msgid "Are you sure you want to delete \\'%(name)s\\'?"
msgstr "Вы дакладна жадаеце выдаліць \\'%(name)s\\'?"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:125
msgid "Size"
msgstr "Памер"

#: ../flask_admin/templates/bootstrap2/admin/file/list.html:185
#: ../flask_admin/templates/bootstrap3/admin/file/list.html:185
msgid "Please select at least one file."
msgstr "Выберыце, калі ласка, як найменш адзін файл."

#: ../flask_admin/templates/bootstrap2/admin/model/create.html:14
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:8
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:14
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/create.html:14
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:8
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:14
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:17
msgid "List"
msgstr "Спіс"

#: ../flask_admin/templates/bootstrap2/admin/model/create.html:17
#: ../flask_admin/templates/bootstrap2/admin/model/details.html:12
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:18
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap3/admin/model/create.html:17
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:12
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:18
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:25
msgid "Create"
msgstr "Стварыць"

#: ../flask_admin/templates/bootstrap2/admin/model/details.html:21
#: ../flask_admin/templates/bootstrap2/admin/model/edit.html:26
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:21
#: ../flask_admin/templates/bootstrap3/admin/model/edit.html:26
msgid "Details"
msgstr "Падрабязнасці"

#: ../flask_admin/templates/bootstrap2/admin/model/details.html:29
#: ../flask_admin/templates/bootstrap2/admin/model/modals/details.html:8
#: ../flask_admin/templates/bootstrap3/admin/model/details.html:28
#: ../flask_admin/templates/bootstrap3/admin/model/modals/details.html:15
msgid "Filter"
msgstr "Цэдаль"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:13
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:14
msgid "Delete?"
msgstr "Выдаліць?"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:30
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:33
msgid "New"
msgstr "Новы"

#: ../flask_admin/templates/bootstrap2/admin/model/inline_list_base.html:40
#: ../flask_admin/templates/bootstrap3/admin/model/inline_list_base.html:43
msgid "Add"
msgstr "Дадаць"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:3
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:3
msgid "Add Filter"
msgstr "Дадаць цэдаль"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:18
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:30
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:18
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:30
msgid "Export"
msgstr "Экспарт"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:38
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:38
msgid "Apply"
msgstr "Ужыць"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:40
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:40
msgid "Reset Filters"
msgstr "Скасаваць цэдалі"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:59
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:66
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:59
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:64
msgid "Search"
msgstr "Пошук"

#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:74
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:77
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:78
#: ../flask_admin/templates/bootstrap2/admin/model/layout.html:79
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:72
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:75
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:76
#: ../flask_admin/templates/bootstrap3/admin/model/layout.html:77
msgid "items"
msgstr "элементы"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap2/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap2/admin/model/modals/create.html:22
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:23
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:25
#: ../flask_admin/templates/bootstrap3/admin/model/modals/create.html:10
msgid "Create New Record"
msgstr "Стварыць новы запіс"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:77
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:76
msgid "Select all records"
msgstr "Выбраць усе запісы"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:120
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:119
msgid "Select record"
msgstr "Выбраць запіс"

#: ../flask_admin/templates/bootstrap2/admin/model/list.html:185
#: ../flask_admin/templates/bootstrap3/admin/model/list.html:186
msgid "Please select at least one record."
msgstr "Выберыце, калі ласка, як найменш адзін запіс."

#: ../flask_admin/templates/bootstrap2/admin/model/row_actions.html:34
#: ../flask_admin/templates/bootstrap3/admin/model/row_actions.html:34
msgid "Are you sure you want to delete this record?"
msgstr "Вы дакладна жадаеце выдаліць гэты запіс?"

