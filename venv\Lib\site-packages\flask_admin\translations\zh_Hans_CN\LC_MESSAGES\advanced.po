# Advanced Functionality
# http://flask-admin.readthedocs.io/en/latest/advanced/
#
# Copyright (C) 2012-2015, <PERSON>
# This file is distributed under the same license as the flask-admin
# package.
# <AUTHOR> <EMAIL>, 2016.
msgid ""
msgstr ""
"Project-Id-Version: flask-admin 1.4.2\n"
"Report-Msgid-Bugs-To: https://github.com/sixu05202004/Flask-extensions-"
"docs\n"
"POT-Creation-Date: 2017-02-07 00:18-0600\n"
"PO-Revision-Date: 2016-11-27 03:00+0800\n"
"Last-Translator: 1dot75cm <<EMAIL>>\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.1.1\n"

#: ../../doc/advanced.rst:4
msgid "Advanced Functionality"
msgstr "高级功能"

#: ../../doc/advanced.rst:7
msgid "Enabling CSRF Protection"
msgstr "启用 CSRF 保护"

#: ../../doc/advanced.rst:9
msgid ""
"To add CSRF protection to the forms that are generated by *ModelView* "
"instances, use the SecureForm class in your *ModelView* subclass by "
"specifying the *form_base_class* parameter::"
msgstr ""
"要向 *ModelView* 实例生成的表单添加 CSRF 保护，请在 *ModelView* 子类中将 *form_base_class* "
"参数值设为 SecureForm 类::"

#: ../../doc/advanced.rst:18
msgid ""
"SecureForm requires WTForms 2 or greater. It uses the WTForms SessionCSRF"
" class to generate and validate the tokens for you when the forms are "
"submitted."
msgstr "SecureForm 需要 WTForms 2+。当表单提交时，它使用 WTForms SessionCSRF 类来生成和验证令牌。"

#: ../../doc/advanced.rst:22
msgid "Localization With Flask-Babelex"
msgstr "使用 Flask-Babelex 本地化"

#: ../../doc/advanced.rst:26
msgid ""
"Flask-Admin comes with translations for several languages. Enabling "
"localization is simple:"
msgstr "Flask-Admin 提供多种语言的翻译。启用本地化非常简单:"

#: ../../doc/advanced.rst:29
msgid ""
"Install `Flask-BabelEx <http://github.com/mrjoes/flask-babelex/>`_ to do "
"the heavy lifting. It's a fork of the `Flask-Babel "
"<http://github.com/mitshuhiko/flask-babel/>`_ package::"
msgstr ""
"安装 `Flask-BabelEx <http://github.com/mrjoes/flask-babelex/>`_ 扩展，它是 "
"`Flask-Babel <http://github.com/mitshuhiko/flask-babel/>`_ 的分支::"

#: ../../doc/advanced.rst:35
msgid "Initialize Flask-BabelEx by creating instance of `Babel` class::"
msgstr "通过创建 `Babel` 类实例，初始化 Flask-BabelEx 扩展::"

#: ../../doc/advanced.rst:43
msgid "Create a locale selector function::"
msgstr "创建区域选择器函数::"

#: ../../doc/advanced.rst:51
msgid ""
"Now, you could try a French version of the application at: "
"`http://localhost:5000/admin/?lang=fr "
"<http://localhost:5000/admin/?lang=fr>`_."
msgstr ""
"现在，您能够访问法语版本的管理界面: `http://localhost:5000/admin/?lang=fr "
"<http://localhost:5000/admin/?lang=fr>`_。"

#: ../../doc/advanced.rst:53
msgid ""
"Go ahead and add your own logic to the locale selector function. The "
"application can store locale in a user profile, cookie, session, etc. It "
"can also use the `Accept-Language` header to make the selection "
"automatically."
msgstr ""
"继续，添加您的区域设置到语言选择器函数。应用程序可以将区域设置存储在用户 profile, cookie, session 中。它还可以使用 "
"`Accept-Language` 头来自动选择语言。"

#: ../../doc/advanced.rst:57
msgid ""
"If the built-in translations are not enough, look at the `Flask-BabelEx "
"documentation <https://pythonhosted.org/Flask-BabelEx/>`_ to see how you "
"can add your own."
msgstr ""
"如果内置的翻译不够，请查看 `Flask-BabelEx 文档 <https://pythonhosted.org/Flask-"
"BabelEx/>`_ 了解如何添加翻译。"

#: ../../doc/advanced.rst:63
msgid "Managing Files & Folders"
msgstr "管理文件 & 目录"

#: ../../doc/advanced.rst:67
msgid ""
"To manage static files instead of database records, Flask-Admin comes "
"with the FileAdmin plug-in. It gives you the ability to upload, delete, "
"rename, etc. You can use it by adding a FileAdmin view to your app::"
msgstr ""
"为了管理静态文件而不是数据库记录，Flask-Admin 附带了 FileAdmin "
"插件。它使您能够上传，删除，重命名文件。您可以通过向应用程序添加 FileAdmin 视图来使用该功能::"

#: ../../doc/advanced.rst:83
msgid ""
"FileAdmin also has out-of-the-box support for managing files located on a"
" Amazon Simple Storage Service bucket. To add it to your app::"
msgstr "FileAdmin 还具有管理 Amazon Simple Storage 服务的文件的开箱支持。将该功能添加到应用程序::"

#: ../../doc/advanced.rst:93
msgid ""
"You can disable uploads, disable file deletion, restrict file uploads to "
"certain types, etc. Check :mod:`flask_admin.contrib.fileadmin` in the API"
" documentation for more details."
msgstr ""
"您可以配置禁用上传，禁用文件删除，限制上传文件的类型等功能。详细信息，请参阅 "
":mod:`flask_admin.contrib.fileadmin` API 文档。"

#: ../../doc/advanced.rst:97
msgid "Adding new file backends"
msgstr "添加新文件后端"

#: ../../doc/advanced.rst:99
msgid ""
"You can also implement your own storage backend by creating a class that "
"implements the same methods defined in the `LocalFileStorage` class. "
"Check :mod:`flask_admin.contrib.fileadmin` in the API documentation for "
"details on the methods."
msgstr ""
"您还可以通过继承 `LocalFileStorage` 类并重写其中的方法来实现自己的存储后端。有关方法的详细信息，请参阅 "
":mod:`flask_admin.contrib.fileadmin` API 文档。"

#: ../../doc/advanced.rst:104
msgid "Adding A Redis Console"
msgstr "添加 Redis 控制台"

#: ../../doc/advanced.rst:108
msgid ""
"Another plug-in that's available is the Redis Console. If you have a "
"Redis instance running on the same machine as your app, you can::"
msgstr "另一个可用的插件是 Redis 控制台。如果您的 Redis 实例与应用程序在同一个节点上运行，您可以::"

#: ../../doc/advanced.rst:122
msgid "Replacing Individual Form Fields"
msgstr "替换单个表单域"

#: ../../doc/advanced.rst:126
msgid ""
"The `form_overrides` attribute allows you to replace individual fields "
"within a form. A common use-case for this would be to add a *What-You-"
"See-Is-What-You-Get* (WYSIWIG) editor, or to handle file / image uploads "
"that need to be tied to a field in your model."
msgstr ""
"`form_overrides` 属性允许您替换表单中的单个字段。一个常见的用例是添加一个 What-You-See-Is-What-You-"
"Get (WYSIWIG, 所见即所得) 编辑器，或处理需要绑定到模型中的文件/图像上传字段。"

#: ../../doc/advanced.rst:131
msgid "WYSIWIG Text Fields"
msgstr "WYSIWIG 文本字段"

#: ../../doc/advanced.rst:132
msgid ""
"To handle complicated text content, you can use `CKEditor "
"<http://ckeditor.com/>`_ by subclassing some of the built-in WTForms "
"classes as follows::"
msgstr ""
"要处理复杂的文本内容，可以通过子类化一些内置的 WTForms 类来集成 `CKEditor <http://ckeditor.com/>`_ "
"编辑器，示例如下::"

#: ../../doc/advanced.rst:158
msgid "File & Image Fields"
msgstr "文件 & 图片字段"

#: ../../doc/advanced.rst:160
msgid ""
"Flask-Admin comes with a built-in "
":meth:`~flask_admin.form.upload.FileUploadField` and "
":meth:`~flask_admin.form.upload.ImageUploadField`. To make use of them, "
"you'll need to specify an upload directory and add them to the forms in "
"question. Image handling also requires you to have `Pillow "
"<https://pypi.python.org/pypi/Pillow/2.8.2>`_ installed if you need to do"
" any processing on the image files."
msgstr ""
"Flask-Admin 内置有 :meth:`~flask_admin.form.upload.FileUploadField` 和 "
":meth:`~flask_admin.form.upload.ImageUploadField`。要使用它们，您需要指定一个上传目录，并将它们添加到表单中。如果您需要对图像文件进行处理，则需要安装"
" `Pillow <https://pypi.python.org/pypi/Pillow/>`_。"

#: ../../doc/advanced.rst:166
msgid ""
"Have a look at the example at https://github.com/flask-admin/Flask-"
"Admin/tree/master/examples/forms."
msgstr ""
"请查看 https://github.com/flask-admin/Flask-Admin/tree/master/examples/forms"
" 示例。"

#: ../../doc/advanced.rst:169
msgid ""
"If you are using the MongoEngine backend, Flask-Admin supports GridFS-"
"backed image and file uploads through WTForms fields. Documentation can "
"be found at :mod:`flask_admin.contrib.mongoengine.fields`."
msgstr ""
"如果您使用 MongoEngine 后端，Flask-Admin 支持通过 WTForms 字段上传的文件/图片，保存在 GridFS "
"后端中。详细信息，请参阅 :mod:`flask_admin.contrib.mongoengine.fields` 文档。"

#: ../../doc/advanced.rst:171
msgid ""
"If you just want to manage static files in a directory, without tying "
"them to a database model, then use the :ref:`File-Admin<file-admin>` "
"plug-in."
msgstr "如果您只想管理目录中的静态文件，而不是将其绑定到数据库模型，则使用 :ref:`File-Admin<file-admin>` 插件。"

#: ../../doc/advanced.rst:175
msgid "Managing Geographical Models"
msgstr "管理地理模型"

#: ../../doc/advanced.rst:179
msgid ""
"If you want to store spatial information in a GIS database, Flask-Admin "
"has you covered. The GeoAlchemy backend extends the SQLAlchemy backend "
"(just as `GeoAlchemy <https://geoalchemy-2.readthedocs.io/>`_  extends "
"SQLAlchemy) to give you a pretty and functional map-based editor for your"
" admin pages."
msgstr ""
"如果你想在 GIS 数据库中存储空间信息，Flask-Admin 同样支持。GeoAlchemy 后端扩展了 SQLAlchemy 后端 (类似 "
"`GeoAlchemy <https://geoalchemy-2.readthedocs.io/>`_ 扩展了 SQLAlchemy "
")，为您的管理页面提供一个漂亮且功能强大的基于地图的编辑器。"

#: ../../doc/advanced.rst:184
msgid "Some notable features include:"
msgstr "一些显著的功能包括:"

#: ../../doc/advanced.rst:186
msgid ""
"Maps are displayed using the amazing `Leaflet <http://leafletjs.com/>`_ "
"Javascript library, with map data from `Mapbox "
"<https://www.mapbox.com/>`_."
msgstr ""
"使用惊人的 `Leaflet <http://leafletjs.com/>`_ Javascript 库显示地图，地图数据来自 `Mapbox "
"<https://www.mapbox.com/>`_。"

#: ../../doc/advanced.rst:188
msgid ""
"Geographic information, including points, lines and polygons, can be "
"edited interactively using `Leaflet.Draw "
"<https://github.com/Leaflet/Leaflet.draw>`_."
msgstr ""
"地理信息，包括点，线和多边形，可以使用 `Leaflet.Draw "
"<https://github.com/Leaflet/Leaflet.draw>`_ 交互式编辑。"

#: ../../doc/advanced.rst:190
msgid ""
"Graceful fallback: `GeoJSON <http://geojson.org/>`_ data can be edited in"
" a ``<textarea>``, if the user has turned off Javascript."
msgstr ""
"后备支持：如果用户已关闭 Javascript，则可以在 ``<textarea>`` 中编辑 `GeoJSON "
"<http://geojson.org/>`_ 数据。"

#: ../../doc/advanced.rst:192
msgid ""
"Works with a `Geometry "
"<https://geoalchemy-2.readthedocs.io/en/latest/types.html#geoalchemy2.types.Geometry>`_"
" SQL field that is integrated with `Shapely "
"<http://toblerity.org/shapely/>`_ objects."
msgstr ""
"使用与 `Shapely <http://toblerity.org/shapely/>`_ 对象集成的 `Geometry "
"<https://geoalchemy-2.readthedocs.io/en/latest/types.html#geoalchemy2.types.Geometry>`_"
" SQL 字段。"

#: ../../doc/advanced.rst:194
msgid ""
"To get started, define some fields on your model using GeoAlchemy's "
"*Geometry* field. Next, add model views to your interface using the "
"ModelView class from the GeoAlchemy backend, rather than the usual "
"SQLAlchemy backend::"
msgstr ""
"要开始使用，请使用 GeoAlchemy 的 *Geometry* 字段在模型上定义一些字段。接下来，使用来自 GeoAlchemy 后端的 "
"ModelView 类，而不是通常的 SQLAlchemy 后端，将模型视图添加到您的接口::"

#: ../../doc/advanced.rst:209
msgid ""
"Some of the Geometry field types that are available include: \"POINT\", "
"\"MULTIPOINT\", \"POLYGON\", \"MULTIPOLYGON\", \"LINESTRING\" and "
"\"MULTILINESTRING\"."
msgstr ""
"可用的 Geometry 字段类型包括: \"POINT\", \"MULTIPOINT\", \"POLYGON\", "
"\"MULTIPOLYGON\", \"LINESTRING\" 和 \"MULTILINESTRING\"。"

#: ../../doc/advanced.rst:212
msgid ""
"Have a look at https://github.com/flask-admin/flask-"
"admin/tree/master/examples/geo_alchemy to get started."
msgstr ""
"请查看 https://github.com/flask-admin/flask-"
"admin/tree/master/examples/geo_alchemy 示例以开始使用。"

#: ../../doc/advanced.rst:216
msgid "Loading Tiles From Mapbox"
msgstr "从 Mapbox 加载地图"

#: ../../doc/advanced.rst:218
msgid ""
"To have map data display correctly, you'll have to sign up for an account"
" at https://www.mapbox.com/ and include some credentials in your "
"application's config::"
msgstr "要正确显示地图数据，您必须在 https://www.mapbox.com/ 注册帐户，并在应用程序的配置中包含一些凭据::"

#: ../../doc/advanced.rst:226
msgid ""
"Leaflet supports loading map tiles from any arbitrary map tile provider, "
"but at the moment, Flask-Admin only supports Mapbox. If you want to use "
"other providers, make a pull request!"
msgstr ""
"Leaflet 支持从任意地图图块 provider 程序加载地图图块，但目前 Flask-Admin 仅支持 "
"Mapbox。如果你想使用其他地图源，请提交 pull request！"

#: ../../doc/advanced.rst:231
msgid "Limitations"
msgstr "限制"

#: ../../doc/advanced.rst:233
msgid ""
"There's currently no way to sort, filter, or search on geometric fields "
"in the admin. It's not clear that there's a good way to do so. If you "
"have any ideas or suggestions, make a pull request!"
msgstr ""
"目前没有办法对 admin 中的 geometric 字段进行排序，过滤或搜索。不知道有没有好方法解决该问题。如果您有任何想法或建议，请提出 "
"pull request！"

#: ../../doc/advanced.rst:238
msgid "Customising Builtin Forms Via Rendering Rules"
msgstr "通过渲染规则定制内置表单"

#: ../../doc/advanced.rst:242
msgid ""
"Before version 1.0.7, all model backends were rendering the *create* and "
"*edit* forms using a special Jinja2 macro, which was looping over the "
"fields of a WTForms form object and displaying them one by one. This "
"works well, but it is difficult to customize."
msgstr ""
"在 1.0.7 版本前，所有模型后端都使用特殊的 Jinja2 宏来渲染 `create` 和 `edit` 表单，该宏在 WTForms "
"表单对象的字段上循环，并逐个显示它们。这很好，但是很难定制。"

#: ../../doc/advanced.rst:246
msgid ""
"Starting from version 1.0.7, Flask-Admin supports form rendering rules, "
"to give you fine grained control of how the forms for your modules should"
" be displayed."
msgstr "从 1.0.7 版本开始，Flask-Admin 支持表单渲染规则，以便您对模块中的表单如何显示进行细粒度的控制。"

#: ../../doc/advanced.rst:249
msgid ""
"The basic idea is pretty simple: the customizable rendering rules replace"
" a static macro, so you can tell Flask-Admin how each form should be "
"rendered. As an extension, however, the rendering rules also let you do a"
" bit more: You can use them to output HTML, call Jinja2 macros, render "
"fields, and so on."
msgstr ""
"基本思想很简单：可自定义的渲染规则替换了静态宏，所以你可以告诉 Flask-Admin "
"如何渲染每个表单。作为一个扩展，渲染规则还可以做更多事：你可以使用它们来输出 HTML，调用 Jinja2 宏，渲染字段等。"

#: ../../doc/advanced.rst:253
msgid ""
"Essentially, form rendering rules separate the form rendering from the "
"form definition. For example, it no longer matters in which sequence your"
" form fields are defined."
msgstr "基本上，表单渲染规则将表单渲染与表单定义分开。明显的改善是，您的表单字段定义的顺序不再重要。"

#: ../../doc/advanced.rst:256
msgid ""
"To start using the form rendering rules, put a list of form field names "
"into the `form_create_rules` property one of your admin views::"
msgstr "要开始使用表单渲染规则，请在管理视图中，将表单字段名列表放入 `form_create_rules` 属性中::"

#: ../../doc/advanced.rst:262
msgid ""
"In this example, only three fields will be rendered and `email` field "
"will be above other two fields."
msgstr "在此示例中，将仅渲染三个字段，并且 `email` 字段将高于其他两个字段。"

#: ../../doc/advanced.rst:264
msgid ""
"Whenever Flask-Admin sees a string value in `form_create_rules`, it "
"automatically assumes that it is a form field reference and creates a "
":class:`flask_admin.form.rules.Field` class instance for that field."
msgstr ""
"每当 Flask-Admin 在 `form_create_rules` 中查看字符串值时，它会假定值是一个表单字段引用，并为该字段创建一个 "
":class:`flask_admin.form.rules.Field` 类实例。"

#: ../../doc/advanced.rst:267
msgid ""
"Let's say we want to display some text between the `email` and "
"`first_name` fields. This can be accomplished by using the "
":class:`flask_admin.form.rules.Text` class::"
msgstr ""
"假设我们要在 `email` 和 `first_name` 字段之间显示一些文本。可以通过使用 "
":class:`flask_admin.form.rules.Text` 类来实现::"

#: ../../doc/advanced.rst:276
msgid "Built-in Rules"
msgstr "内置规则"

#: ../../doc/advanced.rst:278
msgid ""
"Flask-Admin comes with few built-in rules that can be found in the "
":mod:`flask_admin.form.rules` module:"
msgstr "Flask-Admin 有一些内置规则，可以在 :mod:`flask_admin.form.rules` 模块中找到:"

#: ../../doc/advanced.rst:281
msgid "Form Rendering Rule"
msgstr "表单渲染规则"

#: ../../doc/advanced.rst:281
msgid "Description"
msgstr "描述"

#: ../../doc/advanced.rst:283
msgid ":class:`flask_admin.form.rules.BaseRule`"
msgstr ":class:`flask_admin.form.rules.BaseRule`"

#: ../../doc/advanced.rst:283
msgid "All rules derive from this class"
msgstr "所有规则都派生自该类"

#: ../../doc/advanced.rst:284
msgid ":class:`flask_admin.form.rules.NestedRule`"
msgstr ":class:`flask_admin.form.rules.NestedRule`"

#: ../../doc/advanced.rst:284
msgid "Allows rule nesting, useful for HTML containers"
msgstr "允许规则嵌套，适用于 HTML 容器"

#: ../../doc/advanced.rst:285
msgid ":class:`flask_admin.form.rules.Text`"
msgstr ":class:`flask_admin.form.rules.Text`"

#: ../../doc/advanced.rst:285
msgid "Simple text rendering rule"
msgstr "简单的文本渲染规则"

#: ../../doc/advanced.rst:286
msgid ":class:`flask_admin.form.rules.HTML`"
msgstr ":class:`flask_admin.form.rules.HTML`"

#: ../../doc/advanced.rst:286
msgid "Same as `Text` rule, but does not escape the text"
msgstr "与 `Text` 规则相同，但不转义文本"

#: ../../doc/advanced.rst:287
msgid ":class:`flask_admin.form.rules.Macro`"
msgstr ":class:`flask_admin.form.rules.Macro`"

#: ../../doc/advanced.rst:287
msgid "Calls macro from current Jinja2 context"
msgstr "从当前 Jinja2 上下文调用宏"

#: ../../doc/advanced.rst:288
msgid ":class:`flask_admin.form.rules.Container`"
msgstr ":class:`flask_admin.form.rules.Container`"

#: ../../doc/advanced.rst:288
msgid "Wraps child rules into container rendered by macro"
msgstr "将子规则包含到由宏渲染的容器中"

#: ../../doc/advanced.rst:289
msgid ":class:`flask_admin.form.rules.Field`"
msgstr ":class:`flask_admin.form.rules.Field`"

#: ../../doc/advanced.rst:289
msgid "Renders single form field"
msgstr "渲染单个表单字段"

#: ../../doc/advanced.rst:290
msgid ":class:`flask_admin.form.rules.Header`"
msgstr ":class:`flask_admin.form.rules.Header`"

#: ../../doc/advanced.rst:290
msgid "Renders form header"
msgstr "渲染表单标头"

#: ../../doc/advanced.rst:291
msgid ":class:`flask_admin.form.rules.FieldSet`"
msgstr ":class:`flask_admin.form.rules.FieldSet`"

#: ../../doc/advanced.rst:291
msgid "Renders form header and child rules"
msgstr "渲染表单标头和主体规则"

#: ../../doc/advanced.rst:297
msgid "Using Different Database Backends"
msgstr "使用不同的数据库后端"

#: ../../doc/advanced.rst:301
msgid ""
"Other than SQLAlchemy... There are five different backends for you to "
"choose from, depending on which database you would like to use for your "
"application. If, however, you need to implement your own database "
"backend, have a look at :ref:`adding-model-backend`."
msgstr ""
"除了 SQLAlchemy... 有五个不同的后端供您选择，具体取决于您的应用程序使用的数据库。如果您需要实现自己的数据库后端，请查看 :ref"
":`adding-model-backend`。"

#: ../../doc/advanced.rst:305
msgid ""
"If you don't know where to start, but you're familiar with relational "
"databases, then you should probably look at using `SQLAlchemy`_. It is a "
"full-featured toolkit, with support for SQLite, PostgreSQL, MySQL, Oracle"
" and MS-SQL amongst others. It really comes into its own once you have "
"lots of data, and a fair amount of relations between your data models. If"
" you want to track spatial data like latitude/longitude points, you "
"should look into `GeoAlchemy`_, as well."
msgstr ""
"如果您熟悉关系型数据库，那么应该使用 `SQLAlchemy`_。它是一个全功能的工具包，支持 SQLite, PostgreSQL, "
"MySQL, Oracle 和 MS-SQL 等数据库。如果您有很多数据，并且数据模型之间有很多关系，应该使用 "
"SQLAlchemy。如果要跟踪空间数据 (如纬度/经度点)，则应使用 `GeoAlchemy`_。"

#: ../../doc/advanced.rst:312
msgid "SQLAlchemy"
msgstr "SQLAlchemy"

#: ../../doc/advanced.rst:314
msgid "Notable features:"
msgstr "特点:"

#: ../../doc/advanced.rst:316
msgid "SQLAlchemy 0.6+ support"
msgstr "SQLAlchemy 0.6+ 支持"

#: ../../doc/advanced.rst:317
msgid "Paging, sorting, filters"
msgstr "分页, 排序, 过滤"

#: ../../doc/advanced.rst:318
msgid "Proper model relationship handling"
msgstr "正确的模型关系处理"

#: ../../doc/advanced.rst:319
msgid "Inline editing of related models"
msgstr "相关模型的行内编辑"

#: ../../doc/advanced.rst:321
msgid "**Multiple Primary Keys**"
msgstr "**多主键**"

#: ../../doc/advanced.rst:323
msgid ""
"Flask-Admin has limited support for models with multiple primary keys. It"
" only covers specific case when all but one primary keys are foreign keys"
" to another model. For example, model inheritance following this "
"convention."
msgstr "Flask-Admin 对多主键模型的支持有限。它只支持一个主键是另一个模型的外键的特殊情况。例如，遵循此惯例的模型继承。"

#: ../../doc/advanced.rst:327
msgid "Let's Model a car with its tyres::"
msgstr "让我们看看汽车及其轮胎的模型::"

#: ../../doc/advanced.rst:344
msgid ""
"A specific tyre is identified by using the two primary key columns of the"
" ``Tyre`` class, of which the ``car_id`` key is itself a foreign key to "
"the class ``Car``."
msgstr "通过使用 ``Tire`` 类的两个主键列来识别特定轮胎，其中 ``car_id`` 键本身是 ``Car`` 类的外键。"

#: ../../doc/advanced.rst:347
msgid ""
"To be able to CRUD the ``Tyre`` class, you need to enumerate columns when"
" defining the AdminView::"
msgstr "为了能够对 ``Tire`` 类执行 CRUD，你需要在定义 AdminView 时枚举列::"

#: ../../doc/advanced.rst:352
msgid ""
"The ``form_columns`` needs to be explicit, as per default only one "
"primary key is displayed."
msgstr "``form_columns`` 需要显式指定，默认情况下只显示一个主键。"

#: ../../doc/advanced.rst:354
msgid ""
"When having multiple primary keys, **no** validation for uniqueness "
"*prior* to saving of the object will be done. Saving a model that "
"violates a unique-constraint leads to an Sqlalchemy-Integrity-Error. In "
"this case, ``Flask-Admin`` displays a proper error message and you can "
"change the data in the form. When the application has been started with "
"``debug=True`` the ``werkzeug`` debugger will catch the exception and "
"will display the stacktrace."
msgstr ""
"当有多个主键时，在保存对象之前 **不会** 进行唯一性验证。保存违反唯一约束的模型会导致 Sqlalchemy-Integrity-"
"Error。在这种情况下，``Flask-Admin`` 显示正确的错误消息，您可以更改表单中的数据。当应用程序以 ``debug=True`` "
"启动时， ``werkzeug`` 调试器将捕获异常并显示栈跟踪信息。"

#: ../../doc/advanced.rst:360
msgid "MongoEngine"
msgstr "MongoEngine"

#: ../../doc/advanced.rst:362
msgid ""
"If you're looking for something simpler than SQLAlchemy, and your data "
"models are reasonably self-contained, then `MongoDB "
"<https://www.mongodb.org/>`_, a popular *NoSQL* database, could be a "
"better option."
msgstr ""
"如果你需要比 SQLAlchemy 更简单的东西，你的数据模型是合理的自包含类型，那么 `MongoDB "
"<https://www.mongodb.org/>`_ 这个流行的 *NoSQL* 数据库，或许是一个更好的选择。"

#: ../../doc/advanced.rst:366
msgid ""
"`MongoEngine <http://mongoengine.org/>`_ is a python wrapper for MongoDB."
" For an example of using MongoEngine with Flask-Admin, see "
"https://github.com/flask-admin/flask-"
"admin/tree/master/examples/mongoengine."
msgstr ""
"`MongoEngine <http://mongoengine.org/>`_ 是 MongoDB 的 Python 封装。在 Flask-"
"Admin 中使用 MongoEngine 的示例，请参阅 https://github.com/flask-admin/flask-"
"admin/tree/master/examples/mongoengine。"

#: ../../doc/advanced.rst:371 ../../doc/advanced.rst:392
msgid "Features:"
msgstr "特性:"

#: ../../doc/advanced.rst:373
msgid "MongoEngine 0.7+ support"
msgstr "MongoEngine 0.7+ 支持"

#: ../../doc/advanced.rst:374
msgid "Paging, sorting, filters, etc"
msgstr "分页，排序，过滤等"

#: ../../doc/advanced.rst:375
msgid "Supports complex document structure (lists, subdocuments and so on)"
msgstr "支持复杂的文档结构 (列表，子文档等等)"

#: ../../doc/advanced.rst:376
msgid "GridFS support for file and image uploads"
msgstr "GridFS 支持存储上传的文件/图片"

#: ../../doc/advanced.rst:378
msgid ""
"In order to use MongoEngine integration, install the `Flask-MongoEngine "
"<https://flask-mongoengine.readthedocs.io>`_ package. Flask-Admin uses "
"form scaffolding from it."
msgstr ""
"为了集成 MongoEngine，需要安装 `Flask-MongoEngine <https://flask-"
"mongoengine.readthedocs.io>`_ 包。Flask-Admin 使用其中的表单脚手架。"

#: ../../doc/advanced.rst:382 ../../doc/advanced.rst:401
msgid "Known issues:"
msgstr "已知问题:"

#: ../../doc/advanced.rst:384
msgid ""
"Search functionality can't split query into multiple terms due to "
"MongoEngine query language limitations"
msgstr "由于 MongoEngine 查询语言的限制，搜索功能不能将查询拆分为多个条件"

#: ../../doc/advanced.rst:387
msgid ""
"For more, check the :class:`~flask_admin.contrib.mongoengine` API "
"documentation."
msgstr "更多信息，请参阅 :class:`~flask_admin.contrib.mongoengine` API 文档。"

#: ../../doc/advanced.rst:390
msgid "Peewee"
msgstr "Peewee"

#: ../../doc/advanced.rst:394
msgid "Peewee 2.x+ support;"
msgstr "Peewee 2.x+ 支持"

#: ../../doc/advanced.rst:395
msgid "Paging, sorting, filters, etc;"
msgstr "分页，排序，过滤等"

#: ../../doc/advanced.rst:396
msgid "Inline editing of related models;"
msgstr "相关模型的行内编辑"

#: ../../doc/advanced.rst:398
msgid ""
"In order to use peewee integration, you need to install two additional "
"Python packages: `peewee <http://docs.peewee-orm.com/>`_ and `wtf-peewee "
"<https://github.com/coleifer/wtf-peewee/>`_."
msgstr ""
"为了使用 peewee，您需要安装 2 个 Python 包：`peewee <http://docs.peewee-orm.com/>`_ 和 "
"`wtf-peewee <https://github.com/coleifer/wtf-peewee/>`_。"

#: ../../doc/advanced.rst:403
msgid ""
"Many-to-Many model relations are not supported: there's no built-in way "
"to express M2M relation in Peewee"
msgstr "不支持多对多模型关系：在 Peewee 中没有内置的方式来表达 M2M 关系"

#: ../../doc/advanced.rst:405
msgid ""
"For more, check the :class:`~flask_admin.contrib.peewee` API "
"documentation. Or look at the Peewee example at https://github.com/flask-"
"admin/flask-admin/tree/master/examples/peewee."
msgstr ""
"更多信息，请参阅 :class:`~flask_admin.contrib.peewee` API 文档。或查看 Peewee 使用示例 "
"https://github.com/flask-admin/flask-admin/tree/master/examples/peewee。"

#: ../../doc/advanced.rst:409
msgid "PyMongo"
msgstr "PyMongo"

#: ../../doc/advanced.rst:411
msgid "The bare minimum you have to provide for Flask-Admin to work with PyMongo:"
msgstr "Flask-Admin 使用 PyMongo 的最小要求:"

#: ../../doc/advanced.rst:413
msgid "A list of columns by setting `column_list` property"
msgstr "设置 `column_list` 属性，值为字段列表"

#: ../../doc/advanced.rst:414
msgid "Provide form to use by setting `form` property"
msgstr "设置 `form` 属性，值为要使用的表单"

#: ../../doc/advanced.rst:415
msgid ""
"When instantiating :class:`flask_admin.contrib.pymongo.ModelView` class, "
"you have to provide PyMongo collection object"
msgstr "当实例化 :class:`flask_admin.contrib.pymongo.ModelView` 类时，你必须提供 PyMongo 集合对象"

#: ../../doc/advanced.rst:417
msgid "This is minimal PyMongo view::"
msgstr "最简单的 PyMongo 视图:"

#: ../../doc/advanced.rst:433
msgid "On top of that you can add sortable columns, filters, text search, etc."
msgstr "除此之外，您还可以添加可排序的列，过滤器，文本搜索等。"

#: ../../doc/advanced.rst:435
msgid ""
"For more, check the :class:`~flask_admin.contrib.pymongoe` API "
"documentation. Or look at the Peewee example at https://github.com/flask-"
"admin/flask-admin/tree/master/examples/pymongo."
msgstr ""
"更多信息，请参阅 :class:`~flask_admin.contrib.pymongoe` API 文档。或查看 PyMongo 使用示例 "
"https://github.com/flask-admin/flask-admin/tree/master/examples/pymongo。"

#: ../../doc/advanced.rst:439
msgid "Migrating From Django"
msgstr "从 Django 迁移"

#: ../../doc/advanced.rst:443
msgid ""
"If you are used to `Django <https://www.djangoproject.com/>`_ and the "
"*django-admin* package, you will find Flask-Admin to work slightly "
"different from what you would expect."
msgstr ""
"如果你习惯了 `Django <https://www.djangoproject.com/>`_ 和 *django-admin* 包，你会发现"
" Flask-Admin 的工作方式与你期望的略有不同。"

#: ../../doc/advanced.rst:447
msgid "Design Philosophy"
msgstr "设计哲学"

#: ../../doc/advanced.rst:449
msgid ""
"In general, Django and *django-admin* strives to make life easier by "
"implementing sensible defaults. So a developer will be able to get an "
"application up in no time, but it will have to conform to most of the "
"defaults. Of course it is possible to customize things, but this often "
"requires a good understanding of what's going on behind the scenes, and "
"it can be rather tricky and time-consuming."
msgstr ""
"一般来说，Django 和 *django-admin* "
"有合理的默认值。因此，开发人员能够立即获得应用程序，但它必须符合大多数默认值。如果需要定制，需要深刻的理解后端的处理逻辑，这会相当耗时。"

#: ../../doc/advanced.rst:454
msgid ""
"The design philosophy behind Flask is slightly different. It embraces the"
" diversity that one tends to find in web applications by not forcing "
"design decisions onto the developer. Rather than making it very easy to "
"build an application that *almost* solves your whole problem, and then "
"letting you figure out the last bit, Flask aims to make it possible for "
"you to build the *whole* application. It might take a little more effort "
"to get started, but once you've got the hang of it, the sky is the "
"limit... Even when your application is a little different from most other"
" applications out there on the web."
msgstr ""
"Flask 背后的设计理念略有不同。它倾向于使 web 应用程序更灵活，而不是强制设计所有方面。Django "
"使您可以很容易构建应用程序，几乎替您解决了所有问题；Flask 旨在使您控制构建整个应用程序。它需要学习更多内容，一旦您了解了 "
"Flask，您就可以开发任何类型的 Web 应用程序。"

#: ../../doc/advanced.rst:461
msgid ""
"Flask-Admin follows this same design philosophy. So even though it "
"provides you with several tools for getting up & running quickly, it will"
" be up to you, as a developer, to tell Flask-Admin what should be "
"displayed and how. Even though it is easy to get started with a simple "
"`CRUD <http://en.wikipedia.org/wiki/Create,_read,_update_and_delete>`_ "
"interface for each model in your application, Flask-Admin doesn't fix you"
" to this approach, and you are free to define other ways of interacting "
"with some, or all, of your models."
msgstr ""
"Flask-Admin 遵循同样的设计理念。因此，即使它为您提供了几种快速启动和运行的工具，作为开发人员，还是应该由您告诉 Flask-Admin"
" 应该显示什么以及如何显示。即使可以很容易的为应用程序的每个模型构建一个简单的 `CRUD "
"<http://en.wikipedia.org/wiki/Create,_read,_update_and_delete>`_ 接口"
"，Flask-Admin 也不能完全解决所有问题，您可以灵活的定义与模块的交互方法，来实现所有功能。"

#: ../../doc/advanced.rst:467
msgid ""
"Due to Flask-Admin supporting more than one ORM (SQLAlchemy, MongoEngine,"
" Peewee, raw pymongo), the developer is even free to mix different model "
"types into one application by instantiating appropriate CRUD classes."
msgstr ""
"由于 Flask-Admin 支持多种 ORM (SQLAlchemy, MongoEngine, Peewee, raw "
"pymongo)，开发人员甚至可以通过实例化相应的 CRUD 类，将不同的模型类型混合到一个应用程序中。"

#: ../../doc/advanced.rst:470
msgid ""
"Here is a list of some of the configuration properties that are made "
"available by Flask-Admin and the SQLAlchemy backend. You can also see "
"which *django-admin* properties they correspond to:"
msgstr "以下是 Flask-Admin 和 SQLAlchemy 后端提供的一些配置属性的列表。您还可以查看它们对应的 *django-admin* 属性:"

#: ../../doc/advanced.rst:474
msgid "Django"
msgstr "Django"

#: ../../doc/advanced.rst:474
msgid "Flask-Admin"
msgstr "Flask-Admin"

#: ../../doc/advanced.rst:476
msgid "actions"
msgstr "actions"

#: ../../doc/advanced.rst:476
msgid ":attr:`~flask_admin.actions`"
msgstr ":attr:`~flask_admin.actions`"

#: ../../doc/advanced.rst:477
msgid "exclude"
msgstr "exclude"

#: ../../doc/advanced.rst:477
msgid ":attr:`~flask_admin.model.BaseModelView.form_excluded_columns`"
msgstr ":attr:`~flask_admin.model.BaseModelView.form_excluded_columns`"

#: ../../doc/advanced.rst:478
msgid "fields"
msgstr "fields"

#: ../../doc/advanced.rst:478
msgid ":attr:`~flask_admin.model.BaseModelView.form_columns`"
msgstr ":attr:`~flask_admin.model.BaseModelView.form_columns`"

#: ../../doc/advanced.rst:479
msgid "form"
msgstr "form"

#: ../../doc/advanced.rst:479
msgid ":attr:`~flask_admin.model.BaseModelView.form`"
msgstr ":attr:`~flask_admin.model.BaseModelView.form`"

#: ../../doc/advanced.rst:480
msgid "formfield_overrides"
msgstr "formfield_overrides"

#: ../../doc/advanced.rst:480
msgid ":attr:`~flask_admin.model.BaseModelView.form_args`"
msgstr ":attr:`~flask_admin.model.BaseModelView.form_args`"

#: ../../doc/advanced.rst:481
msgid "inlines"
msgstr "inlines"

#: ../../doc/advanced.rst:481
msgid ":attr:`~flask_admin.contrib.sqla.ModelView.inline_models`"
msgstr ":attr:`~flask_admin.contrib.sqla.ModelView.inline_models`"

#: ../../doc/advanced.rst:482
msgid "list_display"
msgstr "list_display"

#: ../../doc/advanced.rst:482
msgid ":attr:`~flask_admin.model.BaseModelView.column_list`"
msgstr ":attr:`~flask_admin.model.BaseModelView.column_list`"

#: ../../doc/advanced.rst:483
msgid "list_filter"
msgstr "list_filter"

#: ../../doc/advanced.rst:483
msgid ":attr:`~flask_admin.contrib.sqla.ModelView.column_filters`"
msgstr ":attr:`~flask_admin.contrib.sqla.ModelView.column_filters`"

#: ../../doc/advanced.rst:484
msgid "list_per_page"
msgstr "list_per_page"

#: ../../doc/advanced.rst:484
msgid ":attr:`~flask_admin.model.BaseModelView.page_size`"
msgstr ":attr:`~flask_admin.model.BaseModelView.page_size`"

#: ../../doc/advanced.rst:485
msgid "search_fields"
msgstr "search_fields"

#: ../../doc/advanced.rst:485
msgid ":attr:`~flask_admin.model.BaseModelView.column_searchable_list`"
msgstr ":attr:`~flask_admin.model.BaseModelView.column_searchable_list`"

#: ../../doc/advanced.rst:486
msgid "add_form_template"
msgstr "add_form_template"

#: ../../doc/advanced.rst:486
msgid ":attr:`~flask_admin.model.BaseModelView.create_template`"
msgstr ":attr:`~flask_admin.model.BaseModelView.create_template`"

#: ../../doc/advanced.rst:487
msgid "change_form_template"
msgstr "change_form_template"

#: ../../doc/advanced.rst:487
msgid ":attr:`~flask_admin.model.BaseModelView.change_form_template`"
msgstr ":attr:`~flask_admin.model.BaseModelView.change_form_template`"

#: ../../doc/advanced.rst:490
msgid ""
"You might want to check :class:`~flask_admin.model.BaseModelView` for "
"basic model configuration options (reused by all model backends) and "
"specific backend documentation, for example "
":class:`~flask_admin.contrib.sqla.ModelView`. There's much more than what"
" is displayed in this table."
msgstr ""
"您可能需要查看 :class:`~flask_admin.model.BaseModelView` 的基本模型配置选项 (由所有模型后端重用) "
"和特定的后端文档，例如 :class:`~flask_admin.contrib.sqla.ModelView`。了解更多的模型配置选项。"

#: ../../doc/advanced.rst:496
msgid "Overriding the Form Scaffolding"
msgstr "覆盖表单脚手架"

#: ../../doc/advanced.rst:500
msgid ""
"If you don't want to the use the built-in Flask-Admin form scaffolding "
"logic, you are free to roll your own by simply overriding "
":meth:`~flask_admin.model.base.scaffold_form`. For example, if you use "
"`WTForms-Alchemy <https://github.com/kvesteri/wtforms-alchemy>`_, you "
"could put your form generation code into a `scaffold_form` method in your"
" `ModelView` class."
msgstr ""
"如果你不想使用 Flask-Admin 内置的表单框架逻辑，你可以通过简单覆盖 "
":meth:`~flask_admin.model.base.scaffold_form` 来自由滚动。例如，如果使用 `WTForms-"
"Alchemy <https://github.com/kvesteri/wtforms-alchemy>`_，您可以将您的表单生成代码放入您的 "
"`ModelView` 类中的 `scaffold_form` 方法。"

#: ../../doc/advanced.rst:505
msgid ""
"For SQLAlchemy, if the `synonym_property` does not return a SQLAlchemy "
"field, then Flask-Admin won't be able to figure out what to do with it, "
"so it won't generate a form field. In this case, you would need to "
"manually contribute your own field::"
msgstr ""
"对于 SQLAlchemy，如果 `synonym_property` 不返回 SQLAlchemy 字段，那么 Flask-Admin "
"将不知道该如何生成表单字段。在这种情况下，您需要手动提供自己的字段::"

#: ../../doc/advanced.rst:515
msgid "Customizing Batch Actions"
msgstr "自定义批处理操作"

#: ../../doc/advanced.rst:519
msgid ""
"If you want to add other batch actions to the list view, besides the "
"default delete action, then you can define a function that implements the"
" desired logic and wrap it with the `@action` decorator."
msgstr "如果要向列表视图添加其他批处理操作，除了默认的删除操作，您可以定义一个实现所需逻辑的函数，并使用 `@action` 装饰器将其包装。"

#: ../../doc/advanced.rst:522
msgid ""
"The `action` decorator takes three parameters: `name`, `text` and "
"`confirmation`. While the wrapped function should accept only one "
"parameter - `ids`::"
msgstr ""
"`action` 装饰器需要三个参数: `name`, `text` 和 `confirmation`。而被包装的函数只接受一个 `ids` "
"参数::"

#~ msgid ""
#~ "For this to work, you would also"
#~ " need to create a template that "
#~ "extends the default functionality by "
#~ "including the necessary CKEditor javascript"
#~ " on the `create` and `edit` pages."
#~ " Save this in `templates/ckeditor.html`::"
#~ msgstr ""
#~ "为了使其工作，还需要创建一个模板，通过在 `create` 和 `edit` 页面包含必要的"
#~ " CKEditor javascript 来扩展默认功能。将其保存在 "
#~ "`templates/ckeditor.html` 中::"

