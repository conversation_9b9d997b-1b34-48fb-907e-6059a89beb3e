# PowerShell脚本：配置防火墙规则
# 需要以管理员身份运行

Write-Host "正在配置防火墙规则..." -ForegroundColor Green

try {
    # 删除可能存在的旧规则
    Write-Host "清理旧的防火墙规则..." -ForegroundColor Yellow
    
    $oldRules = @("HTTP Port 80", "Flask App Port 80", "IIS HTTP", "tdtech.xin HTTP")
    foreach ($rule in $oldRules) {
        try {
            Remove-NetFirewallRule -DisplayName $rule -ErrorAction SilentlyContinue
            Write-Host "已删除规则: $rule" -ForegroundColor Gray
        } catch {
            # 忽略不存在的规则
        }
    }
    
    # 添加新的防火墙规则
    Write-Host "添加新的防火墙规则..." -ForegroundColor Yellow
    
    # HTTP端口80入站规则
    New-NetFirewallRule -DisplayName "tdtech.xin HTTP Inbound" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTP端口80入站规则" -ForegroundColor Green
    
    # HTTP端口80出站规则
    New-NetFirewallRule -DisplayName "tdtech.xin HTTP Outbound" -Direction Outbound -Protocol TCP -LocalPort 80 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTP端口80出站规则" -ForegroundColor Green
    
    # Flask应用端口8080本地规则（仅本地访问）
    New-NetFirewallRule -DisplayName "Flask App Local" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow -RemoteAddress LocalSubnet
    Write-Host "✓ 已添加Flask应用本地访问规则" -ForegroundColor Green
    
    # HTTPS端口443规则（为将来的SSL做准备）
    New-NetFirewallRule -DisplayName "tdtech.xin HTTPS Inbound" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow -Profile Any
    Write-Host "✓ 已添加HTTPS端口443规则" -ForegroundColor Green
    
    Write-Host "防火墙配置完成！" -ForegroundColor Green
    
    # 显示当前防火墙状态
    Write-Host "`n当前防火墙规则:" -ForegroundColor Cyan
    Get-NetFirewallRule -DisplayName "*tdtech*" | Select-Object DisplayName, Direction, Action, Enabled | Format-Table -AutoSize
    
} catch {
    Write-Host "配置防火墙时出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n防火墙配置说明:" -ForegroundColor Yellow
Write-Host "- 端口80: 对外提供HTTP服务" -ForegroundColor White
Write-Host "- 端口8080: Flask应用内部端口（仅本地访问）" -ForegroundColor White
Write-Host "- 端口443: HTTPS服务（预留）" -ForegroundColor White
