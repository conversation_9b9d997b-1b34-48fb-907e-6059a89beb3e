-- 创建视频引导表
-- 执行时间：请在SQL Server Management Studio中执行

-- 检查表是否存在，如果存在则删除
IF OBJECT_ID('dbo.video_guides', 'U') IS NOT NULL
    DROP TABLE dbo.video_guides;

-- 创建视频引导表
CREATE TABLE dbo.video_guides (
    id INT IDENTITY(1,1) PRIMARY KEY,
    step_name NVARCHAR(64) NOT NULL,
    name NVARCHAR(128) NOT NULL,
    description NVARCHAR(500) NULL,
    file_path NVARCHAR(256) NOT NULL,
    thumbnail NVARCHAR(256) NULL,
    duration NVARCHAR(32) NULL,
    created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
);

-- 创建索引
CREATE INDEX IX_video_guides_step_name ON dbo.video_guides(step_name);
CREATE INDEX IX_video_guides_created_at ON dbo.video_guides(created_at);

-- 添加注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'视频引导表', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'主键ID', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'引导步骤名称', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'step_name';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'视频名称', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'name';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'视频描述', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'description';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'文件路径', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'file_path';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'缩略图路径', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'thumbnail';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'视频时长', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'duration';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'创建时间', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'created_at';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'更新时间', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'video_guides',
    @level2type = N'COLUMN', @level2name = N'updated_at';

-- 插入示例数据（可选）
INSERT INTO dbo.video_guides (step_name, name, description, file_path, thumbnail, duration) VALUES
('daily_management', '日常管理模块介绍', '介绍日常管理模块的基本功能和使用方法', '/static/videos/daily_management/sample.mp4', '/static/videos/daily_management/sample_thumb.jpg', '5分钟'),
('suppliers', '供应商管理指南', '详细介绍如何管理供应商信息', '/static/videos/suppliers/sample.mp4', '/static/videos/suppliers/sample_thumb.jpg', '8分钟'),
('ingredients_recipes', '食材食谱管理', '学习如何管理食材和食谱', '/static/videos/ingredients_recipes/sample.mp4', '/static/videos/ingredients_recipes/sample_thumb.jpg', '10分钟');

PRINT '视频引导表创建完成！';
