"""
食谱高级功能数据模型
此模块包含食谱高级功能所需的数据模型
包括：食谱评价和反馈、食谱模板和变种、基于库存的推荐、搜索和筛选优化
"""

from app import db
from datetime import datetime
import json
from sqlalchemy.dialects.mssql import DATETIME2

# 修改Recipe模型，添加模板和变种相关字段
# 这些字段将在models.py中的Recipe类中添加

# 食谱评价和反馈相关模型
class RecipeReview(db.Model):
    """食谱评价表"""
    __tablename__ = 'recipe_reviews'

    id = db.Column(db.Integer, primary_key=1)
    recipe_id = db.Column(db.Integer, db.<PERSON>ey('recipes.id'), nullable=0)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    area_id = db.Column(db.Integer, db.<PERSON>('administrative_areas.id'), nullable=0)
    rating = db.Column(db.Integer, nullable=0)  # 1-5星
    comment = db.Column(db.Text)
    usage_date = db.Column(db.Date)
    is_public = db.Column(db.Boolean, default=1)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)
    updated_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    recipe = db.relationship('Recipe', backref=db.backref('reviews', lazy='dynamic'))
    user = db.relationship('User', backref=db.backref('recipe_reviews', lazy='dynamic'))
    area = db.relationship('AdministrativeArea', backref=db.backref('recipe_reviews', lazy='dynamic'))
    images = db.relationship('RecipeReviewImage', backref='review', lazy='dynamic', cascade='all, delete-orphan')
    tags = db.relationship('RecipeReviewTag', backref='review', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'user_id': self.user_id,
            'user_name': self.user.name,
            'area_id': self.area_id,
            'area_name': self.area.name,
            'rating': self.rating,
            'comment': self.comment,
            'usage_date': self.usage_date.strftime('%Y-%m-%d') if self.usage_date else None,
            'is_public': self.is_public,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'images': [image.to_dict() for image in self.images],
            'tags': [tag.tag_name for tag in self.tags]
        }


class RecipeReviewImage(db.Model):
    """食谱评价图片表"""
    __tablename__ = 'recipe_review_images'

    id = db.Column(db.Integer, primary_key=1)
    review_id = db.Column(db.Integer, db.ForeignKey('recipe_reviews.id', ondelete='CASCADE'), nullable=0)
    image_path = db.Column(db.String(200), nullable=0)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    def to_dict(self):
        return {
            'id': self.id,
            'review_id': self.review_id,
            'image_path': self.image_path,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class RecipeReviewTag(db.Model):
    """食谱评价标签表"""
    __tablename__ = 'recipe_review_tags'

    id = db.Column(db.Integer, primary_key=1)
    review_id = db.Column(db.Integer, db.ForeignKey('recipe_reviews.id', ondelete='CASCADE'), nullable=0)
    tag_name = db.Column(db.String(50), nullable=0)

    def to_dict(self):
        return {
            'id': self.id,
            'review_id': self.review_id,
            'tag_name': self.tag_name
        }


class RecipeImprovementSuggestion(db.Model):
    """食谱改进建议表"""
    __tablename__ = 'recipe_improvement_suggestions'

    id = db.Column(db.Integer, primary_key=1)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=0)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    suggestion = db.Column(db.Text, nullable=0)
    is_adopted = db.Column(db.Boolean, default=0)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    recipe = db.relationship('Recipe', backref=db.backref('improvement_suggestions', lazy='dynamic'))
    user = db.relationship('User', backref=db.backref('recipe_suggestions', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'user_id': self.user_id,
            'user_name': self.user.name,
            'suggestion': self.suggestion,
            'is_adopted': self.is_adopted,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


# 食谱版本管理相关模型
class RecipeVersion(db.Model):
    """食谱版本表"""
    __tablename__ = 'recipe_versions'

    id = db.Column(db.Integer, primary_key=1)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=0)
    version = db.Column(db.Integer, nullable=0)
    data = db.Column(db.Text, nullable=0)  # JSON格式存储完整食谱数据
    changed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    change_reason = db.Column(db.Text)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    recipe = db.relationship('Recipe', backref=db.backref('versions', lazy='dynamic'))
    user = db.relationship('User', backref=db.backref('recipe_versions', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'version': self.version,
            'data': json.loads(self.data) if isinstance(self.data, str) else self.data,
            'changed_by': self.changed_by,
            'user_name': self.user.name,
            'change_reason': self.change_reason,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


# 食材替代和季节性信息相关模型
class RecipeIngredientAlternative(db.Model):
    """食材替代表"""
    __tablename__ = 'recipe_ingredient_alternatives'

    id = db.Column(db.Integer, primary_key=1)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=0)
    original_ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=0)
    alternative_ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=0)
    conversion_ratio = db.Column(db.Float, default=1.0)  # 替代比例
    notes = db.Column(db.Text)

    # 关系
    recipe = db.relationship('Recipe', backref=db.backref('ingredient_alternatives', lazy='dynamic'))
    original_ingredient = db.relationship('Ingredient', foreign_keys=[original_ingredient_id], backref=db.backref('as_original', lazy='dynamic'))
    alternative_ingredient = db.relationship('Ingredient', foreign_keys=[alternative_ingredient_id], backref=db.backref('as_alternative', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'original_ingredient_id': self.original_ingredient_id,
            'original_ingredient_name': self.original_ingredient.name,
            'alternative_ingredient_id': self.alternative_ingredient_id,
            'alternative_ingredient_name': self.alternative_ingredient.name,
            'conversion_ratio': self.conversion_ratio,
            'notes': self.notes
        }


class RecipeSeasonalInfo(db.Model):
    """食谱季节性信息表"""
    __tablename__ = 'recipe_seasonal_info'

    id = db.Column(db.Integer, primary_key=1)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), nullable=0)
    season = db.Column(db.String(20), nullable=0)  # 春/夏/秋/冬
    suitability_score = db.Column(db.Integer, default=3)  # 适宜度评分(1-5)
    notes = db.Column(db.Text)

    # 关系
    recipe = db.relationship('Recipe', backref=db.backref('seasonal_info', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'season': self.season,
            'suitability_score': self.suitability_score,
            'notes': self.notes
        }


# 标签和搜索相关模型
class RecipeTag(db.Model):
    """食谱标签表"""
    __tablename__ = 'recipe_tags'

    id = db.Column(db.Integer, primary_key=1)
    name = db.Column(db.String(50), nullable=0, unique=1)
    category = db.Column(db.String(50))
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    recipes = db.relationship('Recipe', secondary='recipe_tag_relations', backref=db.backref('tags', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'recipe_count': len(self.recipes)
        }


# 食谱标签关联表（多对多关系）
recipe_tag_relations = db.Table('recipe_tag_relations',
    db.Column('recipe_id', db.Integer, db.ForeignKey('recipes.id'), primary_key=1),
    db.Column('tag_id', db.Integer, db.ForeignKey('recipe_tags.id'), primary_key=1),
    db.Column('created_at', db.DateTime, default=lambda: datetime.now().replace(microsecond=0))
)


class UserRecipeFavorite(db.Model):
    """用户食谱收藏表"""
    __tablename__ = 'user_recipe_favorites'

    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), primary_key=1)
    recipe_id = db.Column(db.Integer, db.ForeignKey('recipes.id'), primary_key=1)
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    user = db.relationship('User', backref=db.backref('favorite_recipes', lazy='dynamic'))
    recipe = db.relationship('Recipe', backref=db.backref('favorited_by', lazy='dynamic'))

    def to_dict(self):
        return {
            'user_id': self.user_id,
            'user_name': self.user.name,
            'recipe_id': self.recipe_id,
            'recipe_name': self.recipe.name,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }


class UserSearchHistory(db.Model):
    """用户搜索历史表"""
    __tablename__ = 'user_search_history'

    id = db.Column(db.Integer, primary_key=1)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=0)
    search_query = db.Column(db.Text, nullable=0)
    search_filters = db.Column(db.Text)  # JSON格式存储筛选条件
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0), nullable=0)

    # 关系
    user = db.relationship('User', backref=db.backref('search_history', lazy='dynamic'))

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user_name': self.user.name,
            'search_query': self.search_query,
            'search_filters': json.loads(self.search_filters) if self.search_filters else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
