"""
修复purchase_orders表结构
创建缺少的表
"""

from app import create_app, db
from sqlalchemy import text

def fix_purchase_orders_table():
    """创建purchase_orders表"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否存在
            result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_orders'"))
            if result.scalar() > 0:
                print("purchase_orders表已存在，无需创建")
                return
            
            # 创建purchase_orders表
            create_table_sql = """
            CREATE TABLE purchase_orders (
                id INT IDENTITY(1,1) PRIMARY KEY,
                order_number NVARCHAR(50) NOT NULL,
                supplier_id INT NOT NULL,
                requisition_id INT NULL,
                area_id INT NOT NULL,
                total_amount DECIMAL(12, 2) NOT NULL,
                order_date DATETIME2 NOT NULL,
                expected_delivery_date DATE NULL,
                payment_terms NVARCHAR(200) NULL,
                delivery_terms NVARCHAR(200) NULL,
                status NVARCHAR(20) NOT NULL,
                delivery_date DATETIME2 NULL,
                created_by INT NOT NULL,
                approved_by INT NULL,
                created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
                updated_at DATETIME2 NULL
            )
            """
            db.session.execute(text(create_table_sql))
            
            # 创建purchase_order_items表
            result = db.session.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'purchase_order_items'"))
            if result.scalar() == 0:
                create_items_table_sql = """
                CREATE TABLE purchase_order_items (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    order_id INT NOT NULL,
                    product_id INT NOT NULL,
                    ingredient_id INT NOT NULL,
                    quantity FLOAT NOT NULL,
                    unit NVARCHAR(20) NOT NULL,
                    unit_price DECIMAL(10, 2) NOT NULL,
                    total_price DECIMAL(10, 2) NOT NULL,
                    received_quantity FLOAT NULL DEFAULT 0,
                    notes NVARCHAR(MAX) NULL,
                    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
                    updated_at DATETIME2 NULL
                )
                """
                db.session.execute(text(create_items_table_sql))
                print("purchase_order_items表已创建")
            
            # 提交事务
            db.session.commit()
            print("purchase_orders表已创建")
            
        except Exception as e:
            db.session.rollback()
            print(f"创建purchase_orders表时出错: {str(e)}")

if __name__ == "__main__":
    fix_purchase_orders_table()
